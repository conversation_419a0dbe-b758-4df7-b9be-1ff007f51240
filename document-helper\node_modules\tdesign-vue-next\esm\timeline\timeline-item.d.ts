declare const _default: import("vue").DefineComponent<{
    index: {
        type: NumberConstructor;
    };
    content: {
        type: import("vue").PropType<import("./type").TdTimelineItemProps["content"]>;
    };
    dot: {
        type: import("vue").PropType<import("./type").TdTimelineItemProps["dot"]>;
    };
    dotColor: {
        type: import("vue").PropType<import("./type").TdTimelineItemProps["dotColor"]>;
        default: string;
    };
    label: {
        type: import("vue").PropType<import("./type").TdTimelineItemProps["label"]>;
    };
    labelAlign: {
        type: import("vue").PropType<import("./type").TdTimelineItemProps["labelAlign"]>;
        validator(val: import("./type").TdTimelineItemProps["labelAlign"]): boolean;
    };
    loading: BooleanConstructor;
    onClick: import("vue").PropType<import("./type").TdTimelineItemProps["onClick"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    index: {
        type: NumberConstructor;
    };
    content: {
        type: import("vue").PropType<import("./type").TdTimelineItemProps["content"]>;
    };
    dot: {
        type: import("vue").PropType<import("./type").TdTimelineItemProps["dot"]>;
    };
    dotColor: {
        type: import("vue").PropType<import("./type").TdTimelineItemProps["dotColor"]>;
        default: string;
    };
    label: {
        type: import("vue").PropType<import("./type").TdTimelineItemProps["label"]>;
    };
    labelAlign: {
        type: import("vue").PropType<import("./type").TdTimelineItemProps["labelAlign"]>;
        validator(val: import("./type").TdTimelineItemProps["labelAlign"]): boolean;
    };
    loading: BooleanConstructor;
    onClick: import("vue").PropType<import("./type").TdTimelineItemProps["onClick"]>;
}>>, {
    loading: boolean;
    dotColor: string;
}, {}>;
export default _default;
