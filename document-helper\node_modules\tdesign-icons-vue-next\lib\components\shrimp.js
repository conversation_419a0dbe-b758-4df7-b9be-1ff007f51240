'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var defineProperty = require('../_chunks/dep-304d0f8c.js');
var vue = require('vue');
var utils_renderFn = require('../utils/render-fn.js');
var utils_useSizeProps = require('../utils/use-size-props.js');
require('../utils/use-common-classname.js');
require('../utils/config-context.js');

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { defineProperty._defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var element = {
  "tag": "svg",
  "attrs": {
    "fill": "none",
    "viewBox": "0 0 24 24",
    "width": "1em",
    "height": "1em"
  },
  "children": [{
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M17.5984 5.99073L14 9.58686V5C15.3244 5 16.5497 5.35851 17.5984 5.99073ZM12 5V11L10 11C7.97723 11 6.21627 10.4087 4.98059 9.38636C3.76404 8.37989 3 6.91311 3 5L12 5ZM14 14.4153L17.5899 18.0074C16.5377 18.635 15.3054 19 14 19V14.4153ZM12 17V19H8C8 17.8954 8.89543 17 10 17H12ZM14 21C16.1908 21 18.2151 20.202 19.7726 18.9051C21.7557 17.254 23 14.7793 23 12C23 9.2351 21.7355 6.74979 19.7816 5.10234C18.2136 3.78037 16.2053 3 14 3H1V5C1 7.50516 2.02682 9.53839 3.7057 10.9274C5.36545 12.3005 7.60449 13 10 13H12V15H10C7.79086 15 6 16.7909 6 19V21H14ZM19.1528 16.7421L15.4131 13L20.9302 13C20.7273 14.4375 20.0919 15.7275 19.1528 16.7421ZM20.9282 11L15.4153 11L19.1524 7.26525C20.0827 8.28201 20.722 9.57491 20.9282 11ZM9.00195 5.99805H6.99805V8.00195H9.00195V5.99805Z"
    }
  }]
};
var shrimp = vue.defineComponent({
  name: "ShrimpIcon",
  props: {
    size: {
      type: String
    },
    onClick: {
      type: Function
    }
  },
  setup(props, _ref) {
    var {
      attrs
    } = _ref;
    var propsSize = vue.computed(() => props.size);
    var {
      className,
      style
    } = utils_useSizeProps['default'](propsSize);
    var finalCls = vue.computed(() => ["t-icon", "t-icon-shrimp", className.value]);
    var finalStyle = vue.computed(() => _objectSpread(_objectSpread({}, style.value), attrs.style));
    var finalProps = vue.computed(() => ({
      class: finalCls.value,
      style: finalStyle.value,
      onClick: e => {
        var _props$onClick;
        return (_props$onClick = props.onClick) === null || _props$onClick === void 0 ? void 0 : _props$onClick.call(props, {
          e
        });
      }
    }));
    return () => utils_renderFn['default'](element, finalProps.value);
  }
});

exports.default = shrimp;
//# sourceMappingURL=shrimp.js.map
