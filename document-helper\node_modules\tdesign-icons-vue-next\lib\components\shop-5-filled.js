'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var defineProperty = require('../_chunks/dep-304d0f8c.js');
var vue = require('vue');
var utils_renderFn = require('../utils/render-fn.js');
var utils_useSizeProps = require('../utils/use-size-props.js');
require('../utils/use-common-classname.js');
require('../utils/config-context.js');

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { defineProperty._defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var element = {
  "tag": "svg",
  "attrs": {
    "fill": "none",
    "viewBox": "0 0 24 24",
    "width": "1em",
    "height": "1em"
  },
  "children": [{
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M20.8888 2C20.8888 2 20.9315 2.05148 21 2.14672 21.2814 2.53779 22 3.66668 22 4.9989 22 5.83805 21.6171 6.59672 21 7.14114 20.3988 7.67151 19.5753 7.99854 18.6669 7.99854 17.9238 7.99854 17.2374 7.77968 16.683 7.40969 15.864 6.86314 15.3331 5.98679 15.3331 4.9989 15.3331 5.9869 14.8023 6.86333 13.9834 7.40987 13.4292 7.77975 12.743 7.99854 12 7.99854 11.2569 7.99854 10.5705 7.77968 10.0161 7.40969 9.19706 6.86314 8.66613 5.98679 8.66613 4.9989 8.66613 5.9869 8.13537 6.86333 7.31646 7.40987 6.76224 7.77975 6.07605 7.99854 5.33306 7.99854 4.42469 7.99854 3.60119 7.67151 3 7.14114 2.38288 6.59672 2 5.83805 2 4.9989 2 3.66668 2.71856 2.53779 3 2.14672 3.06854 2.05148 3.11116 2 3.11116 2H20.8888zM18.6669 9.99854C19.479 9.99854 20.2778 9.81913 21 9.48985V19.9961H22V22H16.5V14H7.5V22H2V19.9961H3V9.48985C3.72221 9.81913 4.52103 9.99854 5.33306 9.99854 6.54353 9.99854 7.72463 9.5999 8.66637 8.9004 9.60839 9.59994 10.7897 9.99854 12 9.99854 13.2105 9.99854 14.3916 9.5999 15.3333 8.9004 16.2753 9.59994 17.4566 9.99854 18.6669 9.99854z"
    }
  }, {
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M9.5 22V16H14.5V22H9.5Z"
    }
  }]
};
var shop5Filled = vue.defineComponent({
  name: "Shop5FilledIcon",
  props: {
    size: {
      type: String
    },
    onClick: {
      type: Function
    }
  },
  setup(props, _ref) {
    var {
      attrs
    } = _ref;
    var propsSize = vue.computed(() => props.size);
    var {
      className,
      style
    } = utils_useSizeProps['default'](propsSize);
    var finalCls = vue.computed(() => ["t-icon", "t-icon-shop-5-filled", className.value]);
    var finalStyle = vue.computed(() => _objectSpread(_objectSpread({}, style.value), attrs.style));
    var finalProps = vue.computed(() => ({
      class: finalCls.value,
      style: finalStyle.value,
      onClick: e => {
        var _props$onClick;
        return (_props$onClick = props.onClick) === null || _props$onClick === void 0 ? void 0 : _props$onClick.call(props, {
          e
        });
      }
    }));
    return () => utils_renderFn['default'](element, finalProps.value);
  }
});

exports.default = shop5Filled;
//# sourceMappingURL=shop-5-filled.js.map
