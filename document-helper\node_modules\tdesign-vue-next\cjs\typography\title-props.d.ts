import { TdTitleProps } from '../typography/type';
import { PropType } from 'vue';
declare const _default: {
    content: {
        type: PropType<TdTitleProps["content"]>;
    };
    default: {
        type: PropType<TdTitleProps["default"]>;
    };
    ellipsis: {
        type: PropType<TdTitleProps["ellipsis"]>;
        default: TdTitleProps["ellipsis"];
    };
    level: {
        type: PropType<TdTitleProps["level"]>;
        default: TdTitleProps["level"];
        validator(val: TdTitleProps["level"]): boolean;
    };
};
export default _default;
