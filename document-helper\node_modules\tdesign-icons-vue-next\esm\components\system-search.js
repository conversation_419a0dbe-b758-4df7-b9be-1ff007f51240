import { _ as _defineProperty } from '../_chunks/dep-931ef437.js';
import { defineComponent, computed } from 'vue';
import renderFn from '../utils/render-fn.js';
import useSizeProps from '../utils/use-size-props.js';
import '../style/css.js';
import '../utils/use-common-classname.js';
import '../utils/config-context.js';

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var element = {
  "tag": "svg",
  "attrs": {
    "fill": "none",
    "viewBox": "0 0 24 24",
    "width": "1em",
    "height": "1em"
  },
  "children": [{
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M1 1H23V12H21V3H3V16H12V18H1V1ZM18.25 15C19.7688 15 21 16.2312 21 17.75C21 18.5084 20.693 19.1951 20.1965 19.6926C20.1936 19.6955 20.1907 19.6984 20.1878 19.7013C19.6907 20.195 19.006 20.5 18.25 20.5C16.7312 20.5 15.5 19.2688 15.5 17.75C15.5 16.2312 16.7312 15 18.25 15ZM22.2419 20.3254C22.7216 19.5834 23 18.6992 23 17.75C23 15.1266 20.8734 13 18.25 13C15.6266 13 13.5 15.1266 13.5 17.75C13.5 20.3734 15.6266 22.5 18.25 22.5C19.2004 22.5 20.0857 22.2209 20.8282 21.7401L22.4983 23.4142L23.9142 22.0017L22.2419 20.3254ZM2.25 21H12V23H2.25V21Z"
    }
  }]
};
var systemSearch = defineComponent({
  name: "SystemSearchIcon",
  props: {
    size: {
      type: String
    },
    onClick: {
      type: Function
    }
  },
  setup(props, _ref) {
    var {
      attrs
    } = _ref;
    var propsSize = computed(() => props.size);
    var {
      className,
      style
    } = useSizeProps(propsSize);
    var finalCls = computed(() => ["t-icon", "t-icon-system-search", className.value]);
    var finalStyle = computed(() => _objectSpread(_objectSpread({}, style.value), attrs.style));
    var finalProps = computed(() => ({
      class: finalCls.value,
      style: finalStyle.value,
      onClick: e => {
        var _props$onClick;
        return (_props$onClick = props.onClick) === null || _props$onClick === void 0 ? void 0 : _props$onClick.call(props, {
          e
        });
      }
    }));
    return () => renderFn(element, finalProps.value);
  }
});

export default systemSearch;
//# sourceMappingURL=system-search.js.map
