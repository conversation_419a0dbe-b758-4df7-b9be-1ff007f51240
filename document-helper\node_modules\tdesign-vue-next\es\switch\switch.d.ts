import { TNodeReturnValue } from '../common';
declare const _default: import("vue").DefineComponent<{
    beforeChange: {
        type: import("vue").PropType<import("./type").TdSwitchProps["beforeChange"]>;
    };
    customValue: {
        type: import("vue").PropType<import("./type").TdSwitchProps["customValue"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    label: {
        type: import("vue").PropType<import("./type").TdSwitchProps["label"]>;
        default: () => import("./type").TdSwitchProps["label"];
    };
    loading: BooleanConstructor;
    size: {
        type: import("vue").PropType<import("./type").TdSwitchProps["size"]>;
        default: import("./type").TdSwitchProps["size"];
        validator(val: import("./type").TdSwitchProps["size"]): boolean;
    };
    value: {
        type: import("vue").PropType<import("./type").TdSwitchProps["value"]>;
        default: import("./type").TdSwitchProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<import("./type").TdSwitchProps["value"]>;
        default: import("./type").TdSwitchProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<import("./type").TdSwitchProps["defaultValue"]>;
    };
    onChange: import("vue").PropType<import("./type").TdSwitchProps["onChange"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    beforeChange: {
        type: import("vue").PropType<import("./type").TdSwitchProps["beforeChange"]>;
    };
    customValue: {
        type: import("vue").PropType<import("./type").TdSwitchProps["customValue"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    label: {
        type: import("vue").PropType<import("./type").TdSwitchProps["label"]>;
        default: () => import("./type").TdSwitchProps["label"];
    };
    loading: BooleanConstructor;
    size: {
        type: import("vue").PropType<import("./type").TdSwitchProps["size"]>;
        default: import("./type").TdSwitchProps["size"];
        validator(val: import("./type").TdSwitchProps["size"]): boolean;
    };
    value: {
        type: import("vue").PropType<import("./type").TdSwitchProps["value"]>;
        default: import("./type").TdSwitchProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<import("./type").TdSwitchProps["value"]>;
        default: import("./type").TdSwitchProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<import("./type").TdSwitchProps["defaultValue"]>;
    };
    onChange: import("vue").PropType<import("./type").TdSwitchProps["onChange"]>;
}>>, {
    value: import("./type").SwitchValue;
    disabled: boolean;
    size: "small" | "medium" | "large";
    loading: boolean;
    label: (string | ((h: typeof import("vue").h) => TNodeReturnValue))[] | ((h: typeof import("vue").h, props: {
        value: import("./type").SwitchValue;
    }) => TNodeReturnValue);
    modelValue: import("./type").SwitchValue;
}, {}>;
export default _default;
