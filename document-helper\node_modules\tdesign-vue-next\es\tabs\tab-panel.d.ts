declare const _default: import("vue").DefineComponent<{
    default: {
        type: import("vue").PropType<import("./type").TdTabPanelProps["default"]>;
    };
    destroyOnHide: {
        type: BooleanConstructor;
        default: boolean;
    };
    disabled: BooleanConstructor;
    draggable: {
        type: BooleanConstructor;
        default: boolean;
    };
    label: {
        type: import("vue").PropType<import("./type").TdTabPanelProps["label"]>;
    };
    lazy: BooleanConstructor;
    panel: {
        type: import("vue").PropType<import("./type").TdTabPanelProps["panel"]>;
    };
    removable: BooleanConstructor;
    value: {
        type: import("vue").PropType<import("./type").TdTabPanelProps["value"]>;
    };
    onRemove: import("vue").PropType<import("./type").TdTabPanelProps["onRemove"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    default: {
        type: import("vue").PropType<import("./type").TdTabPanelProps["default"]>;
    };
    destroyOnHide: {
        type: BooleanConstructor;
        default: boolean;
    };
    disabled: BooleanConstructor;
    draggable: {
        type: BooleanConstructor;
        default: boolean;
    };
    label: {
        type: import("vue").PropType<import("./type").TdTabPanelProps["label"]>;
    };
    lazy: BooleanConstructor;
    panel: {
        type: import("vue").PropType<import("./type").TdTabPanelProps["panel"]>;
    };
    removable: BooleanConstructor;
    value: {
        type: import("vue").PropType<import("./type").TdTabPanelProps["value"]>;
    };
    onRemove: import("vue").PropType<import("./type").TdTabPanelProps["onRemove"]>;
}>>, {
    disabled: boolean;
    draggable: boolean;
    lazy: boolean;
    destroyOnHide: boolean;
    removable: boolean;
}, {}>;
export default _default;
