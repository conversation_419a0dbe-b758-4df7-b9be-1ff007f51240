{"version": 3, "file": "directive.mjs", "sources": ["../../../components/loading/directive.ts"], "sourcesContent": ["import type { Directive, DirectiveBinding } from 'vue';\nimport { mapKeys, isEqual, isObject } from 'lodash-es';\n\nimport { TdLoadingProps } from './type';\nimport produceLoading from './plugin';\n\nconst INSTANCE_KEY = Symbol('TdLoading');\n\nconst createInstance = (el: HTMLElement, binding: DirectiveBinding) => {\n  const { fullscreen, inheritColor } = binding.modifiers;\n  const options: TdLoadingProps = {\n    attach: () => el,\n    fullscreen: fullscreen ?? false,\n    inheritColor: inheritColor ?? false,\n    loading: binding.value,\n  };\n\n  if (isObject(binding.value)) {\n    mapKeys(binding.value, (value, key: keyof typeof options) => {\n      options[key] = value;\n    });\n  }\n\n  // @ts-ignore\n  // TODO: unique symbol' can't be used to index type 'HTMLElement'\n  el[INSTANCE_KEY] = {\n    options,\n    instance: produceLoading(options),\n  };\n};\n\nexport const vLoading: Directive = {\n  mounted(el, binding) {\n    if (binding.value) {\n      createInstance(el, binding);\n    }\n  },\n  updated(el, binding) {\n    const instance = el[INSTANCE_KEY];\n    const { value, oldValue } = binding;\n    if (!isEqual(value, oldValue)) {\n      const loading = value?.loading ?? value;\n      if (loading) {\n        createInstance(el, binding);\n      } else {\n        instance?.instance.hide();\n      }\n    }\n  },\n  unmounted(el) {\n    el[INSTANCE_KEY]?.instance.hide();\n  },\n};\n\nexport default vLoading;\n"], "names": ["INSTANCE_KEY", "Symbol", "createInstance", "el", "binding", "_binding$modifiers", "modifiers", "fullscreen", "inheritColor", "options", "attach", "loading", "value", "isObject", "mapKeys", "key", "instance", "produceLoading", "vLoading", "mounted", "updated", "oldValue", "isEqual", "_value$loading", "hide", "unmounted", "_el$INSTANCE_KEY"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAMA,YAAA,GAAeC,OAAO,WAAW,CAAA,CAAA;AAEvC,IAAMC,cAAA,GAAiB,SAAjBA,cAAAA,CAAkBC,EAAA,EAAiBC,OAA8B,EAAA;AACrE,EAAA,IAAAC,kBAAA,GAAqCD,OAAQ,CAAAE,SAAA;IAArCC,UAAA,GAAAF,kBAAA,CAAAE,UAAA;IAAYC,YAAa,GAAAH,kBAAA,CAAbG,YAAa,CAAA;AACjC,EAAA,IAAMC,OAA0B,GAAA;IAC9BC,QAAQ,SAARA;aAAcP,EAAA,CAAA;AAAA,KAAA;AACdI,IAAAA,YAAYA,UAAc,KAAA,IAAA,IAAdA,UAAc,KAAdA,KAAAA,CAAAA,GAAAA,UAAc,GAAA,KAAA;AAC1BC,IAAAA,cAAcA,YAAgB,KAAA,IAAA,IAAhBA,YAAgB,KAAhBA,KAAAA,CAAAA,GAAAA,YAAgB,GAAA,KAAA;IAC9BG,SAASP,OAAQ,CAAAQ,KAAAA;GACnB,CAAA;AAEI,EAAA,IAAAC,QAAA,CAAST,OAAQ,CAAAQ,KAAK,CAAG,EAAA;IAC3BE,OAAA,CAAQV,OAAQ,CAAAQ,KAAA,EAAO,UAACA,KAAA,EAAOG,GAA8B,EAAA;AAC3DN,MAAAA,OAAA,CAAQM,GAAO,CAAA,GAAAH,KAAA,CAAA;AACjB,KAAC,CAAA,CAAA;AACH,GAAA;EAIAT,EAAA,CAAGH,YAAgB,CAAA,GAAA;AACjBS,IAAAA,OAAA,EAAAA,OAAA;IACAO,QAAA,EAAUC,cAAeR,OAAO,CAAA;GAClC,CAAA;AACF,CAAA,CAAA;AAEO,IAAMS,QAAsB,GAAA;AACjCC,EAAAA,OAAA,WAAAA,OAAAA,CAAQhB,IAAIC,OAAS,EAAA;IACnB,IAAIA,QAAQQ,KAAO,EAAA;AACjBV,MAAAA,cAAA,CAAeC,IAAIC,OAAO,CAAA,CAAA;AAC5B,KAAA;GACF;AACAgB,EAAAA,OAAA,WAAAA,OAAAA,CAAQjB,IAAIC,OAAS,EAAA;AACnB,IAAA,IAAMY,WAAWb,EAAG,CAAAH,YAAA,CAAA,CAAA;AACd,IAAA,IAAEY,KAAO,GAAaR,OAAA,CAApBQ,KAAO;MAAAS,QAAA,GAAajB,OAAA,CAAbiB,QAAA,CAAA;AACf,IAAA,IAAI,CAACC,OAAA,CAAQV,KAAO,EAAAS,QAAQ,CAAG,EAAA;AAAA,MAAA,IAAAE,cAAA,CAAA;AACvB,MAAA,IAAAZ,OAAA,GAAAY,CAAAA,cAAA,GAAUX,kBAAAA,4BAAAA,MAAOD,OAAW,MAAAY,IAAAA,IAAAA,cAAA,KAAAA,KAAAA,CAAAA,GAAAA,cAAA,GAAAX,KAAA,CAAA;AAClC,MAAA,IAAID,OAAS,EAAA;AACXT,QAAAA,cAAA,CAAeC,IAAIC,OAAO,CAAA,CAAA;AAC5B,OAAO,MAAA;QACLY,QAAA,KAAA,IAAA,IAAAA,QAAA,KAAAA,KAAAA,CAAAA,IAAAA,QAAA,CAAUA,SAASQ,IAAK,EAAA,CAAA;AAC1B,OAAA;AACF,KAAA;GACF;AACAC,EAAAA,WAAAA,SAAAA,UAAUtB,EAAI,EAAA;AAAA,IAAA,IAAAuB,gBAAA,CAAA;AACT,IAAA,CAAAA,gBAAA,GAAAvB,EAAA,CAAAH,YAAA,CAAA,MAAA,IAAA,IAAA0B,gBAAA,KAAA,KAAA,CAAA,IAAAA,gBAAA,CAAeV,SAASQ,IAAK,EAAA,CAAA;AAClC,GAAA;AACF;;;;"}