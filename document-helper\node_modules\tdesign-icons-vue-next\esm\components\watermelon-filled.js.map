{"version": 3, "file": "watermelon-filled.js", "sources": ["../../src/components/watermelon-filled.tsx"], "sourcesContent": ["import { computed, PropType, defineComponent } from 'vue';\nimport renderFn from '../utils/render-fn';\nimport {\n  TdIconSVGProps, SVGJson,\n} from '../utils/types';\nimport useSizeProps from '../utils/use-size-props';\n\nimport '../style/css';\n\nconst element: SVGJson = {\"tag\":\"svg\",\"attrs\":{\"fill\":\"none\",\"viewBox\":\"0 0 24 24\",\"width\":\"1em\",\"height\":\"1em\"},\"children\":[{\"tag\":\"path\",\"attrs\":{\"fill\":\"currentColor\",\"d\":\"M17.5006 1.08594L18.2077 1.79304C20.0103 3.59563 20.5457 6.52564 20.5006 8.99865L20.5006 9.00139C20.4936 15.3483 15.3501 20.4923 9.00341 20.5001C6.52295 20.552 3.83978 19.8266 1.88216 18.286L0.99707 17.5895L17.5006 1.08594ZM16.5023 8.49805H14.4984V10.502H16.5023V8.49805ZM13.5023 11.498H11.4984V13.502H13.5023V11.498ZM10.5023 14.498H8.49835V16.502H10.5023V14.498Z\"}},{\"tag\":\"path\",\"attrs\":{\"fill\":\"currentColor\",\"d\":\"M21.4997 9.49986C21.4997 16.1273 16.1271 21.4999 9.49968 21.4999C7.90658 21.4999 6.386 21.1894 4.99512 20.6257C5.29501 20.799 5.60178 20.9644 5.91307 21.12C7.50687 21.9163 9.31045 22.4998 10.9883 22.4998C17.3461 22.4998 22.5001 17.3458 22.5001 10.988C22.5001 9.31017 21.9166 7.50658 21.1203 5.91278C20.964 5.60007 20.7978 5.29192 20.6237 4.99072C21.1886 6.38285 21.4997 7.90502 21.4997 9.49986Z\"}}]};\n\nexport default defineComponent({\n  name: 'WatermelonFilledIcon',\n  props: {\n    size: {\n      type: String,\n    },\n    onClick: {\n      type: Function as PropType<TdIconSVGProps['onClick']>,\n    },\n  },\n  setup(props, { attrs }) {\n    const propsSize = computed(() => props.size);\n\n    const { className, style } = useSizeProps(propsSize);\n\n    const finalCls = computed(() => ['t-icon', 't-icon-watermelon-filled', className.value]);\n    const finalStyle = computed(() => ({ ...style.value, ...(attrs.style as Styles) }));\n    const finalProps = computed(() => ({\n      class: finalCls.value,\n      style: finalStyle.value,\n      onClick: (e:MouseEvent) => props.onClick?.({ e }),\n    }));\n    return () => renderFn(element, finalProps.value);\n  },\n\n});\n"], "names": ["element", "defineComponent", "name", "props", "size", "type", "String", "onClick", "Function", "setup", "attrs", "propsSize", "computed", "className", "style", "useSizeProps", "finalCls", "value", "finalStyle", "_objectSpread", "finalProps", "class", "e", "_props$onClick", "call", "renderFn"], "mappings": ";;;;;;;;;;AASA,IAAMA,UAAmB;EAAC,OAAM;EAAM,SAAQ;IAAC,QAAO;IAAO,WAAU;IAAY,SAAQ;IAAM,UAAS;;EAAO,YAAW,CAAC;IAAC,OAAM;IAAO,SAAQ;MAAC,QAAO;MAAe,KAAI;;KAAgX;IAAC,OAAM;IAAO,SAAQ;MAAC,QAAO;MAAe,KAAI;;;;AAE/kB,uBAAeC,gBAAgB;EAC7BC,MAAM;EACNC,OAAO;IACLC,MAAM;MACJC,MAAMC;;IAERC,SAAS;MACPF,MAAMG;;;EAGVC,MAAMN,aAAkB;IAAA,IAAX;MAAEO;;QACPC,YAAYC,SAAS,MAAMT,MAAMC;QAEjC;MAAES;MAAWC;QAAUC,aAAaJ;QAEpCK,WAAWJ,SAAS,MAAM,CAAC,UAAU,4BAA4BC,UAAUI;QAC3EC,aAAaN,SAAS,MAAAO,aAAA,CAAAA,aAAA,KAAYL,MAAMG,QAAWP,MAAMI;QACzDM,aAAaR,SAAS;MAC1BS,OAAOL,SAASC;MAChBH,OAAOI,WAAWD;MAClBV,SAAUe;;iCAAiBnB,MAAMI,0DAANgB,cAAA,CAAAC,IAAA,CAAArB,OAAgB;UAAEmB;;;;WAExC,MAAMG,SAASzB,SAASoB,WAAWH;;AAAA;;;;"}