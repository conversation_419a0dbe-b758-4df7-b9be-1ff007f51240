import { PropType } from 'vue';
import { TdBaseTableProps } from '../type';
import { RowAndColFixedPosition } from '../types';
export interface TFootProps {
    rowKey: string;
    isFixedHeader: boolean;
    rowAndColFixedPosition: RowAndColFixedPosition;
    footData: TdBaseTableProps['footData'];
    columns: TdBaseTableProps['columns'];
    rowAttributes: TdBaseTableProps['rowAttributes'];
    rowClassName: TdBaseTableProps['rowClassName'];
    thWidthList?: {
        [colKey: string]: number;
    };
    footerSummary?: TdBaseTableProps['footerSummary'];
    rowspanAndColspanInFooter: TdBaseTableProps['rowspanAndColspanInFooter'];
    virtualScroll?: boolean;
}
declare const _default: import("vue").DefineComponent<{
    rowKey: StringConstructor;
    isFixedHeader: BooleanConstructor;
    rowAndColFixedPosition: PropType<TFootProps["rowAndColFixedPosition"]>;
    footData: PropType<TFootProps["footData"]>;
    columns: PropType<TFootProps["columns"]>;
    rowAttributes: PropType<TFootProps["rowAttributes"]>;
    rowClassName: PropType<TFootProps["rowClassName"]>;
    thWidthList: PropType<TFootProps["thWidthList"]>;
    footerSummary: PropType<TFootProps["footerSummary"]>;
    rowspanAndColspanInFooter: PropType<TFootProps["rowspanAndColspanInFooter"]>;
    virtualScroll: BooleanConstructor;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, import("vue").EmitsOptions, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    rowKey: StringConstructor;
    isFixedHeader: BooleanConstructor;
    rowAndColFixedPosition: PropType<TFootProps["rowAndColFixedPosition"]>;
    footData: PropType<TFootProps["footData"]>;
    columns: PropType<TFootProps["columns"]>;
    rowAttributes: PropType<TFootProps["rowAttributes"]>;
    rowClassName: PropType<TFootProps["rowClassName"]>;
    thWidthList: PropType<TFootProps["thWidthList"]>;
    footerSummary: PropType<TFootProps["footerSummary"]>;
    rowspanAndColspanInFooter: PropType<TFootProps["rowspanAndColspanInFooter"]>;
    virtualScroll: BooleanConstructor;
}>>, {
    isFixedHeader: boolean;
    virtualScroll: boolean;
}, {}>;
export default _default;
