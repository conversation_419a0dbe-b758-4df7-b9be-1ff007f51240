{"version": 3, "file": "work-off.js", "sources": ["../../src/components/work-off.tsx"], "sourcesContent": ["import { computed, PropType, defineComponent } from 'vue';\nimport renderFn from '../utils/render-fn';\nimport {\n  TdIconSVGProps, SVGJson,\n} from '../utils/types';\nimport useSizeProps from '../utils/use-size-props';\n\nimport '../style/css';\n\nconst element: SVGJson = {\"tag\":\"svg\",\"attrs\":{\"fill\":\"none\",\"viewBox\":\"0 0 24 24\",\"width\":\"1em\",\"height\":\"1em\"},\"children\":[{\"tag\":\"g\",\"attrs\":{\"clipPath\":\"url(#clip0_8726_7418)\"},\"children\":[{\"tag\":\"path\",\"attrs\":{\"fill\":\"currentColor\",\"d\":\"M2.00015 0.585693L23.4144 21.9999L22.0002 23.4141L20.0859 21.4999H2.00015V6.49991H5.08594L0.585938 1.99991L2.00015 0.585693ZM7.08594 8.49991H4.00015V19.4999H18.0859L7.08594 8.49991ZM7.50581 2.49991H16.5002V6.50186L22.0001 6.5006L22.0035 17.9185L20.0001 15.9151V8.50116L12.5869 8.50186L10.5835 6.49844L14.5002 6.50186V4.49991H9.50017L9.5002 6.12211L7.49685 4.07781L7.50581 2.49991Z\"}}]}]};\n\nexport default defineComponent({\n  name: 'WorkOffIcon',\n  props: {\n    size: {\n      type: String,\n    },\n    onClick: {\n      type: Function as PropType<TdIconSVGProps['onClick']>,\n    },\n  },\n  setup(props, { attrs }) {\n    const propsSize = computed(() => props.size);\n\n    const { className, style } = useSizeProps(propsSize);\n\n    const finalCls = computed(() => ['t-icon', 't-icon-work-off', className.value]);\n    const finalStyle = computed(() => ({ ...style.value, ...(attrs.style as Styles) }));\n    const finalProps = computed(() => ({\n      class: finalCls.value,\n      style: finalStyle.value,\n      onClick: (e:MouseEvent) => props.onClick?.({ e }),\n    }));\n    return () => renderFn(element, finalProps.value);\n  },\n\n});\n"], "names": ["element", "defineComponent", "name", "props", "size", "type", "String", "onClick", "Function", "setup", "attrs", "propsSize", "computed", "className", "style", "useSizeProps", "finalCls", "value", "finalStyle", "_objectSpread", "finalProps", "class", "e", "_props$onClick", "call", "renderFn"], "mappings": ";;;;;;;;;;AASA,IAAMA,UAAmB;EAAC,OAAM;EAAM,SAAQ;IAAC,QAAO;IAAO,WAAU;IAAY,SAAQ;IAAM,UAAS;;EAAO,YAAW,CAAC;IAAC,OAAM;IAAI,SAAQ;MAAC,YAAW;;IAAyB,YAAW,CAAC;MAAC,OAAM;MAAO,SAAQ;QAAC,QAAO;QAAe,KAAI;;;;;AAElP,cAAeC,gBAAgB;EAC7BC,MAAM;EACNC,OAAO;IACLC,MAAM;MACJC,MAAMC;;IAERC,SAAS;MACPF,MAAMG;;;EAGVC,MAAMN,aAAkB;IAAA,IAAX;MAAEO;;QACPC,YAAYC,SAAS,MAAMT,MAAMC;QAEjC;MAAES;MAAWC;QAAUC,aAAaJ;QAEpCK,WAAWJ,SAAS,MAAM,CAAC,UAAU,mBAAmBC,UAAUI;QAClEC,aAAaN,SAAS,MAAAO,aAAA,CAAAA,aAAA,KAAYL,MAAMG,QAAWP,MAAMI;QACzDM,aAAaR,SAAS;MAC1BS,OAAOL,SAASC;MAChBH,OAAOI,WAAWD;MAClBV,SAAUe;;iCAAiBnB,MAAMI,0DAANgB,cAAA,CAAAC,IAAA,CAAArB,OAAgB;UAAEmB;;;;WAExC,MAAMG,SAASzB,SAASoB,WAAWH;;AAAA;;;;"}