declare const _default: import("vue").DefineComponent<{
    align: {
        type: import("vue").PropType<import("./type").TdRowProps["align"]>;
        default: import("./type").TdRowProps["align"];
        validator(val: import("./type").TdRowProps["align"]): boolean;
    };
    gutter: {
        type: import("vue").PropType<import("./type").TdRowProps["gutter"]>;
        default: import("./type").TdRowProps["gutter"];
    };
    justify: {
        type: import("vue").PropType<import("./type").TdRowProps["justify"]>;
        default: import("./type").TdRowProps["justify"];
        validator(val: import("./type").TdRowProps["justify"]): boolean;
    };
    tag: {
        type: StringConstructor;
        default: string;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    align: {
        type: import("vue").PropType<import("./type").TdRowProps["align"]>;
        default: import("./type").TdRowProps["align"];
        validator(val: import("./type").TdRowProps["align"]): boolean;
    };
    gutter: {
        type: import("vue").PropType<import("./type").TdRowProps["gutter"]>;
        default: import("./type").TdRowProps["gutter"];
    };
    justify: {
        type: import("vue").PropType<import("./type").TdRowProps["justify"]>;
        default: import("./type").TdRowProps["justify"];
        validator(val: import("./type").TdRowProps["justify"]): boolean;
    };
    tag: {
        type: StringConstructor;
        default: string;
    };
}>>, {
    align: "start" | "end" | "center" | "top" | "middle" | "bottom" | "stretch" | "baseline";
    tag: string;
    justify: "start" | "end" | "center" | "space-around" | "space-between";
    gutter: number | import("./type").GutterObject | (number | import("./type").GutterObject)[];
}, {}>;
export default _default;
