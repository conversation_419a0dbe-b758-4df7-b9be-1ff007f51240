import { PropType } from 'vue';
import { TdSelectInputProps } from './type';
import type { SelectInputValueDisplayOptions } from './hooks';
declare const _default: import("vue").DefineComponent<{
    valueDisplayOptions: {
        type: PropType<SelectInputValueDisplayOptions>;
    };
    allowInput: BooleanConstructor;
    autoWidth: BooleanConstructor;
    autofocus: BooleanConstructor;
    borderless: BooleanConstructor;
    clearable: BooleanConstructor;
    collapsedItems: {
        type: PropType<TdSelectInputProps["collapsedItems"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    inputProps: {
        type: PropType<TdSelectInputProps["inputProps"]>;
    };
    inputValue: {
        type: PropType<TdSelectInputProps["inputValue"]>;
        default: TdSelectInputProps["inputValue"];
    };
    defaultInputValue: {
        type: PropType<TdSelectInputProps["defaultInputValue"]>;
    };
    keys: {
        type: PropType<TdSelectInputProps["keys"]>;
    };
    label: {
        type: PropType<TdSelectInputProps["label"]>;
    };
    loading: BooleanConstructor;
    minCollapsedNum: {
        type: NumberConstructor;
        default: number;
    };
    multiple: BooleanConstructor;
    panel: {
        type: PropType<TdSelectInputProps["panel"]>;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    popupProps: {
        type: PropType<TdSelectInputProps["popupProps"]>;
    };
    popupVisible: {
        type: BooleanConstructor;
        default: any;
    };
    defaultPopupVisible: BooleanConstructor;
    prefixIcon: {
        type: PropType<TdSelectInputProps["prefixIcon"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    reserveKeyword: BooleanConstructor;
    size: {
        type: PropType<TdSelectInputProps["size"]>;
        default: TdSelectInputProps["size"];
        validator(val: TdSelectInputProps["size"]): boolean;
    };
    status: {
        type: PropType<TdSelectInputProps["status"]>;
        default: TdSelectInputProps["status"];
        validator(val: TdSelectInputProps["status"]): boolean;
    };
    suffix: {
        type: PropType<TdSelectInputProps["suffix"]>;
    };
    suffixIcon: {
        type: PropType<TdSelectInputProps["suffixIcon"]>;
    };
    tag: {
        type: PropType<TdSelectInputProps["tag"]>;
    };
    tagInputProps: {
        type: PropType<TdSelectInputProps["tagInputProps"]>;
    };
    tagProps: {
        type: PropType<TdSelectInputProps["tagProps"]>;
    };
    tips: {
        type: PropType<TdSelectInputProps["tips"]>;
    };
    value: {
        type: PropType<TdSelectInputProps["value"]>;
        default: TdSelectInputProps["value"];
    };
    valueDisplay: {
        type: PropType<TdSelectInputProps["valueDisplay"]>;
    };
    onBlur: PropType<TdSelectInputProps["onBlur"]>;
    onClear: PropType<TdSelectInputProps["onClear"]>;
    onEnter: PropType<TdSelectInputProps["onEnter"]>;
    onFocus: PropType<TdSelectInputProps["onFocus"]>;
    onInputChange: PropType<TdSelectInputProps["onInputChange"]>;
    onMouseenter: PropType<TdSelectInputProps["onMouseenter"]>;
    onMouseleave: PropType<TdSelectInputProps["onMouseleave"]>;
    onPaste: PropType<TdSelectInputProps["onPaste"]>;
    onPopupVisibleChange: PropType<TdSelectInputProps["onPopupVisibleChange"]>;
    onTagChange: PropType<TdSelectInputProps["onTagChange"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, import("vue").EmitsOptions, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    valueDisplayOptions: {
        type: PropType<SelectInputValueDisplayOptions>;
    };
    allowInput: BooleanConstructor;
    autoWidth: BooleanConstructor;
    autofocus: BooleanConstructor;
    borderless: BooleanConstructor;
    clearable: BooleanConstructor;
    collapsedItems: {
        type: PropType<TdSelectInputProps["collapsedItems"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    inputProps: {
        type: PropType<TdSelectInputProps["inputProps"]>;
    };
    inputValue: {
        type: PropType<TdSelectInputProps["inputValue"]>;
        default: TdSelectInputProps["inputValue"];
    };
    defaultInputValue: {
        type: PropType<TdSelectInputProps["defaultInputValue"]>;
    };
    keys: {
        type: PropType<TdSelectInputProps["keys"]>;
    };
    label: {
        type: PropType<TdSelectInputProps["label"]>;
    };
    loading: BooleanConstructor;
    minCollapsedNum: {
        type: NumberConstructor;
        default: number;
    };
    multiple: BooleanConstructor;
    panel: {
        type: PropType<TdSelectInputProps["panel"]>;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    popupProps: {
        type: PropType<TdSelectInputProps["popupProps"]>;
    };
    popupVisible: {
        type: BooleanConstructor;
        default: any;
    };
    defaultPopupVisible: BooleanConstructor;
    prefixIcon: {
        type: PropType<TdSelectInputProps["prefixIcon"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    reserveKeyword: BooleanConstructor;
    size: {
        type: PropType<TdSelectInputProps["size"]>;
        default: TdSelectInputProps["size"];
        validator(val: TdSelectInputProps["size"]): boolean;
    };
    status: {
        type: PropType<TdSelectInputProps["status"]>;
        default: TdSelectInputProps["status"];
        validator(val: TdSelectInputProps["status"]): boolean;
    };
    suffix: {
        type: PropType<TdSelectInputProps["suffix"]>;
    };
    suffixIcon: {
        type: PropType<TdSelectInputProps["suffixIcon"]>;
    };
    tag: {
        type: PropType<TdSelectInputProps["tag"]>;
    };
    tagInputProps: {
        type: PropType<TdSelectInputProps["tagInputProps"]>;
    };
    tagProps: {
        type: PropType<TdSelectInputProps["tagProps"]>;
    };
    tips: {
        type: PropType<TdSelectInputProps["tips"]>;
    };
    value: {
        type: PropType<TdSelectInputProps["value"]>;
        default: TdSelectInputProps["value"];
    };
    valueDisplay: {
        type: PropType<TdSelectInputProps["valueDisplay"]>;
    };
    onBlur: PropType<TdSelectInputProps["onBlur"]>;
    onClear: PropType<TdSelectInputProps["onClear"]>;
    onEnter: PropType<TdSelectInputProps["onEnter"]>;
    onFocus: PropType<TdSelectInputProps["onFocus"]>;
    onInputChange: PropType<TdSelectInputProps["onInputChange"]>;
    onMouseenter: PropType<TdSelectInputProps["onMouseenter"]>;
    onMouseleave: PropType<TdSelectInputProps["onMouseleave"]>;
    onPaste: PropType<TdSelectInputProps["onPaste"]>;
    onPopupVisibleChange: PropType<TdSelectInputProps["onPopupVisibleChange"]>;
    onTagChange: PropType<TdSelectInputProps["onTagChange"]>;
}>>, {
    value: string | number | boolean | Object | any[] | Date | import("./type").SelectInputValue[];
    disabled: boolean;
    multiple: boolean;
    size: import("..").SizeEnum;
    status: "error" | "default" | "success" | "warning";
    loading: boolean;
    placeholder: string;
    readonly: boolean;
    autoWidth: boolean;
    autofocus: boolean;
    borderless: boolean;
    clearable: boolean;
    inputValue: string;
    minCollapsedNum: number;
    allowInput: boolean;
    popupVisible: boolean;
    defaultPopupVisible: boolean;
    reserveKeyword: boolean;
}, {}>;
export default _default;
