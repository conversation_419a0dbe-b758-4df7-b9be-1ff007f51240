{"version": 3, "file": "getTargetElm.js", "sources": ["../../../../components/guide/utils/getTargetElm.ts"], "sourcesContent": ["import { isString, isFunction } from 'lodash-es';\n\nimport { AttachNode } from '../../common';\n\nexport function getTargetElm(elm: AttachNode): HTMLElement {\n  if (elm) {\n    let targetElement: HTMLElement = null;\n    if (isString(elm)) {\n      targetElement = document.querySelector(elm);\n    } else if (isFunction(elm)) {\n      targetElement = elm() as HTMLElement;\n    } else {\n      throw new Error('elm should be string or function');\n    }\n    if (targetElement) {\n      return targetElement as HTMLElement;\n    }\n    if (process?.env?.NODE_ENV !== 'test') {\n      throw new Error('There is no element with given.');\n    }\n  } else {\n    return document.body;\n  }\n}\n"], "names": ["getTargetElm", "elm", "_process", "targetElement", "isString", "document", "querySelector", "isFunction", "Error", "process", "env", "NODE_ENV", "body"], "mappings": ";;;;;;;;AAIO,SAASA,aAAaC,GAA8B,EAAA;AACzD,EAAA,IAAIA,GAAK,EAAA;AAAA,IAAA,IAAAC,QAAA,CAAA;IACP,IAAIC,aAA6B,GAAA,IAAA,CAAA;AAC7B,IAAA,IAAAC,QAAA,CAASH,GAAG,CAAG,EAAA;AACDE,MAAAA,aAAA,GAAAE,QAAA,CAASC,cAAcL,GAAG,CAAA,CAAA;AAC5C,KAAA,MAAA,IAAWM,UAAW,CAAAN,GAAG,CAAG,EAAA;MAC1BE,aAAA,GAAgBF,GAAI,EAAA,CAAA;AACtB,KAAO,MAAA;AACC,MAAA,MAAA,IAAIO,MAAM,kCAAkC,CAAA,CAAA;AACpD,KAAA;AACA,IAAA,IAAIL,aAAe,EAAA;AACV,MAAA,OAAAA,aAAA,CAAA;AACT,KAAA;IACI,IAAA,CAAA,CAAAD,QAAA,GAAAO,OAAA,cAAAP,QAAA,KAAA,KAAA,CAAA,IAAA,CAAAA,QAAA,GAAAA,QAAA,CAASQ,GAAK,MAAA,IAAA,IAAAR,QAAA,KAAdA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAA,CAAcS,QAAA,MAAa,MAAQ,EAAA;AAC/B,MAAA,MAAA,IAAIH,MAAM,iCAAiC,CAAA,CAAA;AACnD,KAAA;AACF,GAAO,MAAA;IACL,OAAOH,QAAS,CAAAO,IAAA,CAAA;AAClB,GAAA;AACF;;;;"}