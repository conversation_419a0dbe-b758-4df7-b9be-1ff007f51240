declare const _default: import("vue").DefineComponent<{
    allowUncheck: BooleanConstructor;
    checked: {
        type: BooleanConstructor;
        default: any;
    };
    modelValue: {
        type: BooleanConstructor;
        default: any;
    };
    defaultChecked: BooleanConstructor;
    default: {
        type: import("vue").PropType<import("./type").TdRadioProps["default"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    label: {
        type: import("vue").PropType<import("./type").TdRadioProps["label"]>;
    };
    name: {
        type: StringConstructor;
        default: string;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    value: {
        type: import("vue").PropType<import("./type").TdRadioProps["value"]>;
        default: import("./type").TdRadioProps["value"];
    };
    onChange: import("vue").PropType<import("./type").TdRadioProps["onChange"]>;
    onClick: import("vue").PropType<import("./type").TdRadioProps["onClick"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    allowUncheck: BooleanConstructor;
    checked: {
        type: BooleanConstructor;
        default: any;
    };
    modelValue: {
        type: BooleanConstructor;
        default: any;
    };
    defaultChecked: BooleanConstructor;
    default: {
        type: import("vue").PropType<import("./type").TdRadioProps["default"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    label: {
        type: import("vue").PropType<import("./type").TdRadioProps["label"]>;
    };
    name: {
        type: StringConstructor;
        default: string;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    value: {
        type: import("vue").PropType<import("./type").TdRadioProps["value"]>;
        default: import("./type").TdRadioProps["value"];
    };
    onChange: import("vue").PropType<import("./type").TdRadioProps["onChange"]>;
    onClick: import("vue").PropType<import("./type").TdRadioProps["onClick"]>;
}>>, {
    name: string;
    value: import("./type").RadioValue;
    disabled: boolean;
    checked: boolean;
    defaultChecked: boolean;
    allowUncheck: boolean;
    modelValue: boolean;
    readonly: boolean;
}, {}>;
export default _default;
