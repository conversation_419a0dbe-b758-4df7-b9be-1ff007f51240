import { PropType } from 'vue';
import { Color } from '../../utils';
declare const _default: import("vue").DefineComponent<{
    colors: {
        type: PropType<string[]>;
        default: () => PropType<string[]>;
    };
    title: {
        type: StringConstructor;
        default: string;
    };
    editable: {
        type: BooleanConstructor;
        default: boolean;
    };
    onSetColor: {
        type: FunctionConstructor;
        default: () => () => void;
    };
    handleAddColor: {
        type: FunctionConstructor;
        default: () => () => void;
    };
    disabled: BooleanConstructor;
    color: {
        type: PropType<Color>;
    };
    onChange: {
        type: FunctionConstructor;
        default: () => () => void;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    colors: {
        type: PropType<string[]>;
        default: () => PropType<string[]>;
    };
    title: {
        type: StringConstructor;
        default: string;
    };
    editable: {
        type: BooleanConstructor;
        default: boolean;
    };
    onSetColor: {
        type: FunctionConstructor;
        default: () => () => void;
    };
    handleAddColor: {
        type: FunctionConstructor;
        default: () => () => void;
    };
    disabled: BooleanConstructor;
    color: {
        type: PropType<Color>;
    };
    onChange: {
        type: FunctionConstructor;
        default: () => () => void;
    };
}>>, {
    colors: string[];
    disabled: boolean;
    title: string;
    onChange: Function;
    editable: boolean;
    onSetColor: Function;
    handleAddColor: Function;
}, {}>;
export default _default;
