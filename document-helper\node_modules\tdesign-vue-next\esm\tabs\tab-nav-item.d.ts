import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<{
    index: NumberConstructor;
    active: {
        type: BooleanConstructor;
    };
    theme: {
        type: PropType<import("./type").TdTabsProps["theme"]>;
        default: import("./type").TdTabsProps["theme"];
        validator(val: import("./type").TdTabsProps["theme"]): boolean;
    };
    size: {
        type: PropType<import("./type").TdTabsProps["size"]>;
        default: import("./type").TdTabsProps["size"];
        validator(val: import("./type").TdTabsProps["size"]): boolean;
    };
    placement: {
        type: PropType<import("./type").TdTabsProps["placement"]>;
        default: import("./type").TdTabsProps["placement"];
        validator(val: import("./type").TdTabsProps["placement"]): boolean;
    };
    label: {
        type: any;
    };
    disabled: BooleanConstructor;
    removable: BooleanConstructor;
    value: {
        type: PropType<import("./type").TdTabPanelProps["value"]>;
    };
    onClick: PropType<Function>;
    onRemove: PropType<Function>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    index: NumberConstructor;
    active: {
        type: BooleanConstructor;
    };
    theme: {
        type: PropType<import("./type").TdTabsProps["theme"]>;
        default: import("./type").TdTabsProps["theme"];
        validator(val: import("./type").TdTabsProps["theme"]): boolean;
    };
    size: {
        type: PropType<import("./type").TdTabsProps["size"]>;
        default: import("./type").TdTabsProps["size"];
        validator(val: import("./type").TdTabsProps["size"]): boolean;
    };
    placement: {
        type: PropType<import("./type").TdTabsProps["placement"]>;
        default: import("./type").TdTabsProps["placement"];
        validator(val: import("./type").TdTabsProps["placement"]): boolean;
    };
    label: {
        type: any;
    };
    disabled: BooleanConstructor;
    removable: BooleanConstructor;
    value: {
        type: PropType<import("./type").TdTabPanelProps["value"]>;
    };
    onClick: PropType<Function>;
    onRemove: PropType<Function>;
}>>, {
    disabled: boolean;
    size: "medium" | "large";
    active: boolean;
    label: any;
    theme: "normal" | "card";
    placement: "left" | "right" | "top" | "bottom";
    removable: boolean;
}, {}>;
export default _default;
