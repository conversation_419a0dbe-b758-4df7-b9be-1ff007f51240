import type { TextareaValue, TdTextareaProps } from './type';
declare const _default: import("vue").DefineComponent<{
    allowInputOverMax: BooleanConstructor;
    autofocus: BooleanConstructor;
    autosize: {
        type: import("vue").PropType<TdTextareaProps["autosize"]>;
        default: TdTextareaProps["autosize"];
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    maxcharacter: {
        type: NumberConstructor;
    };
    maxlength: {
        type: import("vue").PropType<TdTextareaProps["maxlength"]>;
    };
    name: {
        type: StringConstructor;
        default: string;
    };
    placeholder: {
        type: StringConstructor;
        default: any;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    status: {
        type: import("vue").PropType<TdTextareaProps["status"]>;
        default: TdTextareaProps["status"];
        validator(val: TdTextareaProps["status"]): boolean;
    };
    tips: {
        type: import("vue").PropType<TdTextareaProps["tips"]>;
    };
    value: {
        type: import("vue").PropType<TdTextareaProps["value"]>;
        default: TdTextareaProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<TdTextareaProps["value"]>;
        default: TdTextareaProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<TdTextareaProps["defaultValue"]>;
    };
    onBlur: import("vue").PropType<TdTextareaProps["onBlur"]>;
    onChange: import("vue").PropType<TdTextareaProps["onChange"]>;
    onFocus: import("vue").PropType<TdTextareaProps["onFocus"]>;
    onKeydown: import("vue").PropType<TdTextareaProps["onKeydown"]>;
    onKeypress: import("vue").PropType<TdTextareaProps["onKeypress"]>;
    onKeyup: import("vue").PropType<TdTextareaProps["onKeyup"]>;
    onValidate: import("vue").PropType<TdTextareaProps["onValidate"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    allowInputOverMax: BooleanConstructor;
    autofocus: BooleanConstructor;
    autosize: {
        type: import("vue").PropType<TdTextareaProps["autosize"]>;
        default: TdTextareaProps["autosize"];
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    maxcharacter: {
        type: NumberConstructor;
    };
    maxlength: {
        type: import("vue").PropType<TdTextareaProps["maxlength"]>;
    };
    name: {
        type: StringConstructor;
        default: string;
    };
    placeholder: {
        type: StringConstructor;
        default: any;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    status: {
        type: import("vue").PropType<TdTextareaProps["status"]>;
        default: TdTextareaProps["status"];
        validator(val: TdTextareaProps["status"]): boolean;
    };
    tips: {
        type: import("vue").PropType<TdTextareaProps["tips"]>;
    };
    value: {
        type: import("vue").PropType<TdTextareaProps["value"]>;
        default: TdTextareaProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<TdTextareaProps["value"]>;
        default: TdTextareaProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<TdTextareaProps["defaultValue"]>;
    };
    onBlur: import("vue").PropType<TdTextareaProps["onBlur"]>;
    onChange: import("vue").PropType<TdTextareaProps["onChange"]>;
    onFocus: import("vue").PropType<TdTextareaProps["onFocus"]>;
    onKeydown: import("vue").PropType<TdTextareaProps["onKeydown"]>;
    onKeypress: import("vue").PropType<TdTextareaProps["onKeypress"]>;
    onKeyup: import("vue").PropType<TdTextareaProps["onKeyup"]>;
    onValidate: import("vue").PropType<TdTextareaProps["onValidate"]>;
}>>, {
    name: string;
    value: TextareaValue;
    disabled: boolean;
    status: "error" | "default" | "success" | "warning";
    placeholder: string;
    modelValue: TextareaValue;
    readonly: boolean;
    allowInputOverMax: boolean;
    autofocus: boolean;
    autosize: boolean | {
        minRows?: number;
        maxRows?: number;
    };
}, {}>;
export default _default;
