declare const _default: import("vue").DefineComponent<{
    delay: {
        type: NumberConstructor;
    };
    destroyOnClose: {
        type: BooleanConstructor;
        default: boolean;
    };
    duration: {
        type: NumberConstructor;
    };
    placement: {
        type: import("vue").PropType<import("./type").TdTooltipProps["placement"]>;
        default: string;
    };
    showArrow: {
        type: BooleanConstructor;
        default: boolean;
    };
    theme: {
        type: import("vue").PropType<import("./type").TdTooltipProps["theme"]>;
        default: import("./type").TdTooltipProps["theme"];
        validator(val: import("./type").TdTooltipProps["theme"]): boolean;
    };
    attach: {
        type: import("vue").PropType<import("..").TdPopupProps["attach"]>;
        default: import("..").TdPopupProps["attach"];
    };
    content: {
        type: import("vue").PropType<import("..").TdPopupProps["content"]>;
    };
    default: {
        type: import("vue").PropType<import("..").TdPopupProps["default"]>;
    };
    disabled: BooleanConstructor;
    hideEmptyPopup: BooleanConstructor;
    overlayClassName: {
        type: import("vue").PropType<import("..").TdPopupProps["overlayClassName"]>;
    };
    overlayInnerClassName: {
        type: import("vue").PropType<import("..").TdPopupProps["overlayInnerClassName"]>;
    };
    overlayInnerStyle: {
        type: import("vue").PropType<import("..").TdPopupProps["overlayInnerStyle"]>;
    };
    overlayStyle: {
        type: import("vue").PropType<import("..").TdPopupProps["overlayStyle"]>;
    };
    popperOptions: {
        type: import("vue").PropType<import("..").TdPopupProps["popperOptions"]>;
    };
    trigger: {
        type: import("vue").PropType<import("..").TdPopupProps["trigger"]>;
        default: import("..").TdPopupProps["trigger"];
        validator(val: import("..").TdPopupProps["trigger"]): boolean;
    };
    triggerElement: {
        type: import("vue").PropType<import("..").TdPopupProps["triggerElement"]>;
    };
    visible: {
        type: BooleanConstructor;
        default: any;
    };
    modelValue: {
        type: BooleanConstructor;
        default: any;
    };
    defaultVisible: BooleanConstructor;
    zIndex: {
        type: NumberConstructor;
    };
    onOverlayClick: import("vue").PropType<import("..").TdPopupProps["onOverlayClick"]>;
    onScroll: import("vue").PropType<import("..").TdPopupProps["onScroll"]>;
    onScrollToBottom: import("vue").PropType<import("..").TdPopupProps["onScrollToBottom"]>;
    onVisibleChange: import("vue").PropType<import("..").TdPopupProps["onVisibleChange"]>;
}, () => any, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    delay: {
        type: NumberConstructor;
    };
    destroyOnClose: {
        type: BooleanConstructor;
        default: boolean;
    };
    duration: {
        type: NumberConstructor;
    };
    placement: {
        type: import("vue").PropType<import("./type").TdTooltipProps["placement"]>;
        default: string;
    };
    showArrow: {
        type: BooleanConstructor;
        default: boolean;
    };
    theme: {
        type: import("vue").PropType<import("./type").TdTooltipProps["theme"]>;
        default: import("./type").TdTooltipProps["theme"];
        validator(val: import("./type").TdTooltipProps["theme"]): boolean;
    };
    attach: {
        type: import("vue").PropType<import("..").TdPopupProps["attach"]>;
        default: import("..").TdPopupProps["attach"];
    };
    content: {
        type: import("vue").PropType<import("..").TdPopupProps["content"]>;
    };
    default: {
        type: import("vue").PropType<import("..").TdPopupProps["default"]>;
    };
    disabled: BooleanConstructor;
    hideEmptyPopup: BooleanConstructor;
    overlayClassName: {
        type: import("vue").PropType<import("..").TdPopupProps["overlayClassName"]>;
    };
    overlayInnerClassName: {
        type: import("vue").PropType<import("..").TdPopupProps["overlayInnerClassName"]>;
    };
    overlayInnerStyle: {
        type: import("vue").PropType<import("..").TdPopupProps["overlayInnerStyle"]>;
    };
    overlayStyle: {
        type: import("vue").PropType<import("..").TdPopupProps["overlayStyle"]>;
    };
    popperOptions: {
        type: import("vue").PropType<import("..").TdPopupProps["popperOptions"]>;
    };
    trigger: {
        type: import("vue").PropType<import("..").TdPopupProps["trigger"]>;
        default: import("..").TdPopupProps["trigger"];
        validator(val: import("..").TdPopupProps["trigger"]): boolean;
    };
    triggerElement: {
        type: import("vue").PropType<import("..").TdPopupProps["triggerElement"]>;
    };
    visible: {
        type: BooleanConstructor;
        default: any;
    };
    modelValue: {
        type: BooleanConstructor;
        default: any;
    };
    defaultVisible: BooleanConstructor;
    zIndex: {
        type: NumberConstructor;
    };
    onOverlayClick: import("vue").PropType<import("..").TdPopupProps["onOverlayClick"]>;
    onScroll: import("vue").PropType<import("..").TdPopupProps["onScroll"]>;
    onScrollToBottom: import("vue").PropType<import("..").TdPopupProps["onScrollToBottom"]>;
    onVisibleChange: import("vue").PropType<import("..").TdPopupProps["onVisibleChange"]>;
}>>, {
    disabled: boolean;
    visible: boolean;
    theme: "default" | "primary" | "success" | "warning" | "danger" | "light";
    modelValue: boolean;
    attach: import("..").AttachNode;
    placement: "mouse" | import("..").PopupPlacement;
    trigger: "focus" | "click" | "mousedown" | "hover" | "context-menu";
    destroyOnClose: boolean;
    hideEmptyPopup: boolean;
    showArrow: boolean;
    defaultVisible: boolean;
}, {}>;
export default _default;
