import { TdRowProps } from '../grid/type';
import { PropType } from 'vue';
declare const _default: {
    align: {
        type: PropType<TdRowProps["align"]>;
        default: TdRowProps["align"];
        validator(val: TdRowProps["align"]): boolean;
    };
    gutter: {
        type: PropType<TdRowProps["gutter"]>;
        default: TdRowProps["gutter"];
    };
    justify: {
        type: PropType<TdRowProps["justify"]>;
        default: TdRowProps["justify"];
        validator(val: TdRowProps["justify"]): boolean;
    };
    tag: {
        type: StringConstructor;
        default: string;
    };
};
export default _default;
