declare const _default: import("vue").DefineComponent<{
    disabled: BooleanConstructor;
    color: {
        type: import("vue").PropType<import("@common/js/color-picker").Color>;
    };
    onChange: {
        type: FunctionConstructor;
        default: () => () => void;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    disabled: BooleanConstructor;
    color: {
        type: import("vue").PropType<import("@common/js/color-picker").Color>;
    };
    onChange: {
        type: FunctionConstructor;
        default: () => () => void;
    };
}>>, {
    disabled: boolean;
    onChange: Function;
}, {}>;
export default _default;
