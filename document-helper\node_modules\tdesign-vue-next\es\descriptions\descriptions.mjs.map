{"version": 3, "file": "descriptions.mjs", "sources": ["../../../components/descriptions/descriptions.tsx"], "sourcesContent": ["import { isNil, isArray } from 'lodash-es';\n\nimport { defineComponent, provide, ref } from 'vue';\n\nimport { useTNodeJSX, usePrefixClass, useCommonClassName, useChildComponentSlots } from '@tdesign/shared-hooks';\n\nimport props from './props';\nimport { descriptionsKey } from './consts';\nimport { TdDescriptionsProps } from './type';\nimport DescriptionsRow from './descriptions-row';\nimport { renderCustomNode, itemTypeIsProps } from './utils';\nimport { ItemsType, TdDescriptionsItem } from './types';\n\n/**\n * 实现思路\n * 1. 基于 table tbody tr td 来实现布局\n * 2. 通过 span 计算总共有几行以及每一行的 item 个数，特别注意最后一行，要填充满\n * 3. 整体布局：左右布局（column 和 span 生效）/上下布局（column 和 span 失效，一行一个 item）\n * 4. item 布局：左右布局/上下布局\n */\n\n/**\n * TDescriptions：承载 header（title） 和 body（table, tbody）\n * TDescriptionsRow：承载每一行（tr）\n * TDescriptionsItem：获取 item 数据（span, label, content）\n */\n\nexport default defineComponent({\n  name: 'TDescriptions',\n  props,\n  setup(props: TdDescriptionsProps) {\n    const COMPONENT_NAME = usePrefixClass('descriptions');\n    const { SIZE } = useCommonClassName();\n    const getChildByName = useChildComponentSlots();\n    const renderTNodeJSX = useTNodeJSX();\n    const itemsType = ref<ItemsType>(ItemsType.props);\n\n    // 计算渲染的行内容\n    const getRows = () => {\n      /**\n       * 1. 两种方式获取要渲染的 items\n       *  a. props 传 items\n       *  b. slots t-descriptions-item\n       * a 优先级更高\n       */\n      const { column, layout } = props;\n\n      let items: TdDescriptionsItem[] = [];\n\n      if (isArray(props.items)) {\n        /**\n         * 2.1 a 方式获取 items\n         * ! 这里要支持 label: string / <div></div> / () =>  <div></div>\n         * ! 暂时没有这样一个全局的方法，所以先在组件内部写一个临时方法，无论之后是有了更好的处理方式要删除掉，还是其它组件也需要时再放到公共方法里面，都是可行的\n         */\n        items = props.items.map((item) => ({\n          label: renderCustomNode(item.label),\n          content: renderCustomNode(item.content),\n          span: item.span || 1,\n        }));\n        itemsType.value = ItemsType.props;\n      } else {\n        const slots = getChildByName('TDescriptionsItem');\n        if (slots.length !== 0) {\n          // 2.2 b 方式 获取 TDescriptionsItem\n          items = slots;\n          itemsType.value = ItemsType.slots;\n        }\n      }\n\n      // 2. 判断布局，如果整体布局为 VERTICAL，那么直接返回即可。\n      if (layout === 'vertical') {\n        return [items];\n      }\n\n      // 3. 布局为 HORIZONTAL 时，需要计算每一行的 item 个数\n      let temp: TdDescriptionsItem[] = [];\n      let reset = column;\n\n      // 4. 记录结果\n      const res: TdDescriptionsItem[][] = [];\n      items.forEach((item, index) => {\n        let span = 1;\n        if (itemTypeIsProps(itemsType.value, item)) {\n          span = isNil(item.span) ? span : item.span;\n          // 当 span 大于 column 时，取 column\n          span = span > column ? column : span;\n        } else {\n          item.props = item.props || {};\n          span = isNil(item.props?.span) ? span : item.props.span;\n          span = span > column ? column : span;\n          item.props.span = span;\n        }\n\n        if (reset >= span) {\n          // 当前行还剩余空间\n          temp.push(item);\n          reset -= span;\n        } else {\n          // 当前行放不下了，放下一行\n          res.push(temp);\n          temp = [item];\n          reset = column - span;\n        }\n\n        if (index === items.length - 1) {\n          // 最后一个\n          if (itemTypeIsProps(itemsType.value, item)) {\n            item.span += reset;\n          } else {\n            item.props.span += reset;\n          }\n          res.push(temp);\n        }\n      });\n      return res;\n    };\n\n    provide(descriptionsKey, props);\n\n    const renderBody = () => {\n      const tableClass = [\n        `${COMPONENT_NAME.value}__body`,\n        SIZE.value[props.size],\n        { [`${COMPONENT_NAME.value}__body--fixed`]: props.tableLayout === 'fixed' },\n        { [`${COMPONENT_NAME.value}__body--border`]: props.bordered },\n      ];\n      return (\n        <table class={tableClass}>\n          <tbody>\n            {getRows().map((row) => (\n              <DescriptionsRow item-type={itemsType.value} row={row} />\n            ))}\n          </tbody>\n        </table>\n      );\n    };\n\n    const renderHeader = () => {\n      const title = renderTNodeJSX('title');\n      return title ? <div class={`${COMPONENT_NAME.value}__header`}>{title}</div> : '';\n    };\n\n    return () => (\n      <div class={COMPONENT_NAME.value}>\n        {renderHeader()}\n        {renderBody()}\n      </div>\n    );\n  },\n});\n"], "names": ["defineComponent", "name", "props", "setup", "COMPONENT_NAME", "usePrefixClass", "_useCommonClassName", "useCommonClassName", "SIZE", "getChildByName", "useChildComponentSlots", "renderTNodeJSX", "useTNodeJSX", "itemsType", "ref", "ItemsType", "getRows", "column", "layout", "items", "isArray", "map", "item", "label", "renderCustomNode", "content", "span", "value", "slots", "length", "temp", "reset", "res", "for<PERSON>ach", "index", "itemTypeIsProps", "isNil", "_item$props", "push", "provide", "<PERSON><PERSON><PERSON>", "renderBody", "tableClass", "concat", "size", "_defineProperty", "tableLayout", "bordered", "_createVNode", "row", "DescriptionsRow", "renderHeader", "title"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,oBAAeA,eAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,eAAA;AACNC,EAAAA,KAAA,EAAAA,KAAA;AACAC,EAAAA,OAAAA,SAAAA,MAAMD,MAA4B,EAAA;AAC1B,IAAA,IAAAE,cAAA,GAAiBC,eAAe,cAAc,CAAA,CAAA;AAC9C,IAAA,IAAAC,mBAAA,GAAWC,kBAAmB,EAAA;MAA5BC,IAAK,GAAAF,mBAAA,CAALE,IAAK,CAAA;AACb,IAAA,IAAMC,iBAAiBC,sBAAuB,EAAA,CAAA;AAC9C,IAAA,IAAMC,iBAAiBC,WAAY,EAAA,CAAA;AAC7B,IAAA,IAAAC,SAAA,GAAYC,GAAe,CAAAC,SAAA,CAAUb,KAAK,CAAA,CAAA;AAGhD,IAAA,IAAMc,UAAU,SAAVA,UAAgB;AAOd,MAAA,IAAEC,MAAQ,GAAWf,MAAAA,CAAnBe,MAAQ;QAAAC,MAAA,GAAWhB,MAAAA,CAAXgB,MAAA,CAAA;MAEhB,IAAIC,QAA8B,EAAC,CAAA;AAE/B,MAAA,IAAAC,OAAA,CAAQlB,MAAM,CAAAiB,KAAK,CAAG,EAAA;QAMxBA,KAAA,GAAQjB,MAAM,CAAAiB,KAAA,CAAME,GAAI,CAAA,UAACC,IAAU,EAAA;UAAA,OAAA;AACjCC,YAAAA,KAAA,EAAOC,gBAAiB,CAAAF,IAAA,CAAKC,KAAK,CAAA;AAClCE,YAAAA,OAAA,EAASD,gBAAiB,CAAAF,IAAA,CAAKG,OAAO,CAAA;AACtCC,YAAAA,IAAA,EAAMJ,KAAKI,IAAQ,IAAA,CAAA;WACnB,CAAA;AAAA,SAAA,CAAA,CAAA;AACFb,QAAAA,SAAA,CAAUc,QAAQZ,SAAU,CAAAb,KAAA,CAAA;AAC9B,OAAO,MAAA;AACC,QAAA,IAAA0B,KAAA,GAAQnB,eAAe,mBAAmB,CAAA,CAAA;AAC5C,QAAA,IAAAmB,KAAA,CAAMC,WAAW,CAAG,EAAA;AAEdV,UAAAA,KAAA,GAAAS,KAAA,CAAA;AACRf,UAAAA,SAAA,CAAUc,QAAQZ,SAAU,CAAAa,KAAA,CAAA;AAC9B,SAAA;AACF,OAAA;MAGA,IAAIV,WAAW,UAAY,EAAA;QACzB,OAAO,CAACC,KAAK,CAAA,CAAA;AACf,OAAA;MAGA,IAAIW,OAA6B,EAAC,CAAA;MAClC,IAAIC,KAAQ,GAAAd,MAAA,CAAA;MAGZ,IAAMe,MAA8B,EAAC,CAAA;AAC/Bb,MAAAA,KAAA,CAAAc,OAAA,CAAQ,UAACX,IAAA,EAAMY,KAAU,EAAA;QAC7B,IAAIR,IAAO,GAAA,CAAA,CAAA;QACX,IAAIS,eAAgB,CAAAtB,SAAA,CAAUc,KAAO,EAAAL,IAAI,CAAG,EAAA;AAC1CI,UAAAA,IAAA,GAAOU,KAAM,CAAAd,IAAA,CAAKI,IAAI,CAAA,GAAIA,OAAOJ,IAAK,CAAAI,IAAA,CAAA;AAE/BA,UAAAA,IAAA,GAAAA,IAAA,GAAOT,SAASA,MAAS,GAAAS,IAAA,CAAA;AAClC,SAAO,MAAA;AAAA,UAAA,IAAAW,WAAA,CAAA;UACAf,IAAA,CAAApB,KAAA,GAAQoB,IAAK,CAAApB,KAAA,IAAS,EAAC,CAAA;UAC5BwB,IAAA,GAAOU,qBAAMd,IAAK,CAAApB,KAAA,MAAA,IAAA,IAAAmC,WAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAALA,WAAA,CAAYX,IAAI,CAAI,GAAAA,IAAA,GAAOJ,KAAKpB,KAAM,CAAAwB,IAAA,CAAA;AAC5CA,UAAAA,IAAA,GAAAA,IAAA,GAAOT,SAASA,MAAS,GAAAS,IAAA,CAAA;AAChCJ,UAAAA,IAAA,CAAKpB,MAAMwB,IAAO,GAAAA,IAAA,CAAA;AACpB,SAAA;QAEA,IAAIK,SAASL,IAAM,EAAA;AAEjBI,UAAAA,IAAA,CAAKQ,KAAKhB,IAAI,CAAA,CAAA;AACLS,UAAAA,KAAA,IAAAL,IAAA,CAAA;AACX,SAAO,MAAA;AAELM,UAAAA,GAAA,CAAIM,KAAKR,IAAI,CAAA,CAAA;UACbA,IAAA,GAAO,CAACR,IAAI,CAAA,CAAA;UACZS,KAAA,GAAQd,MAAS,GAAAS,IAAA,CAAA;AACnB,SAAA;AAEI,QAAA,IAAAQ,KAAA,KAAUf,KAAM,CAAAU,MAAA,GAAS,CAAG,EAAA;UAE9B,IAAIM,eAAgB,CAAAtB,SAAA,CAAUc,KAAO,EAAAL,IAAI,CAAG,EAAA;YAC1CA,IAAA,CAAKI,IAAQ,IAAAK,KAAA,CAAA;AACf,WAAO,MAAA;AACLT,YAAAA,IAAA,CAAKpB,MAAMwB,IAAQ,IAAAK,KAAA,CAAA;AACrB,WAAA;AACAC,UAAAA,GAAA,CAAIM,KAAKR,IAAI,CAAA,CAAA;AACf,SAAA;AACF,OAAC,CAAA,CAAA;AACM,MAAA,OAAAE,GAAA,CAAA;KACT,CAAA;AAEAO,IAAAA,OAAA,CAAQC,iBAAiBtC,MAAK,CAAA,CAAA;AAE9B,IAAA,IAAMuC,aAAa,SAAbA,aAAmB;AACvB,MAAA,IAAMC,UAAa,GAAA,CAAA,EAAA,CAAAC,MAAA,CACdvC,cAAe,CAAAuB,KAAA,EAAA,QAAA,CAAA,EAClBnB,IAAA,CAAKmB,MAAMzB,MAAM,CAAA0C,IAAA,CAAA,EAAAC,eAAA,CAAAF,EAAAA,EAAAA,EAAAA,CAAAA,MAAA,CACXvC,eAAeuB,KAAuBzB,EAAAA,eAAAA,CAAAA,EAAAA,MAAAA,CAAM4C,gBAAgB,OAAQ,CAAAD,EAAAA,eAAA,QAAAF,MAAA,CACpEvC,cAAe,CAAAuB,KAAA,qBAAwBzB,OAAM6C,QAAS,CAC9D,CAAA,CAAA;AAEE,MAAA,OAAAC,WAAA,CAAA,OAAA,EAAA;QAAA,OAAcN,EAAAA,UAAAA;OAAAM,EAAAA,CAAAA,WAAA,iBAEThC,OAAA,EAAU,CAAAK,GAAA,CAAI,UAAC4B,GAAA,EAAA;QAAA,OAAAD,WAAA,CAAAE,eAAA,EAAA;UAAA,WACcrC,EAAAA,UAAUc,KAAO;UAAA,KAAKsB,EAAAA,GAAAA;;OACnD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;KAIT,CAAA;AAEA,IAAA,IAAME,eAAe,SAAfA,eAAqB;AACnB,MAAA,IAAAC,KAAA,GAAQzC,eAAe,OAAO,CAAA,CAAA;MAC7B,OAAAyC,KAAA,GAAAJ,WAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAA,EAAA,CAAAL,MAAA,CAAuBvC,cAAe,CAAAuB,KAAA,EAAA,UAAA,CAAA;OAAkByB,EAAAA,CAAAA,KAAM,KAAS,EAAA,CAAA;KAChF,CAAA;IAEA,OAAO,YAAA;AAAA,MAAA,OAAAJ,WAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EACO5C,cAAe,CAAAuB,KAAAA;AAAA,OAAA,EAAA,CACxBwB,YAAa,EAAA,EACbV,UAAW,EAAA,CAAA,CAAA,CAAA;KAFb,CAAA;AAKL,GAAA;AACF,CAAC,CAAA;;;;"}