import { _ as _defineProperty } from '../_chunks/dep-931ef437.js';
import { defineComponent, computed } from 'vue';
import renderFn from '../utils/render-fn.js';
import useSizeProps from '../utils/use-size-props.js';
import '../style/css.js';
import '../utils/use-common-classname.js';
import '../utils/config-context.js';

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var element = {
  "tag": "svg",
  "attrs": {
    "fill": "none",
    "viewBox": "0 0 24 24",
    "width": "1em",
    "height": "1em"
  },
  "children": [{
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M1 2H23V10.25H21V4H3V16H11.5V18H1V2ZM3 20H11.5V22H3V20Z"
    }
  }, {
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M19.4999 12V13.376C20.2149 13.56 20.8525 13.9367 21.354 14.4475L22.5466 13.759L23.5466 15.491L22.355 16.179C22.4494 16.5199 22.4999 16.879 22.4999 17.25C22.4999 17.621 22.4494 17.9801 22.355 18.321L23.5466 19.009L22.5466 20.741L21.354 20.0525C20.8525 20.5633 20.2149 20.94 19.4999 21.124V22.5H17.4999V21.124C16.785 20.94 16.1474 20.5633 15.6458 20.0525L14.4533 20.741L13.4533 19.009L14.6449 18.321C14.5504 17.9801 14.4999 17.621 14.4999 17.25C14.4999 16.879 14.5504 16.5199 14.6449 16.179L13.4533 15.491L14.4533 13.759L15.6458 14.4475C16.1474 13.9367 16.785 13.56 17.4999 13.376V12H19.4999ZM16.7487 16.2833C16.5902 16.5698 16.4999 16.8994 16.4999 17.25C16.4999 17.6006 16.5902 17.9302 16.7487 18.2167L16.7852 18.28C17.1351 18.8612 17.7721 19.25 18.4999 19.25C19.2278 19.25 19.8648 18.8612 20.2147 18.28L20.2512 18.2168C20.4097 17.9302 20.4999 17.6007 20.4999 17.25C20.4999 16.8993 20.4097 16.5698 20.2512 16.2832L20.2147 16.22C19.8648 15.6388 19.2278 15.25 18.4999 15.25C17.7721 15.25 17.1351 15.6388 16.7852 16.22L16.7487 16.2833Z"
    }
  }]
};
var systemSetting = defineComponent({
  name: "SystemSettingIcon",
  props: {
    size: {
      type: String
    },
    onClick: {
      type: Function
    }
  },
  setup(props, _ref) {
    var {
      attrs
    } = _ref;
    var propsSize = computed(() => props.size);
    var {
      className,
      style
    } = useSizeProps(propsSize);
    var finalCls = computed(() => ["t-icon", "t-icon-system-setting", className.value]);
    var finalStyle = computed(() => _objectSpread(_objectSpread({}, style.value), attrs.style));
    var finalProps = computed(() => ({
      class: finalCls.value,
      style: finalStyle.value,
      onClick: e => {
        var _props$onClick;
        return (_props$onClick = props.onClick) === null || _props$onClick === void 0 ? void 0 : _props$onClick.call(props, {
          e
        });
      }
    }));
    return () => renderFn(element, finalProps.value);
  }
});

export default systemSetting;
//# sourceMappingURL=system-setting.js.map
