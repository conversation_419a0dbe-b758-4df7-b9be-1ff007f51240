{"version": 3, "file": "ellipsis.js", "sources": ["../../../../components/table/components/ellipsis.tsx"], "sourcesContent": ["/** 超出省略显示 */\nimport { defineComponent, PropType, ref, computed, onMounted, onUpdated } from 'vue';\nimport { debounce } from 'lodash-es';\nimport type { AttachNode, TNode } from '../../common';\nimport { useContent } from '@tdesign/shared-hooks';\nimport { isTextEllipsis } from '@tdesign/shared-utils';\nimport TTooltip, { TooltipProps } from '../../tooltip';\n\nexport interface EllipsisProps {\n  content: string | TNode;\n  default: string | TNode;\n  tooltipContent: string | number | TNode;\n  placement: TooltipProps['placement'];\n  attach?: AttachNode;\n  tooltipProps: TooltipProps;\n  zIndex: number;\n}\n\nexport default defineComponent({\n  name: 'TEllipsis',\n  props: {\n    /** 内容 */\n    content: {\n      type: [String, Function] as PropType<EllipsisProps['content']>,\n    },\n    /** 内容，同 content */\n    default: {\n      type: [String, Function] as PropType<EllipsisProps['default']>,\n    },\n    /** 内容，同 content，可以单独自定义浮层内容，无需和触发元素保持一致 */\n    tooltipContent: {\n      type: [String, Number, Function] as PropType<EllipsisProps['tooltipContent']>,\n    },\n    /** 浮层位置 */\n    placement: String as PropType<EllipsisProps['placement']>,\n    /** 挂载元素 */\n    attach: [String, Function] as PropType<EllipsisProps['attach']>,\n    /** 透传 Tooltip 组件属性 */\n    tooltipProps: Object as PropType<EllipsisProps['tooltipProps']>,\n    zIndex: Number,\n    overlayClassName: String,\n    classPrefix: {\n      type: String,\n      default: 't',\n    },\n  },\n\n  setup(props) {\n    const root = ref();\n\n    // 用于判断是否需要渲染 Tooltip\n    const flag = ref(false);\n    const isOverflow = ref(false);\n    const renderContent = useContent();\n\n    const ellipsisClasses = computed(() => [\n      `${props.classPrefix}-table__ellipsis`,\n      `${props.classPrefix}-text-ellipsis`,\n    ]);\n\n    const innerEllipsisClassName = computed<TooltipProps['overlayClassName']>(() => [\n      `${props.classPrefix}-table__ellipsis-content`,\n      props.overlayClassName,\n    ]);\n\n    onMounted(() => {\n      isOverflow.value = isTextEllipsis(root.value);\n    });\n\n    onUpdated(() => {\n      isOverflow.value = isTextEllipsis(root.value);\n    });\n\n    // 当表格数据量大时，不希望默认渲染全量的 Tooltip，期望在用户 mouseenter 的时候再显示，通过 flag 判断\n    const onTriggerMouseenter = () => {\n      if (!root.value) return;\n      flag.value = true;\n    };\n\n    const onTriggerMouseleave = () => {\n      if (!root.value) return;\n    };\n    const handleVisibleChange = (v: boolean) => {\n      if (!v) flag.value = false;\n    };\n    // 使用 debounce 有两个原因：1. 避免 safari/firefox 等浏览器不显示省略浮层；2. 避免省略列快速滚动时，出现一堆的省略浮层\n    const onMouseAround = debounce((e: MouseEvent) => {\n      e.type === 'mouseleave' ? onTriggerMouseleave() : onTriggerMouseenter();\n    }, 80);\n\n    return () => {\n      const cellNode = renderContent('default', 'content');\n\n      const ellipsisContent = (\n        <div\n          ref={root}\n          class={ellipsisClasses.value}\n          onMouseenter={onMouseAround}\n          onMouseleave={onMouseAround}\n          style={{\n            textOverflow: isOverflow.value ? 'ellipsis' : 'clip',\n          }}\n        >\n          {cellNode}\n        </div>\n      );\n      let content = null;\n      const tooltipProps = props.tooltipProps as EllipsisProps['tooltipProps'];\n      if (isOverflow.value && flag.value) {\n        const rProps = {\n          content: (props.tooltipContent as string) || (() => cellNode),\n          destroyOnClose: true,\n          zIndex: props.zIndex,\n          attach: props.attach,\n          placement: props.placement,\n          overlayClassName: tooltipProps?.overlayClassName\n            ? innerEllipsisClassName.value.concat(tooltipProps.overlayClassName)\n            : innerEllipsisClassName.value,\n          onVisibleChange: handleVisibleChange,\n          ...tooltipProps,\n        };\n        content = <TTooltip {...rProps}>{ellipsisContent}</TTooltip>;\n      } else {\n        content = ellipsisContent;\n      }\n      return content;\n    };\n  },\n});\n"], "names": ["_isSlot", "s", "Object", "prototype", "toString", "call", "_isVNode", "defineComponent", "name", "props", "content", "type", "String", "Function", "tooltipContent", "Number", "placement", "attach", "tooltipProps", "zIndex", "overlayClassName", "classPrefix", "setup", "root", "ref", "flag", "isOverflow", "renderContent", "useContent", "ellipsisClasses", "computed", "concat", "innerEllipsisClassName", "onMounted", "value", "isTextEllipsis", "onUpdated", "onTriggerMouseenter", "onTriggerMouseleave", "handleVisibleChange", "v", "onMouseAround", "debounce", "e", "cellNode", "ellip<PERSON><PERSON><PERSON><PERSON>", "_createVNode", "textOverflow", "rProps", "_objectSpread", "destroyOnClose", "onVisibleChange", "TTooltip", "_default"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMuC,SAAAA,QAAAC,CAAA,EAAA;AAAA,EAAA,OAAA,OAAAA,CAAA,KAAA,UAAA,IAAAC,MAAA,CAAAC,SAAA,CAAAC,QAAA,CAAAC,IAAA,CAAAJ,CAAA,CAAAK,KAAAA,iBAAAA,IAAAA,CAAAA,WAAA,CAAAL,CAAA,CAAA,CAAA;AAAA,CAAA;AAYvC,gBAAeM,mBAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,WAAA;AACNC,EAAAA,KAAO,EAAA;AAELC,IAAAA,OAAS,EAAA;AACPC,MAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,QAAQ,CAAA;KACzB;IAEA,SAAS,EAAA;AACPF,MAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,QAAQ,CAAA;KACzB;AAEAC,IAAAA,cAAgB,EAAA;AACdH,MAAAA,IAAM,EAAA,CAACC,MAAQ,EAAAG,MAAA,EAAQF,QAAQ,CAAA;KACjC;AAEAG,IAAAA,SAAW,EAAAJ,MAAA;AAEXK,IAAAA,MAAA,EAAQ,CAACL,MAAA,EAAQC,QAAQ,CAAA;AAEzBK,IAAAA,YAAc,EAAAhB,MAAA;AACdiB,IAAAA,MAAQ,EAAAJ,MAAA;AACRK,IAAAA,gBAAkB,EAAAR,MAAA;AAClBS,IAAAA,WAAa,EAAA;AACXV,MAAAA,IAAM,EAAAC,MAAA;MACN,SAAS,EAAA,GAAA;AACX,KAAA;GACF;AAEAU,EAAAA,OAAAA,SAAAA,MAAMb,KAAO,EAAA;AACX,IAAA,IAAMc,OAAOC,OAAI,EAAA,CAAA;AAGX,IAAA,IAAAC,IAAA,GAAOD,QAAI,KAAK,CAAA,CAAA;AAChB,IAAA,IAAAE,UAAA,GAAaF,QAAI,KAAK,CAAA,CAAA;AAC5B,IAAA,IAAMG,gBAAgBC,gBAAW,EAAA,CAAA;IAE3B,IAAAC,eAAA,GAAkBC,aAAS,YAAA;AAAA,MAAA,OAAM,CAAAC,EAAAA,CAAAA,MAAA,CAClCtB,KAAM,CAAAY,WAAA,EAAAU,kBAAAA,CAAAA,EAAAA,EAAAA,CAAAA,MAAA,CACNtB,KAAM,CAAAY,WAAA,EACV,gBAAA,CAAA,CAAA,CAAA;KAAA,CAAA,CAAA;IAEK,IAAAW,sBAAA,GAAyBF,aAA2C,YAAA;MAAA,OAAM,CAAA,EAAA,CAAAC,MAAA,CAC3EtB,KAAM,CAAAY,WAAA,EACTZ,0BAAAA,CAAAA,EAAAA,KAAM,CAAAW,gBAAA,CACP,CAAA;KAAA,CAAA,CAAA;AAEDa,IAAAA,aAAA,CAAU,YAAM;MACHP,UAAA,CAAAQ,KAAA,GAAQC,kBAAe,CAAAZ,IAAA,CAAKW,KAAK,CAAA,CAAA;AAC9C,KAAC,CAAA,CAAA;AAEDE,IAAAA,aAAA,CAAU,YAAM;MACHV,UAAA,CAAAQ,KAAA,GAAQC,kBAAe,CAAAZ,IAAA,CAAKW,KAAK,CAAA,CAAA;AAC9C,KAAC,CAAA,CAAA;AAGD,IAAA,IAAMG,sBAAsB,SAAtBA,sBAA4B;AAChC,MAAA,IAAI,CAACd,IAAK,CAAAW,KAAA,EAAO,OAAA;MACjBT,IAAA,CAAKS,KAAQ,GAAA,IAAA,CAAA;KACf,CAAA;AAEA,IAAA,IAAMI,sBAAsB,SAAtBA,sBAA4B;AAChC,MAAA,IAAI,CAACf,IAAK,CAAAW,KAAA,EAAO,OAAA;KACnB,CAAA;AACM,IAAA,IAAAK,mBAAA,GAAsB,SAAtBA,mBAAAA,CAAuBC,CAAe,EAAA;AAC1C,MAAA,IAAI,CAACA,CAAA,EAAGf,IAAA,CAAKS,KAAQ,GAAA,KAAA,CAAA;KACvB,CAAA;AAEM,IAAA,IAAAO,aAAA,GAAgBC,iBAAS,CAAA,UAACC,CAAkB,EAAA;MAChDA,CAAA,CAAEhC,IAAS,KAAA,YAAA,GAAe2B,mBAAoB,EAAA,GAAID,mBAAoB,EAAA,CAAA;OACrE,EAAE,CAAA,CAAA;AAEL,IAAA,OAAO,YAAM;AACL,MAAA,IAAAO,QAAA,GAAWjB,aAAc,CAAA,SAAA,EAAW,SAAS,CAAA,CAAA;MAEnD,IAAMkB,eACJ,GAAAC,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,KAAA,EACOvB,IACL;QAAA,OAAOM,EAAAA,eAAgB,CAAAK,KAAA;AAAA,QAAA,cAAA,EACTO,aAAA;AAAA,QAAA,cAAA,EACAA,aAAA;QAAA,OACP,EAAA;AACLM,UAAAA,YAAA,EAAcrB,UAAW,CAAAQ,KAAA,GAAQ,UAAa,GAAA,MAAA;AAChD,SAAA;AAAA,OAAA,EAAA,CAECU,SATF,CAAA,CAAA;MAYH,IAAIlC,OAAU,GAAA,IAAA,CAAA;AACd,MAAA,IAAMQ,eAAeT,KAAM,CAAAS,YAAA,CAAA;AACvB,MAAA,IAAAQ,UAAA,CAAWQ,KAAS,IAAAT,IAAA,CAAKS,KAAO,EAAA;QAClC,IAAMc,MAAS,GAAAC,aAAA,CAAA;AACbvC,UAAAA,OAAA,EAAUD,KAAM,CAAAK,cAAA,IAA8B,YAAA;AAAA,YAAA,OAAM8B,QAAA,CAAA;WAAA;AACpDM,UAAAA,cAAgB,EAAA,IAAA;UAChB/B,QAAQV,KAAM,CAAAU,MAAA;UACdF,QAAQR,KAAM,CAAAQ,MAAA;UACdD,WAAWP,KAAM,CAAAO,SAAA;UACjBI,gBAAA,EAAkBF,iBAAAA,IAAAA,IAAAA,2BAAAA,aAAcE,gBAC5B,GAAAY,sBAAA,CAAuBE,MAAMH,MAAO,CAAAb,YAAA,CAAaE,gBAAgB,CAAA,GACjEY,sBAAuB,CAAAE,KAAA;AAC3BiB,UAAAA,eAAiB,EAAAZ,mBAAAA;AAAA,SAAA,EACdrB,YAAA,CACL,CAAA;QACAR,OAAA,GAAAoC,eAAA,CAAAM,qBAAA,EAAwBJ,MAAS,EAAAhD,OAAA,CAAA6C,eAAA,CAAA,GAAAA,eAAA,GAAA;AAAA,UAAA,SAAA,EAAA,SAAAQ,QAAA,GAAA;AAAA,YAAA,OAAA,CAAAR,eAAA,CAAA,CAAA;AAAA,WAAA;SAAtB,CAAA,CAAA;AACb,OAAO,MAAA;AACKnC,QAAAA,OAAA,GAAAmC,eAAA,CAAA;AACZ,OAAA;AACO,MAAA,OAAAnC,OAAA,CAAA;KACT,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}