{"version": 3, "file": "empty.mjs", "sources": ["../../../components/empty/empty.tsx"], "sourcesContent": ["import { computed, defineComponent, h, toRefs } from 'vue';\nimport { isString, isPlainObject } from 'lodash-es';\n\nimport { useConfig, useTNodeJSX, usePrefixClass, useCommonClassName } from '@tdesign/shared-hooks';\n\nimport props from './props';\nimport type { TdEmptyProps } from './type';\nimport Image from '../image';\nimport MaintenanceSvg from './components/MaintenanceSvg';\nimport NetworkErrorSvg from './components/NetworkErrorSvg';\nimport EmptySvg from './components/EmptySvg';\nimport FailSvg from './components/FailSvg';\nimport SuccessSvg from './components/SuccessSvg';\n\nexport default defineComponent({\n  name: 'TEmpty',\n  components: { TImage: Image },\n  props,\n  setup(props: TdEmptyProps, { slots }) {\n    const { size, image: propsImage, description: propsDescription, title: propsTitle, type } = toRefs(props);\n    const { globalConfig } = useConfig('empty');\n    const classPrefix = usePrefixClass('empty');\n    const showAction = computed(() => props.action || slots.action);\n    const { SIZE } = useCommonClassName();\n    const renderTNodeJSX = useTNodeJSX();\n\n    const defaultMaps: {\n      [key in TdEmptyProps['type']]?: Pick<TdEmptyProps, 'image' | 'title'>;\n    } = {\n      maintenance: {\n        image: globalConfig.value.image.maintenance || MaintenanceSvg,\n        title: globalConfig.value.titleText.maintenance,\n      },\n      success: {\n        image: globalConfig.value.image.success || SuccessSvg,\n        title: globalConfig.value.titleText.success,\n      },\n      fail: {\n        image: globalConfig.value.image.fail || FailSvg,\n        title: globalConfig.value.titleText.fail,\n      },\n      'network-error': {\n        image: globalConfig.value.image.networkError || NetworkErrorSvg,\n        title: globalConfig.value.titleText.networkError,\n      },\n      empty: {\n        image: globalConfig.value.image.empty || EmptySvg,\n        title: globalConfig.value.titleText.empty,\n      },\n    };\n\n    const emptyClasses = computed(() => [classPrefix.value, SIZE.value[size.value]]);\n    const titleClasses = [`${classPrefix.value}__title`];\n    const imageClasses = [`${classPrefix.value}__image`];\n    const descriptionClasses = [`${classPrefix.value}__description`];\n    const actionClass = [`${classPrefix.value}__action`];\n\n    const typeImageProps = computed(() => defaultMaps[type.value] ?? null);\n    const showImage = computed(() => propsImage.value || slots?.image?.() || typeImageProps.value?.image);\n    const showTitle = computed(() => propsTitle.value || slots?.title?.() || typeImageProps.value?.title);\n    const showDescription = computed(() => propsDescription.value || slots?.description?.());\n\n    const renderTitle = () => {\n      if (!showTitle.value) {\n        return null;\n      }\n      return <div class={titleClasses}>{showTitle.value}</div>;\n    };\n    const renderDescription = () => {\n      if (!showDescription.value) {\n        return null;\n      }\n      return <div class={descriptionClasses}>{showDescription.value}</div>;\n    };\n    const getImageIns = () => {\n      const data = showImage.value;\n      let result = null;\n      if (isString(data)) {\n        result = <Image src={data} />;\n      } else if (data && Reflect.has(data, 'setup')) {\n        result = h(data as unknown);\n      } else if (isPlainObject(data)) {\n        result = <Image {...data} />;\n      }\n\n      return data ? result : null;\n    };\n\n    return () => {\n      return (\n        <div class={emptyClasses.value}>\n          {showImage.value ? (\n            <div class={imageClasses} style={props.imageStyle}>\n              {slots?.image ? renderTNodeJSX('image') : getImageIns()}\n            </div>\n          ) : null}\n          {renderTitle()}\n          {renderDescription()}\n          {showAction.value ? <div class={actionClass}>{renderTNodeJSX('action')}</div> : null}\n        </div>\n      );\n    };\n  },\n});\n"], "names": ["defineComponent", "name", "components", "TImage", "Image", "props", "setup", "slots", "_ref", "_toRefs", "toRefs", "size", "propsImage", "image", "propsDescription", "description", "propsTitle", "title", "type", "_useConfig", "useConfig", "globalConfig", "classPrefix", "usePrefixClass", "showAction", "computed", "action", "_useCommonClassName", "useCommonClassName", "SIZE", "renderTNodeJSX", "useTNodeJSX", "defaultMaps", "maintenance", "value", "MaintenanceSvg", "titleText", "success", "SuccessSvg", "fail", "FailSvg", "networkError", "NetworkErrorSvg", "empty", "EmptySvg", "emptyClasses", "titleClasses", "concat", "imageClasses", "descriptionClasses", "actionClass", "typeImageProps", "_defaultMaps$type$val", "showImage", "_slots$image", "_typeImageProps$value", "call", "showTitle", "_slots$title", "_typeImageProps$value2", "showDescription", "_slots$description", "renderTitle", "_createVNode", "renderDescription", "getImageIns", "data", "result", "isString", "Reflect", "has", "h", "isPlainObject", "imageStyle"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA,aAAeA,eAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,QAAA;AACNC,EAAAA,UAAA,EAAY;AAAEC,IAAAA,MAAA,EAAQC,KAAAA;GAAM;AAC5BC,EAAAA,KAAA,EAAAA,KAAA;AACAC,EAAAA,KAAMD,WAANC,KAAMD,CAAAA,MAAAA,EAAAA,IAAAA,EAAgC;AAAA,IAAA,IAATE,KAAA,GAAAC,IAAA,CAAAD,KAAA,CAAA;AACrB,IAAA,IAAAE,OAAA,GAAsFC,MAAA,CAAOL,MAAK,CAAA;MAAhGM,IAAM,GAAAF,OAAA,CAANE,IAAM;MAAOC,UAAY,GAAAH,OAAA,CAAnBI,KAAA;MAAgCC,gBAAkB,GAAAL,OAAA,CAA/BM,WAAA;MAAsCC,UAAY,GAAAP,OAAA,CAAnBQ,KAAA;MAAmBC,IAAA,GAAAT,OAAA,CAAAS,IAAA,CAAA;AACnF,IAAA,IAAAC,UAAA,GAAyBC,SAAA,CAAU,OAAO,CAAA;MAAlCC,YAAA,GAAAF,UAAA,CAAAE,YAAA,CAAA;AACF,IAAA,IAAAC,WAAA,GAAcC,eAAe,OAAO,CAAA,CAAA;IAC1C,IAAMC,aAAaC,QAAS,CAAA,YAAA;AAAA,MAAA,OAAMpB,MAAM,CAAAqB,MAAA,IAAUnB,MAAMmB,MAAM,CAAA;KAAA,CAAA,CAAA;AACxD,IAAA,IAAAC,mBAAA,GAAWC,kBAAmB,EAAA;MAA5BC,IAAK,GAAAF,mBAAA,CAALE,IAAK,CAAA;AACb,IAAA,IAAMC,iBAAiBC,WAAY,EAAA,CAAA;AAEnC,IAAA,IAAMC,WAEF,GAAA;AACFC,MAAAA,WAAa,EAAA;QACXpB,KAAO,EAAAQ,YAAA,CAAaa,KAAM,CAAArB,KAAA,CAAMoB,WAAe,IAAAE,cAAA;AAC/ClB,QAAAA,KAAA,EAAOI,YAAa,CAAAa,KAAA,CAAME,SAAU,CAAAH,WAAAA;OACtC;AACAI,MAAAA,OAAS,EAAA;QACPxB,KAAO,EAAAQ,YAAA,CAAaa,KAAM,CAAArB,KAAA,CAAMwB,OAAW,IAAAC,UAAA;AAC3CrB,QAAAA,KAAA,EAAOI,YAAa,CAAAa,KAAA,CAAME,SAAU,CAAAC,OAAAA;OACtC;AACAE,MAAAA,IAAM,EAAA;QACJ1B,KAAO,EAAAQ,YAAA,CAAaa,KAAM,CAAArB,KAAA,CAAM0B,IAAQ,IAAAC,OAAA;AACxCvB,QAAAA,KAAA,EAAOI,YAAa,CAAAa,KAAA,CAAME,SAAU,CAAAG,IAAAA;OACtC;AACA,MAAA,eAAiB,EAAA;QACf1B,KAAO,EAAAQ,YAAA,CAAaa,KAAM,CAAArB,KAAA,CAAM4B,YAAgB,IAAAC,eAAA;AAChDzB,QAAAA,KAAA,EAAOI,YAAa,CAAAa,KAAA,CAAME,SAAU,CAAAK,YAAAA;OACtC;AACAE,MAAAA,KAAO,EAAA;QACL9B,KAAO,EAAAQ,YAAA,CAAaa,KAAM,CAAArB,KAAA,CAAM8B,KAAS,IAAAC,QAAA;AACzC3B,QAAAA,KAAA,EAAOI,YAAa,CAAAa,KAAA,CAAME,SAAU,CAAAO,KAAAA;AACtC,OAAA;KACF,CAAA;IAEM,IAAAE,YAAA,GAAepB,QAAS,CAAA,YAAA;AAAA,MAAA,OAAM,CAACH,WAAA,CAAYY,OAAOL,IAAK,CAAAK,KAAA,CAAMvB,IAAK,CAAAuB,KAAA,CAAM,CAAC,CAAA;KAAA,CAAA,CAAA;IAC/E,IAAMY,YAAe,GAAA,CAAAC,EAAAA,CAAAA,MAAA,CAAIzB,WAAA,CAAYY,KAAc,EAAA,SAAA,CAAA,CAAA,CAAA;IACnD,IAAMc,YAAe,GAAA,CAAAD,EAAAA,CAAAA,MAAA,CAAIzB,WAAA,CAAYY,KAAc,EAAA,SAAA,CAAA,CAAA,CAAA;IACnD,IAAMe,kBAAqB,GAAA,CAAAF,EAAAA,CAAAA,MAAA,CAAIzB,WAAA,CAAYY,KAAoB,EAAA,eAAA,CAAA,CAAA,CAAA;IAC/D,IAAMgB,WAAc,GAAA,CAAAH,EAAAA,CAAAA,MAAA,CAAIzB,WAAA,CAAYY,KAAe,EAAA,UAAA,CAAA,CAAA,CAAA;IAEnD,IAAMiB,iBAAiB1B,QAAS,CAAA,YAAA;AAAA,MAAA,IAAA2B,qBAAA,CAAA;AAAA,MAAA,OAAA,CAAAA,qBAAA,GAAMpB,WAAY,CAAAd,IAAA,CAAKgB,+EAAU,IAAI,CAAA;KAAA,CAAA,CAAA;IAC/D,IAAAmB,SAAA,GAAY5B,QAAS,CAAA,YAAA;MAAA,IAAA6B,YAAA,EAAAC,qBAAA,CAAA;AAAA,MAAA,OAAM3C,UAAW,CAAAsB,KAAA,KAAS3B,UAAAA,IAAAA,IAAAA,oCAAAA,MAAOM,KAAQ,cAAAyC,YAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAfA,YAAA,CAAAE,IAAA,CAAAjD,KAAe,CAAA,OAAAgD,qBAAA,GAAKJ,cAAe,CAAAjB,KAAA,MAAAqB,IAAAA,IAAAA,qBAAA,KAAfA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAAsB1C,KAAK,CAAA,CAAA;KAAA,CAAA,CAAA;IAC9F,IAAA4C,SAAA,GAAYhC,QAAS,CAAA,YAAA;MAAA,IAAAiC,YAAA,EAAAC,sBAAA,CAAA;AAAA,MAAA,OAAM3C,UAAW,CAAAkB,KAAA,KAAS3B,UAAAA,IAAAA,IAAAA,oCAAAA,MAAOU,KAAQ,cAAAyC,YAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAfA,YAAA,CAAAF,IAAA,CAAAjD,KAAe,CAAA,OAAAoD,sBAAA,GAAKR,cAAe,CAAAjB,KAAA,MAAAyB,IAAAA,IAAAA,sBAAA,KAAfA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAAsB1C,KAAK,CAAA,CAAA;KAAA,CAAA,CAAA;IACpG,IAAM2C,kBAAkBnC,QAAS,CAAA,YAAA;AAAA,MAAA,IAAAoC,kBAAA,CAAA;MAAA,OAAM/C,iBAAiBoB,KAAS,KAAA3B,KAAA,KAAAA,IAAAA,IAAAA,KAAA,gBAAAsD,kBAAA,GAAAtD,KAAA,CAAOQ,kEAAP8C,kBAAA,CAAAL,IAAA,CAAAjD,KAAqB,CAAC,CAAA,CAAA;KAAA,CAAA,CAAA;AAEvF,IAAA,IAAMuD,cAAc,SAAdA,cAAoB;AACpB,MAAA,IAAA,CAACL,UAAUvB,KAAO,EAAA;AACb,QAAA,OAAA,IAAA,CAAA;AACT,OAAA;AACA,MAAA,OAAA6B,WAAA,CAAA,KAAA,EAAA;QAAA,OAAmBjB,EAAAA,YAAAA;OAAeW,EAAAA,CAAAA,SAAA,CAAUvB;KAC9C,CAAA;AACA,IAAA,IAAM8B,oBAAoB,SAApBA,oBAA0B;AAC1B,MAAA,IAAA,CAACJ,gBAAgB1B,KAAO,EAAA;AACnB,QAAA,OAAA,IAAA,CAAA;AACT,OAAA;AACA,MAAA,OAAA6B,WAAA,CAAA,KAAA,EAAA;QAAA,OAAmBd,EAAAA,kBAAAA;OAAqBW,EAAAA,CAAAA,eAAA,CAAgB1B;KAC1D,CAAA;AACA,IAAA,IAAM+B,cAAc,SAAdA,cAAoB;AACxB,MAAA,IAAMC,OAAOb,SAAU,CAAAnB,KAAA,CAAA;MACvB,IAAIiC,MAAS,GAAA,IAAA,CAAA;AACT,MAAA,IAAAC,QAAA,CAASF,IAAI,CAAG,EAAA;QACTC,MAAA,GAAAJ,WAAA,CAAA3D,KAAA,EAAA;UAAA,KAAY8D,EAAAA,IAAAA;SAAM,EAAA,IAAA,CAAA,CAAA;AAC7B,iBAAWA,IAAQ,IAAAG,OAAA,CAAQC,GAAI,CAAAJ,IAAA,EAAM,OAAO,CAAG,EAAA;AAC7CC,QAAAA,MAAA,GAASI,EAAEL,IAAe,CAAA,CAAA;AAC5B,OAAA,MAAA,IAAWM,aAAc,CAAAN,IAAI,CAAG,EAAA;AACrBC,QAAAA,MAAA,GAAAJ,WAAA,CAAA3D,KAAA,EAAW8D,IAAM,EAAA,IAAA,CAAA,CAAA;AAC5B,OAAA;AAEA,MAAA,OAAOA,OAAOC,MAAS,GAAA,IAAA,CAAA;KACzB,CAAA;AAEA,IAAA,OAAO,YAAM;AAET,MAAA,OAAAJ,WAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAYlB,YAAa,CAAAX,KAAAA;AAAA,OAAA,EAAA,CACtBmB,UAAUnB,KACT,GAAA6B,WAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAYf;iBAAqB3C,MAAAA,CAAMoE,UAAAA;AACpC,OAAA,EAAA,CAAAlE,KAAA,KAAA,IAAA,IAAAA,KAAA,KAAA,KAAA,CAAA,IAAAA,KAAA,CAAOM,QAAQiB,cAAe,CAAA,OAAO,IAAImC,WAAY,EAAA,CAAA,CAAA,GAEtD,IAAA,EACHH,WAAY,EAAA,EACZE,iBAAkB,EAAA,EAClBxC,UAAA,CAAWU,KAAQ,GAAA6B,WAAA,CAAA,KAAA,EAAA;QAAA,OAAYb,EAAAA,WAAAA;UAAcpB,cAAe,CAAA,QAAQ,CAAE,KAAS,IAAA,CAAA,CAAA,CAAA;KAGtF,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}