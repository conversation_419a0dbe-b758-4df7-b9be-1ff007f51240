import { _ as _defineProperty } from '../_chunks/dep-931ef437.js';
import { defineComponent, computed } from 'vue';
import renderFn from '../utils/render-fn.js';
import useSizeProps from '../utils/use-size-props.js';
import '../style/css.js';
import '../utils/use-common-classname.js';
import '../utils/config-context.js';

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var element = {
  "tag": "svg",
  "attrs": {
    "fill": "none",
    "viewBox": "0 0 24 24",
    "width": "1em",
    "height": "1em"
  },
  "children": [{
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M5 6.4998V17.4998L1 17.5 1 6.5 5 6.4998zM7 18.2099L15.0004 22.7095 15.0004 1.29004 7 5.7896V18.2099zM21.1569 6.34285L20.4497 5.63574 19.0355 7.04996 19.7426 7.75706C22.0858 10.1002 22.0858 13.8992 19.7426 16.2423L19.0355 16.9495 20.4497 18.3637 21.1569 17.6566C24.2811 14.5324 24.2811 9.46704 21.1569 6.34285z"
    }
  }, {
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M18.682 7.40351L17.2678 8.81772L17.9749 9.52483C19.3417 10.8917 19.3417 13.1077 17.9749 14.4746L17.2678 15.1817L18.682 16.5959L19.3891 15.8888C21.537 13.7409 21.537 10.2585 19.3891 8.11062L18.682 7.40351Z"
    }
  }, {
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M17.6213 14.121C18.7929 12.9495 18.7929 11.05 17.6213 9.87838L16.9142 9.17128L15.5 10.5855L16.2071 11.2926C16.5976 11.6831 16.5976 12.3163 16.2071 12.7068L15.5 13.4139L16.9142 14.8281L17.6213 14.121Z"
    }
  }]
};
var soundHighFilled = defineComponent({
  name: "SoundHighFilledIcon",
  props: {
    size: {
      type: String
    },
    onClick: {
      type: Function
    }
  },
  setup(props, _ref) {
    var {
      attrs
    } = _ref;
    var propsSize = computed(() => props.size);
    var {
      className,
      style
    } = useSizeProps(propsSize);
    var finalCls = computed(() => ["t-icon", "t-icon-sound-high-filled", className.value]);
    var finalStyle = computed(() => _objectSpread(_objectSpread({}, style.value), attrs.style));
    var finalProps = computed(() => ({
      class: finalCls.value,
      style: finalStyle.value,
      onClick: e => {
        var _props$onClick;
        return (_props$onClick = props.onClick) === null || _props$onClick === void 0 ? void 0 : _props$onClick.call(props, {
          e
        });
      }
    }));
    return () => renderFn(element, finalProps.value);
  }
});

export default soundHighFilled;
//# sourceMappingURL=sound-high-filled.js.map
