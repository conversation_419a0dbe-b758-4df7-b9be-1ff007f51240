{"version": 3, "file": "dropdown.js", "sources": ["../../../components/dropdown/dropdown.tsx"], "sourcesContent": ["import { defineComponent, ref, computed } from 'vue';\nimport { omit, isArray, isNumber } from 'lodash-es';\n\nimport Popup, { PopupVisibleChangeContext } from '../popup/index';\nimport DropdownMenu from './dropdown-menu';\nimport { DropdownOption, TdDropdownProps } from './type';\nimport props from './props';\nimport { useTNodeJSX, usePrefixClass } from '@tdesign/shared-hooks';\n\nimport useDropdownOptions from './hooks/useDropdownOptions';\n\nexport default defineComponent({\n  name: 'TDropdown',\n  props,\n  setup(props: TdDropdownProps, { attrs }) {\n    const renderTNodeJSX = useTNodeJSX();\n    const COMPONENT_NAME = usePrefixClass('dropdown');\n    const popupElem = ref(null);\n    const isPopupVisible = ref(false);\n\n    const manualCloseTimeout = computed(() => {\n      const delay = props.popupProps?.delay;\n      if (isNumber(delay)) return delay + 10;\n      if (isArray(delay)) return (delay[1] ?? delay[0]) + 10;\n      return 160;\n    });\n\n    const handleMenuClick = (data: DropdownOption, context: { e: MouseEvent }) => {\n      if (props.hideAfterItemClick) {\n        setTimeout(() => (isPopupVisible.value = false), manualCloseTimeout.value);\n\n        props.popupProps?.onVisibleChange?.(false, context);\n        // TODO\n        // @ts-ignore types only declare onVisibleChange，but not declare on-visible-change\n        props.popupProps?.['on-visible-change']?.(false, context);\n      }\n\n      props?.onClick?.(data, context);\n    };\n\n    const handleVisibleChange = (visible: boolean, context: PopupVisibleChangeContext) => {\n      isPopupVisible.value = visible;\n\n      props.popupProps?.onVisibleChange?.(visible, context);\n      // TODO\n      // @ts-ignore types only declare onVisibleChange，but not declare on-visible-change\n      props.popupProps?.['on-visible-change']?.(visible, context);\n    };\n\n    return () => {\n      const trigger = renderTNodeJSX('default')?.[0];\n      const options = useDropdownOptions(props);\n\n      const popupParams = {\n        ...attrs,\n        disabled: props.disabled,\n        placement: props.placement,\n        trigger: props.trigger,\n        ...omit(props.popupProps, ['onVisibleChange', 'on-visible-change']),\n        overlayInnerClassName: [\n          COMPONENT_NAME.value,\n          (props.popupProps as TdDropdownProps['popupProps'])?.overlayInnerClassName,\n        ],\n      };\n\n      return (\n        <Popup\n          destroyOnClose={true}\n          ref={popupElem}\n          visible={isPopupVisible.value}\n          onVisibleChange={handleVisibleChange}\n          expandAnimation\n          {...popupParams}\n          v-slots={{\n            content: () => (\n              <>\n                {renderTNodeJSX('panelTopContent')}\n                {options.value?.length ? (\n                  <DropdownMenu {...omit(props, 'onClick')} options={options.value} onClick={handleMenuClick} />\n                ) : null}\n                {renderTNodeJSX('panelBottomContent')}\n              </>\n            ),\n          }}\n        >\n          {trigger}\n        </Popup>\n      );\n    };\n  },\n});\n"], "names": ["defineComponent", "name", "props", "setup", "attrs", "_ref", "renderTNodeJSX", "useTNodeJSX", "COMPONENT_NAME", "usePrefixClass", "popupElem", "ref", "isPopupVisible", "manualCloseTimeout", "computed", "_props2$popupProps", "_delay$", "delay", "popupProps", "isNumber", "isArray", "handleMenuClick", "data", "context", "_props2$onClick", "hideAfterItemClick", "_props2$popupProps2", "_props2$popupProps2$o", "_props2$popupProps3", "_props2$popupProps3$o", "setTimeout", "value", "onVisibleChange", "onClick", "handleVisibleChange", "visible", "_props2$popupProps4", "_props2$popupProps4$o", "_props2$popupProps5", "_props2$popupProps5$o", "_renderTNodeJSX", "_props2$popupProps6", "trigger", "options", "useDropdownOptions", "popupParams", "_objectSpread", "disabled", "placement", "omit", "overlayInnerClassName", "_createVNode", "Popup", "_mergeProps", "content", "_options$value", "length", "DropdownMenu"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,gBAAeA,mBAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,WAAA;AACNC,EAAAA,KAAA,EAAAA,yBAAA;AACAC,EAAAA,KAAMD,WAANC,KAAMD,CAAAA,MAAAA,EAAAA,IAAAA,EAAmC;AAAA,IAAA,IAATE,KAAA,GAAAC,IAAA,CAAAD,KAAA,CAAA;AAC9B,IAAA,IAAME,iBAAiBC,iBAAY,EAAA,CAAA;AAC7B,IAAA,IAAAC,cAAA,GAAiBC,uBAAe,UAAU,CAAA,CAAA;AAC1C,IAAA,IAAAC,SAAA,GAAYC,QAAI,IAAI,CAAA,CAAA;AACpB,IAAA,IAAAC,cAAA,GAAiBD,QAAI,KAAK,CAAA,CAAA;AAE1B,IAAA,IAAAE,kBAAA,GAAqBC,aAAS,YAAM;MAAA,IAAAC,kBAAA,EAAAC,OAAA,CAAA;AAClC,MAAA,IAAAC,KAAA,GAAA,CAAAF,kBAAA,GAAQb,OAAMgB,UAAY,MAAA,IAAA,IAAAH,kBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAlBb,kBAAAA,CAAkBe,KAAA,CAAA;MAChC,IAAIE,kBAASF,KAAK,CAAA,EAAG,OAAOA,KAAQ,GAAA,EAAA,CAAA;MACpC,IAAIG,gBAAQH,KAAK,CAAA,EAAW,OAAA,CAAA,CAAAD,OAAA,GAAAC,KAAA,CAAM,CAAM,CAAA,MAAA,IAAA,IAAAD,OAAA,KAAA,KAAA,CAAA,GAAAA,OAAA,GAAAC,KAAA,CAAM,CAAM,CAAA,IAAA,EAAA,CAAA;AAC7C,MAAA,OAAA,GAAA,CAAA;AACT,KAAC,CAAA,CAAA;IAEK,IAAAI,eAAA,GAAkB,SAAlBA,eAAAA,CAAmBC,IAAA,EAAsBC,OAA+B,EAAA;AAAA,MAAA,IAAAC,eAAA,CAAA;MAC5E,IAAItB,OAAMuB,kBAAoB,EAAA;AAAA,QAAA,IAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,CAAA;AAC5BC,QAAAA,UAAA,CAAW,YAAA;AAAA,UAAA,OAAOlB,cAAA,CAAemB,KAAQ,GAAA,KAAA,CAAA;SAAQlB,EAAAA,mBAAmBkB,KAAK,CAAA,CAAA;QAEzE7B,CAAAA,mBAAAA,GAAAA,MAAM,CAAAgB,UAAA,cAAAQ,mBAAA,KAAA,KAAA,CAAA,IAAA,CAAAC,qBAAA,GAANzB,mBAAAA,CAAkB8B,eAAkB,MAAAL,IAAAA,IAAAA,qBAAA,KAApCzB,KAAAA,CAAAA,IAAAA,qBAAAA,CAAAA,IAAAA,CAAAA,mBAAAA,EAAoC,KAAA,EAAOqB,OAAO,CAAA,CAAA;QAGlDrB,CAAAA,mBAAAA,GAAAA,MAAM,CAAAgB,UAAA,MAAAU,IAAAA,IAAAA,mBAAA,KAAAC,KAAAA,CAAAA,IAAAA,CAAAA,qBAAA,GAAN3B,mBAAAA,CAAmB,mBAAuB,CAAA,MAAA2B,IAAAA,IAAAA,qBAAA,KAA1C3B,KAAAA,CAAAA,IAAAA,qBAAAA,CAAAA,IAAAA,CAAAA,mBAAAA,EAA0C,KAAA,EAAOqB,OAAO,CAAA,CAAA;AAC1D,OAAA;MAEArB,MAAAA,KAAAA,IAAAA,IAAAA,MAAAA,KAAAA,KAAAA,CAAAA,IAAAA,CAAAA,eAAAA,GAAAA,MAAAA,CAAO+B,OAAU,MAAAT,IAAAA,IAAAA,eAAA,eAAjBtB,eAAAA,CAAAA,IAAAA,CAAAA,MAAAA,EAAiBoB,IAAA,EAAMC,OAAO,CAAA,CAAA;KAChC,CAAA;IAEM,IAAAW,mBAAA,GAAsB,SAAtBA,mBAAAA,CAAuBC,OAAA,EAAkBZ,OAAuC,EAAA;AAAA,MAAA,IAAAa,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,CAAA;MACpF3B,cAAA,CAAemB,KAAQ,GAAAI,OAAA,CAAA;MAEvBjC,CAAAA,mBAAAA,GAAAA,MAAM,CAAAgB,UAAA,cAAAkB,mBAAA,KAAA,KAAA,CAAA,IAAA,CAAAC,qBAAA,GAANnC,mBAAAA,CAAkB8B,eAAkB,MAAAK,IAAAA,IAAAA,qBAAA,KAApCnC,KAAAA,CAAAA,IAAAA,qBAAAA,CAAAA,IAAAA,CAAAA,mBAAAA,EAAoCiC,OAAA,EAASZ,OAAO,CAAA,CAAA;MAGpDrB,CAAAA,mBAAAA,GAAAA,MAAM,CAAAgB,UAAA,MAAAoB,IAAAA,IAAAA,mBAAA,KAAAC,KAAAA,CAAAA,IAAAA,CAAAA,qBAAA,GAANrC,mBAAAA,CAAmB,mBAAuB,CAAA,MAAAqC,IAAAA,IAAAA,qBAAA,KAA1CrC,KAAAA,CAAAA,IAAAA,qBAAAA,CAAAA,IAAAA,CAAAA,mBAAAA,EAA0CiC,OAAA,EAASZ,OAAO,CAAA,CAAA;KAC5D,CAAA;AAEA,IAAA,OAAO,YAAM;MAAA,IAAAiB,eAAA,EAAAC,mBAAA,CAAA;AACL,MAAA,IAAAC,OAAA,GAAA,CAAAF,eAAA,GAAUlC,cAAe,CAAA,SAAS,CAAI,MAAA,IAAA,IAAAkC,eAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,eAAA,CAA4B,CAAA,CAAA,CAAA;AACtC,MAAA,IAAAG,OAAA,GAAUC,6CAAmB1C,MAAK,CAAA,CAAA;MAExC,IAAM2C,WAAc,GAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACf1C,KAAA,CAAA,EAAA,EAAA,EAAA;QACH2C,UAAU7C,MAAM,CAAA6C,QAAA;QAChBC,WAAW9C,MAAM,CAAA8C,SAAA;QACjBN,SAASxC,MAAM,CAAAwC,OAAAA;OACZO,EAAAA,SAAK/C,CAAAA,MAAAA,CAAMgB,YAAY,CAAC,iBAAA,EAAmB,mBAAmB,CAAC,CAAA,CAAA,EAAA,EAAA,EAAA;AAClEgC,QAAAA,qBAAuB,EAAA,CACrB1C,cAAe,CAAAuB,KAAA,GAAAU,mBAAA,GACdvC,OAAMgB,UAA8C,MAAAuB,IAAAA,IAAAA,mBAAA,KAApDvC,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAAA,CAAoDgD,qBAAA,CAAA;OAEzD,CAAA,CAAA;AAEA,MAAA,OAAAC,eAAA,CAAAC,iBAAA,EAAAC,cAAA,CAAA;AAAA,QAAA,gBAAA,EAEoB,IAAA;AAAA,QAAA,KAAA,EACX3C,SAAA;QAAA,SACIE,EAAAA,cAAA,CAAemB,KACxB;AAAA,QAAA,iBAAA,EAAiBG,mBACjB;AAAA,QAAA,iBAAA,EAAA,IAAA;AAAA,OAAA,EACIW;;kBAaHH;;QAXCY,SAAS,SAATA;;sDAEKhD,eAAe,iBAAiB,CAAA,EAChC,CAAAiD,cAAA,GAAAZ,OAAQ,CAAAZ,KAAA,MAAA,IAAA,IAAAwB,cAAA,KAAA,KAAA,CAAA,IAARA,cAAA,CAAeC,MACd,GAAAL,eAAA,CAAAM,gCAAA,EAAAJ,cAAA,CAAkBJ,SAAK/C,CAAAA,MAAAA,EAAO,SAAS,CAAA,EAAA;YAAA,SAAYyC,EAAAA,OAAA,CAAQZ,KAAO;YAAA,SAASV,EAAAA,eAAAA;sBACzE,IAAA,EACHf,eAAe,oBAAoB,CAAA,CAAA,CAAA,CAAA;AAAA,SAAA;AACtC,OAAA,CAAA,CAAA;KAOV,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}