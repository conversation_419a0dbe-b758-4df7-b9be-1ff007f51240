import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<{
    className: {
        type: StringConstructor;
        default: string;
    };
    value: {
        type: NumberConstructor;
        default: number;
    };
    maxValue: {
        type: NumberConstructor;
        default: number;
    };
    railStyle: {
        type: PropType<any>;
    };
    type: {
        type: PropType<"hue" | "alpha">;
        default: string;
    };
    disabled: BooleanConstructor;
    color: {
        type: PropType<import("@common/js/color-picker").Color>;
    };
    onChange: {
        type: FunctionConstructor;
        default: () => () => void;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    className: {
        type: StringConstructor;
        default: string;
    };
    value: {
        type: NumberConstructor;
        default: number;
    };
    maxValue: {
        type: NumberConstructor;
        default: number;
    };
    railStyle: {
        type: PropType<any>;
    };
    type: {
        type: PropType<"hue" | "alpha">;
        default: string;
    };
    disabled: BooleanConstructor;
    color: {
        type: PropType<import("@common/js/color-picker").Color>;
    };
    onChange: {
        type: FunctionConstructor;
        default: () => () => void;
    };
}>>, {
    value: number;
    disabled: boolean;
    type: "alpha" | "hue";
    onChange: Function;
    className: string;
    maxValue: number;
}, {}>;
export default _default;
