import { TdRadioGroupProps } from '../radio/type';
import { PropType } from 'vue';
declare const _default: {
    allowUncheck: BooleanConstructor;
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    name: {
        type: StringConstructor;
        default: string;
    };
    options: {
        type: PropType<TdRadioGroupProps["options"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    size: {
        type: PropType<TdRadioGroupProps["size"]>;
        default: TdRadioGroupProps["size"];
        validator(val: TdRadioGroupProps["size"]): boolean;
    };
    theme: {
        type: PropType<TdRadioGroupProps["theme"]>;
        default: TdRadioGroupProps["theme"];
        validator(val: TdRadioGroupProps["theme"]): boolean;
    };
    value: {
        type: PropType<TdRadioGroupProps["value"]>;
        default: TdRadioGroupProps["value"];
    };
    modelValue: {
        type: PropType<TdRadioGroupProps["value"]>;
        default: TdRadioGroupProps["value"];
    };
    defaultValue: {
        type: PropType<TdRadioGroupProps["defaultValue"]>;
    };
    variant: {
        type: PropType<TdRadioGroupProps["variant"]>;
        default: TdRadioGroupProps["variant"];
        validator(val: TdRadioGroupProps["variant"]): boolean;
    };
    onChange: PropType<TdRadioGroupProps["onChange"]>;
};
export default _default;
