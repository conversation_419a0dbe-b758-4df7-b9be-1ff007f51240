{"version": 3, "file": "dialog-card.js", "sources": ["../../../components/dialog/dialog-card.tsx"], "sourcesContent": ["import { computed, defineComponent, ref, toRefs } from 'vue';\nimport {\n  CloseIcon as TdCloseIcon,\n  InfoCircleFilledIcon as TdInfoCircleFilledIcon,\n  CheckCircleFilledIcon as TdCheckCircleFilledIcon,\n  ErrorCircleFilledIcon as TdErrorCircleFilledIcon,\n} from 'tdesign-icons-vue-next';\nimport dialogCardProps from './dialog-card-props';\nimport { useConfig, useContent, useTNodeJSX, useGlobalIcon, usePrefixClass } from '@tdesign/shared-hooks';\n\nimport { useAction } from './hooks';\n\nimport type { TdDialogProps } from './type';\nimport dialogProps from './props';\nimport { getCSSValue, initDragEvent } from './utils';\n\nexport default defineComponent({\n  name: 'TDialogCard',\n  // 注册v-draggable指令,传入true时候初始化拖拽事件\n  directives: {\n    draggable(el, binding) {\n      // el 指令绑定的元素\n      if (el && binding && binding.value) {\n        initDragEvent(el);\n      }\n    },\n  },\n  props: {\n    ...dialogProps,\n    ...dialogCardProps,\n  },\n  setup(props, { expose }) {\n    const rootRef = ref<HTMLElement | null>(null);\n    const COMPONENT_NAME = usePrefixClass('dialog');\n    const classPrefix = usePrefixClass();\n    const renderTNodeJSX = useTNodeJSX();\n    const renderContent = useContent();\n    const { globalConfig } = useConfig('dialog');\n    const { CloseIcon, InfoCircleFilledIcon, CheckCircleFilledIcon, ErrorCircleFilledIcon } = useGlobalIcon({\n      CloseIcon: TdCloseIcon,\n      InfoCircleFilledIcon: TdInfoCircleFilledIcon,\n      CheckCircleFilledIcon: TdCheckCircleFilledIcon,\n      ErrorCircleFilledIcon: TdErrorCircleFilledIcon,\n    });\n    const { cancelBtn, confirmBtn, confirmLoading } = toRefs(props);\n    const confirmBtnAction = (e: MouseEvent) => props.onConfirm?.({ e });\n    const cancelBtnAction = (e: MouseEvent) => props.onCancel?.({ e });\n    const { getConfirmBtn, getCancelBtn } = useAction({ confirmBtnAction, cancelBtnAction });\n    // 是否非模态对话框\n    const isModeLess = computed(() => props.mode === 'modeless');\n    // 是否全屏对话框\n    const isFullScreen = computed(() => props.mode === 'full-screen');\n    const closeBtnAction = (e: MouseEvent) => props?.onCloseBtnClick?.({ e });\n    const onStopDown = (e: MouseEvent) => {\n      if (isModeLess.value && props?.draggable) e.stopPropagation();\n    };\n\n    const resetPosition = () => {\n      if (!rootRef.value && isModeLess.value && props.draggable) return;\n      rootRef.value.style.position = 'relative';\n      rootRef.value.style.left = 'unset';\n      rootRef.value.style.top = 'unset';\n    };\n\n    // 暴露给父组件的接口\n    expose({\n      $el: rootRef,\n      resetPosition,\n    });\n\n    const dialogClass = computed(() => {\n      const dialogClass = [\n        `${COMPONENT_NAME.value}`,\n        `${COMPONENT_NAME.value}__modal-${props.theme}`,\n        isModeLess.value && props.draggable && `${COMPONENT_NAME.value}--draggable`,\n        props.dialogClassName,\n      ];\n\n      if (isFullScreen.value) {\n        dialogClass.push(`${COMPONENT_NAME.value}__fullscreen`);\n      } else {\n        dialogClass.push(...[`${COMPONENT_NAME.value}--default`, `${COMPONENT_NAME.value}--${props.placement}`]);\n      }\n      return dialogClass;\n    });\n    const dialogStyle = computed(() => {\n      return !isFullScreen.value ? { width: getCSSValue(props.width), ...props.dialogStyle } : { ...props.dialogStyle }; // width全屏模式不生效\n    });\n\n    const renderCard = () => {\n      const confirmBtnLoading = computed(() => {\n        // @ts-ignore\n        return confirmBtn.value?.loading || confirmLoading.value;\n      });\n      const defaultFooter = (\n        <div>\n          {getCancelBtn({\n            cancelBtn: cancelBtn.value as TdDialogProps['cancelBtn'],\n            globalCancel: globalConfig.value.cancel,\n            className: `${COMPONENT_NAME.value}__cancel`,\n          })}\n          {getConfirmBtn({\n            theme: props?.theme,\n            confirmBtn: confirmBtn.value as TdDialogProps['confirmBtn'],\n            globalConfirm: globalConfig.value.confirm,\n            globalConfirmBtnTheme: globalConfig.value.confirmBtnTheme,\n            className: `${COMPONENT_NAME.value}__confirm`,\n            confirmLoading: confirmBtnLoading.value,\n          })}\n        </div>\n      );\n\n      const footerContent = renderTNodeJSX('footer', defaultFooter);\n\n      const renderHeader = () => {\n        // header 值为 true 显示空白头部\n        const header = renderTNodeJSX('header', <h5 class=\"title\"></h5>) ?? false;\n        const headerClassName = isFullScreen.value\n          ? [`${COMPONENT_NAME.value}__header`, `${COMPONENT_NAME.value}__header--fullscreen`]\n          : `${COMPONENT_NAME.value}__header`;\n\n        const closeClassName = isFullScreen.value\n          ? [`${COMPONENT_NAME.value}__close`, `${COMPONENT_NAME.value}__close--fullscreen`]\n          : `${COMPONENT_NAME.value}__close`;\n        const getIcon = () => {\n          const icon = {\n            info: <InfoCircleFilledIcon class={`${classPrefix.value}-is-info`} />,\n            warning: <ErrorCircleFilledIcon class={`${classPrefix.value}-is-warning`} />,\n            danger: <ErrorCircleFilledIcon class={`${classPrefix.value}-is-error`} />,\n            success: <CheckCircleFilledIcon class={`${classPrefix.value}-is-success`} />,\n          };\n          return icon[props?.theme as keyof typeof icon];\n        };\n        return (\n          (header || props?.closeBtn) && (\n            <div class={headerClassName} onMousedown={onStopDown}>\n              <div class={`${COMPONENT_NAME.value}__header-content`}>\n                {getIcon()}\n                {header}\n              </div>\n\n              {props?.closeBtn ? (\n                <span class={closeClassName} onClick={closeBtnAction}>\n                  {renderTNodeJSX('closeBtn', <CloseIcon />)}\n                </span>\n              ) : null}\n            </div>\n          )\n        );\n      };\n\n      const renderBody = () => {\n        const body = renderContent('default', 'body');\n        const bodyClassName =\n          props?.theme === 'default' ? [`${COMPONENT_NAME.value}__body`] : [`${COMPONENT_NAME.value}__body__icon`];\n        if (isFullScreen.value && footerContent) {\n          bodyClassName.push(`${COMPONENT_NAME.value}__body--fullscreen`);\n        } else if (isFullScreen.value) {\n          bodyClassName.push(`${COMPONENT_NAME.value}__body--fullscreen--without-footer`);\n        }\n        return (\n          <div class={bodyClassName} onMousedown={onStopDown}>\n            {body}\n          </div>\n        );\n      };\n\n      const renderFooter = () => {\n        const footerClassName = isFullScreen.value\n          ? [`${COMPONENT_NAME.value}__footer`, `${COMPONENT_NAME.value}__footer--fullscreen`]\n          : `${COMPONENT_NAME.value}__footer`;\n\n        return (\n          footerContent && (\n            <div class={footerClassName} onMousedown={onStopDown}>\n              {footerContent}\n            </div>\n          )\n        );\n      };\n\n      return (\n        <>\n          {renderHeader()}\n          {renderBody()}\n          {!!props.footer && renderFooter()}\n        </>\n      );\n    };\n\n    return () => (\n      <div\n        key=\"dialog\"\n        ref={rootRef}\n        class={dialogClass.value}\n        style={dialogStyle.value}\n        v-draggable={isModeLess.value && props.draggable}\n      >\n        {renderCard()}\n      </div>\n    );\n  },\n});\n"], "names": ["defineComponent", "name", "directives", "draggable", "el", "binding", "value", "initDragEvent", "props", "_objectSpread", "dialogProps", "dialogCardProps", "setup", "_ref", "expose", "rootRef", "ref", "COMPONENT_NAME", "usePrefixClass", "classPrefix", "renderTNodeJSX", "useTNodeJSX", "renderContent", "useContent", "_useConfig", "useConfig", "globalConfig", "_useGlobalIcon", "useGlobalIcon", "CloseIcon", "TdCloseIcon", "InfoCircleFilledIcon", "TdInfoCircleFilledIcon", "CheckCircleFilledIcon", "TdCheckCircleFilledIcon", "ErrorCircleFilledIcon", "TdErrorCircleFilledIcon", "_toRefs", "toRefs", "cancelBtn", "confirmBtn", "confirmLoading", "confirmBtnAction", "e", "_props$onConfirm", "onConfirm", "call", "cancelBtnAction", "_props$onCancel", "onCancel", "_useAction", "useAction", "getConfirmBtn", "getCancelBtn", "isModeLess", "computed", "mode", "isFullScreen", "closeBtnAction", "_props$onCloseBtnClic", "onCloseBtnClick", "onStopDown", "stopPropagation", "resetPosition", "style", "position", "left", "top", "$el", "dialogClass", "concat", "theme", "dialogClassName", "push", "apply", "placement", "dialogStyle", "width", "getCSSValue", "renderCard", "confirmBtnLoading", "_confirmBtn$value", "loading", "defaultFooter", "globalCancel", "cancel", "className", "globalConfirm", "confirm", "globalConfirmBtnTheme", "confirmBtnTheme", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "renderHeader", "_renderTNodeJSX", "header", "_createVNode", "headerClassName", "closeClassName", "getIcon", "icon", "info", "warning", "danger", "success", "closeBtn", "renderBody", "body", "bodyClassName", "renderFooter", "footerClassName", "_Fragment", "footer", "_withDirectives"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,kBAAeA,mBAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,aAAA;AAENC,EAAAA,UAAY,EAAA;AACVC,IAAAA,SAAA,WAAAA,SAAAA,CAAUC,IAAIC,OAAS,EAAA;AAEjB,MAAA,IAAAD,EAAA,IAAMC,OAAW,IAAAA,OAAA,CAAQC,KAAO,EAAA;QAClCC,gCAAA,CAAcH,EAAE,CAAA,CAAA;AAClB,OAAA;AACF,KAAA;GACF;EACAI,KAAO,EAAAC,aAAA,CAAAA,aAAA,KACFC,uBAAA,CAAA,EACAC,iCAAA,CACL;AACAC,EAAAA,KAAM,WAANA,KAAMA,CAAAJ,KAAA,EAAAK,IAAA,EAAmB;AAAA,IAAA,IAAVC,MAAA,GAAAD,IAAA,CAAAC,MAAA,CAAA;AACP,IAAA,IAAAC,OAAA,GAAUC,QAAwB,IAAI,CAAA,CAAA;AACtC,IAAA,IAAAC,cAAA,GAAiBC,qBAAe,QAAQ,CAAA,CAAA;AAC9C,IAAA,IAAMC,cAAcD,oBAAe,EAAA,CAAA;AACnC,IAAA,IAAME,iBAAiBC,mBAAY,EAAA,CAAA;AACnC,IAAA,IAAMC,gBAAgBC,kBAAW,EAAA,CAAA;AACjC,IAAA,IAAAC,UAAA,GAAyBC,wCAAA,CAAU,QAAQ,CAAA;MAAnCC,YAAA,GAAAF,UAAA,CAAAE,YAAA,CAAA;IACR,IAAAC,cAAA,GAA0FC,qBAAc,CAAA;AACtGC,QAAAA,SAAW,EAAAC,6BAAA;AACXC,QAAAA,oBAAsB,EAAAC,wCAAA;AACtBC,QAAAA,qBAAuB,EAAAC,yCAAA;AACvBC,QAAAA,qBAAuB,EAAAC,yCAAAA;AACzB,OAAC,CAAA;MALOP,SAAW,GAAAF,cAAA,CAAXE,SAAW;MAAAE,oBAAA,GAAAJ,cAAA,CAAAI,oBAAA;MAAsBE,qBAAuB,GAAAN,cAAA,CAAvBM,qBAAuB;MAAAE,qBAAA,GAAAR,cAAA,CAAAQ,qBAAA,CAAA;AAMhE,IAAA,IAAAE,OAAA,GAAkDC,WAAO9B,KAAK,CAAA;MAAtD+B,SAAW,GAAAF,OAAA,CAAXE,SAAW;MAAAC,UAAA,GAAAH,OAAA,CAAAG,UAAA;MAAYC,cAAe,GAAAJ,OAAA,CAAfI,cAAe,CAAA;AAC9C,IAAA,IAAMC,mBAAmB,SAAnBA,iBAAoBC,CAAA,EAAA;AAAA,MAAA,IAAAC,gBAAA,CAAA;AAAA,MAAA,OAAA,CAAAA,gBAAA,GAAkBpC,MAAMqC,SAAY,MAAA,IAAA,IAAAD,gBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAlBA,gBAAA,CAAAE,IAAA,CAAAtC,OAAkB;AAAEmC,QAAAA,GAAAA,CAAAA;AAAE,OAAC,CAAA,CAAA;AAAA,KAAA,CAAA;AACnE,IAAA,IAAMI,kBAAkB,SAAlBA,gBAAmBJ,CAAA,EAAA;AAAA,MAAA,IAAAK,eAAA,CAAA;AAAA,MAAA,OAAA,CAAAA,eAAA,GAAkBxC,MAAMyC,QAAW,MAAA,IAAA,IAAAD,eAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAjBA,eAAA,CAAAF,IAAA,CAAAtC,OAAiB;AAAEmC,QAAAA,GAAAA,CAAAA;AAAE,OAAC,CAAA,CAAA;AAAA,KAAA,CAAA;IAC3D,IAAAO,UAAA,GAAkCC,iCAAU;AAAET,QAAAA,gBAAA,EAAAA,gBAAA;AAAkBK,QAAAA,iBAAAA,eAAAA;AAAgB,OAAC,CAAA;MAA/EK,2BAAAA;MAAeC,YAAa,GAAAH,UAAA,CAAbG,YAAa,CAAA;IAEpC,IAAMC,UAAa,GAAAC,YAAA,CAAS,YAAA;AAAA,MAAA,OAAM/C,KAAA,CAAMgD,SAAS,UAAU,CAAA;KAAA,CAAA,CAAA;IAE3D,IAAMC,YAAe,GAAAF,YAAA,CAAS,YAAA;AAAA,MAAA,OAAM/C,KAAA,CAAMgD,SAAS,aAAa,CAAA;KAAA,CAAA,CAAA;AAChE,IAAA,IAAME,iBAAiB,SAAjBA,eAAkBf,CAAA,EAAA;AAAA,MAAA,IAAAgB,qBAAA,CAAA;AAAA,MAAA,OAAkBnD,UAAAA,IAAAA,IAAAA,6CAAAA,MAAOoD,eAAkB,MAAA,IAAA,IAAAD,qBAAA,KAAzBA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAAAb,IAAA,CAAAtC,OAAyB;AAAEmC,QAAAA,GAAAA,CAAAA;AAAE,OAAC,CAAA,CAAA;AAAA,KAAA,CAAA;AAClE,IAAA,IAAAkB,UAAA,GAAa,SAAbA,UAAAA,CAAclB,CAAkB,EAAA;AAChC,MAAA,IAAAW,UAAA,CAAWhD,SAASE,KAAO,aAAPA,KAAO,KAAA,KAAA,CAAA,IAAPA,KAAO,CAAAL,SAAA,EAAWwC,CAAA,CAAEmB,eAAgB,EAAA,CAAA;KAC9D,CAAA;AAEA,IAAA,IAAMC,gBAAgB,SAAhBA,gBAAsB;AAC1B,MAAA,IAAI,CAAChD,OAAA,CAAQT,KAAS,IAAAgD,UAAA,CAAWhD,SAASE,KAAM,CAAAL,SAAA,EAAW,OAAA;AACnDY,MAAAA,OAAA,CAAAT,KAAA,CAAM0D,MAAMC,QAAW,GAAA,UAAA,CAAA;AACvBlD,MAAAA,OAAA,CAAAT,KAAA,CAAM0D,MAAME,IAAO,GAAA,OAAA,CAAA;AACnBnD,MAAAA,OAAA,CAAAT,KAAA,CAAM0D,MAAMG,GAAM,GAAA,OAAA,CAAA;KAC5B,CAAA;AAGOrD,IAAAA,MAAA,CAAA;AACLsD,MAAAA,GAAK,EAAArD,OAAA;AACLgD,MAAAA,aAAA,EAAAA,aAAAA;AACF,KAAC,CAAA,CAAA;AAEK,IAAA,IAAAM,WAAA,GAAcd,aAAS,YAAM;AACjC,MAAA,IAAMc,YAAc,GAAA,CAAA,EAAA,CAAAC,MAAA,CACfrD,cAAe,CAAAX,KAAA,CAAAgE,EAAAA,EAAAA,CAAAA,MAAA,CACfrD,cAAe,CAAAX,KAAA,EAAAgE,UAAAA,CAAAA,CAAAA,MAAA,CAAgB9D,KAAM,CAAA+D,KAAA,CAAA,EACxCjB,UAAW,CAAAhD,KAAA,IAASE,KAAM,CAAAL,SAAA,IAAAmE,EAAAA,CAAAA,MAAA,CAAgBrD,cAAe,CAAAX,KAAA,EAAA,aAAA,CAAA,EACzDE,KAAM,CAAAgE,eAAA,CACR,CAAA;MAEA,IAAIf,aAAanD,KAAO,EAAA;QACtB+D,YAAY,CAAAI,IAAA,CAAAH,EAAAA,CAAAA,MAAA,CAAQrD,cAAA,CAAeX,KAAmB,EAAA,cAAA,CAAA,CAAA,CAAA;AACxD,OAAO,MAAA;QACL+D,YAAY,CAAAI,IAAA,CAAAC,KAAA,CAAZL,YAAY,EAAQ,CAAAC,EAAAA,CAAAA,MAAA,CAAIrD,cAAA,CAAeX,KAAkB,mBAAAgE,MAAA,CAAGrD,cAAe,CAAAX,KAAA,EAAA,IAAA,CAAA,CAAAgE,MAAA,CAAU9D,KAAM,CAAAmE,SAAA,CAAA,CAAY,CAAA,CAAA;AACzG,OAAA;AACON,MAAAA,OAAAA,YAAAA,CAAAA;AACT,KAAC,CAAA,CAAA;AACK,IAAA,IAAAO,WAAA,GAAcrB,aAAS,YAAM;AACjC,MAAA,OAAO,CAACE,YAAa,CAAAnD,KAAA,GAAAG,aAAA,CAAA;AAAUoE,QAAAA,KAAA,EAAOC,+BAAYtE,KAAM,CAAAqE,KAAK,CAAA;OAAMrE,EAAAA,MAAMoE,WAAY,CAAA,GAAAnE,aAAA,CAASD,EAAAA,EAAAA,MAAMoE,WAAY,CAAA,CAAA;AAClH,KAAC,CAAA,CAAA;AAED,IAAA,IAAMG,aAAa,SAAbA,aAAmB;AACjB,MAAA,IAAAC,iBAAA,GAAoBzB,aAAS,YAAM;AAAA,QAAA,IAAA0B,iBAAA,CAAA;AAEhC,QAAA,OAAA,CAAAA,CAAAA,iBAAA,GAAAzC,UAAA,CAAWlC,KAAO,MAAA,IAAA,IAAA2E,iBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAlBA,iBAAA,CAAkBC,OAAA,KAAWzC,cAAe,CAAAnC,KAAA,CAAA;AACrD,OAAC,CAAA,CAAA;AACD,MAAA,IAAM6E,8CAED9B,YAAa,CAAA;QACZd,WAAWA,SAAU,CAAAjC,KAAA;AACrB8E,QAAAA,YAAA,EAAc1D,aAAapB,KAAM,CAAA+E,MAAA;AACjCC,QAAAA,SAAA,EAAAhB,EAAAA,CAAAA,MAAA,CAAcrD,cAAe,CAAAX,KAAA,EAAA,UAAA,CAAA;OAC9B,CAAA,EACA8C,aAAc,CAAA;AACbmB,QAAAA,OAAO/D,KAAO,KAAA,IAAA,IAAPA,KAAO,KAAPA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAO,CAAA+D,KAAA;QACd/B,YAAYA,UAAW,CAAAlC,KAAA;AACvBiF,QAAAA,aAAA,EAAe7D,aAAapB,KAAM,CAAAkF,OAAA;AAClCC,QAAAA,qBAAA,EAAuB/D,aAAapB,KAAM,CAAAoF,eAAA;AAC1CJ,QAAAA,SAAA,KAAAhB,MAAA,CAAcrD,cAAe,CAAAX,KAAA,EAAA,WAAA,CAAA;QAC7BmC,gBAAgBuC,iBAAkB,CAAA1E,KAAAA;AACpC,OAAC,CAAA,CAbF,CAAA,CAAA;AAiBG,MAAA,IAAAqF,aAAA,GAAgBvE,cAAe,CAAA,QAAA,EAAU+D,aAAa,CAAA,CAAA;AAE5D,MAAA,IAAMS,eAAe,SAAfA,eAAqB;AAAA,QAAA,IAAAC,eAAA,CAAA;QAEnB,IAAAC,MAAA,IAAAD,eAAA,GAASzE,eAAe,QAAU,EAAA2E,eAAA,CAAA,IAAA,EAAA;AAAA,UAAA,OAAA,EAAA,OAAA;AAAA,SAAA,EAAA,IAAA,CAAuB,CAAK,MAAAF,IAAAA,IAAAA,eAAA,KAAAA,KAAAA,CAAAA,GAAAA,eAAA,GAAA,KAAA,CAAA;QACpE,IAAMG,eAAkB,GAAAvC,YAAA,CAAanD,KACjC,GAAA,CAAAgE,EAAAA,CAAAA,MAAA,CAAIrD,cAAA,CAAeX,KAAiB,kBAAAgE,MAAA,CAAGrD,cAAe,CAAAX,KAAA,EAAA,sBAAA,CAAA,CACtD,GAAAgE,EAAAA,CAAAA,MAAA,CAAGrD,cAAe,CAAAX,KAAA,EAAA,UAAA,CAAA,CAAA;QAEtB,IAAM2F,cAAiB,GAAAxC,YAAA,CAAanD,KAChC,GAAA,CAAAgE,EAAAA,CAAAA,MAAA,CAAIrD,cAAA,CAAeX,KAAgB,iBAAAgE,MAAA,CAAGrD,cAAe,CAAAX,KAAA,EAAA,qBAAA,CAAA,CACrD,GAAAgE,EAAAA,CAAAA,MAAA,CAAGrD,cAAe,CAAAX,KAAA,EAAA,SAAA,CAAA,CAAA;AACtB,QAAA,IAAM4F,UAAU,SAAVA,UAAgB;AACpB,UAAA,IAAMC,IAAO,GAAA;YACXC;iCAAsCjF,YAAYb,KAAiB,EAAA,UAAA,CAAA;aAAA,EAAA,IAAA,CAAA;YACnE+F;iCAA0ClF,YAAYb,KAAoB,EAAA,aAAA,CAAA;aAAA,EAAA,IAAA,CAAA;YAC1EgG;iCAAyCnF,YAAYb,KAAkB,EAAA,WAAA,CAAA;aAAA,EAAA,IAAA,CAAA;YACvEiG;iCAA0CpF,YAAYb,KAAoB,EAAA,aAAA,CAAA;AAAA,aAAA,EAAA,IAAA,CAAA;WAC5E,CAAA;UACA,OAAO6F,KAAK3F,KAAO,KAAA,IAAA,IAAPA,KAAO,KAAPA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAO,CAAA+D,KAAA,CAAA,CAAA;SACrB,CAAA;QACA,OAAA,CACGuB,WAAUtF,KAAO,KAAA,IAAA,IAAPA,KAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAPA,KAAO,CAAAgG,QAAA,CAAA,KAAAT,eAAA,CAAA,KAAA,EAAA;AAAA,UAAA,OAAA,EACJC;uBAA8BnC,EAAAA,UAAAA;AAAA,SAAA,EAAA,CAAAkC,eAAA,CAAA,KAAA,EAAA;AAAA,UAAA,OAAA,EAAA,EAAA,CAAAzB,MAAA,CACzBrD,cAAe,CAAAX,KAAA,EAAA,kBAAA,CAAA;AAAA,SAAA,EAAA,CAC3B4F,OAAQ,EAAA,EACRJ,MAAA,CAGFtF,CAAAA,EAAAA,KAAO,KAAPA,IAAAA,IAAAA,KAAO,KAAPA,KAAAA,CAAAA,IAAAA,KAAO,CAAAgG,QAAA,GAAAT,eAAA,CAAA,MAAA,EAAA;AAAA,UAAA,OAAA,EACOE,cAAgB;UAAA,SAASvC,EAAAA,cAAAA;SACnCtC,EAAAA,CAAAA,cAAA,CAAe,uCAAyB,IAAA,EAAA,IAAA,CAAA,CAAA,CAEzC,CAAA,GAAA,IAAA,CAVL,CAAA,CAAA;OAcP,CAAA;AAEA,MAAA,IAAMqF,aAAa,SAAbA,aAAmB;AACjB,QAAA,IAAAC,IAAA,GAAOpF,aAAc,CAAA,SAAA,EAAW,MAAM,CAAA,CAAA;QAC5C,IAAMqF,aACJ,GAAA,CAAAnG,KAAA,KAAA,IAAA,IAAAA,KAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,KAAA,CAAO+D,KAAU,MAAA,SAAA,GAAY,CAAAD,EAAAA,CAAAA,MAAA,CAAIrD,cAAe,CAAAX,KAAA,EAAiB,QAAA,CAAA,CAAA,GAAA,CAAAgE,EAAAA,CAAAA,MAAA,CAAIrD,cAAA,CAAeX,KAAmB,EAAA,cAAA,CAAA,CAAA,CAAA;AACrG,QAAA,IAAAmD,YAAA,CAAanD,SAASqF,aAAe,EAAA;UACzBgB,aAAA,CAAAlC,IAAA,CAAAH,EAAAA,CAAAA,MAAA,CAAQrD,cAAA,CAAeX,KAAyB,EAAA,oBAAA,CAAA,CAAA,CAAA;AAChE,SAAA,MAAA,IAAWmD,aAAanD,KAAO,EAAA;UACfqG,aAAA,CAAAlC,IAAA,CAAAH,EAAAA,CAAAA,MAAA,CAAQrD,cAAA,CAAeX,KAAyC,EAAA,oCAAA,CAAA,CAAA,CAAA;AAChF,SAAA;AACA,QAAA,OAAAyF,eAAA,CAAA,KAAA,EAAA;AAAA,UAAA,OAAA,EACcY;uBAA4B9C,EAAAA,UAAAA;AAAA,SAAA,EAAA,CACrC6C;OAGP,CAAA;AAEA,MAAA,IAAME,eAAe,SAAfA,eAAqB;QACzB,IAAMC,eAAkB,GAAApD,YAAA,CAAanD,KACjC,GAAA,CAAAgE,EAAAA,CAAAA,MAAA,CAAIrD,cAAA,CAAeX,KAAiB,kBAAAgE,MAAA,CAAGrD,cAAe,CAAAX,KAAA,EAAA,sBAAA,CAAA,CACtD,GAAAgE,EAAAA,CAAAA,MAAA,CAAGrD,cAAe,CAAAX,KAAA,EAAA,UAAA,CAAA,CAAA;QAGpB,OAAAqF,aAAA,IAAAI,eAAA,CAAA,KAAA,EAAA;AAAA,UAAA,OAAA,EACcc;uBAA8BhD,EAAAA,UAAAA;AAAA,SAAA,EAAA,CACvC8B,cADF,CAAA,CAAA;OAKP,CAAA;MAGE,OAAAI,eAAA,CAAAe,YAAA,EAAA,IAAA,EAAA,CACGlB,YAAa,EAAA,EACba,UAAW,EAAA,EACX,CAAC,CAACjG,KAAM,CAAAuG,MAAA,IAAUH,YAAa,EAAA,CAAA,CAAA,CAAA;KAGtC,CAAA;IAEO,OAAA,YAAA;MAAA,OAAAI,kBAAA,CAAAjB,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,KAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAGEhF,OAAA;QAAA,OACEsD,EAAAA,WAAY,CAAA/D,KAAA;AAAA,QAAA,OAAA,EACZsE,WAAY,CAAAtE,KAAAA;AAAA,OAAA,EAAA,CAGlByE,UAAA,2CAFYzB,UAAW,CAAAhD,KAAA,IAASE,MAAML,SAEtC,CAAA,CAAA,CAAA,CAAA;KAPF,CAAA;AAUL,GAAA;AACF,CAAC,CAAA;;;;"}