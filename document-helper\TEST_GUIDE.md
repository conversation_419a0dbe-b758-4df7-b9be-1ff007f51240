# 功能测试指南

## 🧪 测试前准备

1. 确保应用正在运行：`npm run dev`
2. 打开浏览器访问：`http://localhost:5173/`
3. 确认页面正常加载，左侧显示目录树，右侧显示内容编辑区

## 📋 功能测试清单

### 1. 添加根目录测试 ✅

**测试步骤**：
1. 点击左侧底部的"添加根目录"按钮
2. 观察是否出现新的章节项
3. 检查新章节是否自动选中（高亮显示）
4. 确认是否自动进入标题编辑模式
5. 输入新标题，按回车确认

**预期结果**：
- 新章节成功添加到目录树
- 新章节自动选中并高亮
- 自动进入编辑模式，输入框聚焦
- 右侧显示新章节的内容
- 标题修改后立即生效

### 2. 添加子章节测试 ✅

**测试步骤**：
1. 选中任意现有章节
2. 悬停鼠标，观察操作按钮出现
3. 点击"+"按钮添加子章节
4. 观察父章节是否自动展开
5. 检查子章节是否自动选中

**预期结果**：
- 子章节成功添加到父章节下
- 父章节自动展开显示子章节
- 新子章节自动选中
- 自动进入标题编辑模式

### 3. 快速编辑标题测试 ✅

**测试步骤**：
1. 双击任意章节标题
2. 观察是否进入编辑模式
3. 修改标题内容
4. 按回车确认或点击其他地方
5. 观察标题是否更新

**预期结果**：
- 双击后立即进入编辑模式
- 输入框自动聚焦并选中原文本
- 修改后标题立即更新
- 右侧内容区标题同步更新

### 4. 详细编辑测试 ✅

**测试步骤**：
1. 悬停在章节上，点击设置图标
2. 在弹出对话框中修改标题和描述
3. 点击确认保存
4. 观察标题是否更新

**预期结果**：
- 对话框正确显示当前标题和描述
- 修改后保存成功
- 标题在目录树中更新
- 描述信息正确保存

### 5. 删除章节测试 ✅

**测试步骤**：
1. 悬停在要删除的章节上
2. 点击删除图标（垃圾桶）
3. 在确认对话框中点击确认
4. 观察章节是否被删除
5. 检查子章节是否一并删除

**预期结果**：
- 出现删除确认对话框
- 确认后章节及所有子章节被删除
- 如果删除的是当前选中章节，右侧内容清空
- 目录树正确更新

### 6. 内容预览测试 ✅

**测试步骤**：
1. 选中任意章节
2. 在右侧编辑区输入内容
3. 观察左侧目录树中的内容预览
4. 修改内容，观察预览是否更新

**预期结果**：
- 目录项下方显示内容摘要
- 摘要显示前50个字符
- 内容修改后预览实时更新
- Markdown标记被正确移除

### 7. 编辑模式切换测试 ✅

**测试步骤**：
1. 选中任意章节
2. 切换编辑、预览、分屏模式
3. 在编辑模式下输入Markdown内容
4. 切换到预览模式查看渲染效果

**预期结果**：
- 三种模式正确切换
- 编辑模式支持文本输入
- 预览模式正确渲染Markdown
- 分屏模式同时显示编辑和预览

## 🔍 边界情况测试

### 1. 空标题处理
- 输入空标题时应保持原标题不变
- 只输入空格时应被忽略

### 2. 特殊字符处理
- 标题包含特殊字符（如：<>&"'）时正确显示
- 内容包含HTML标签时正确处理

### 3. 深层嵌套
- 创建多层嵌套章节（3-5层）
- 确保所有层级都能正常操作

### 4. 快速操作
- 快速连续添加多个章节
- 快速切换不同章节
- 确保操作响应及时且稳定

## 🐛 常见问题排查

### 问题1：添加章节后没有反应
**可能原因**：JavaScript错误或网络问题
**排查方法**：
1. 打开浏览器开发者工具（F12）
2. 查看Console标签页是否有错误信息
3. 刷新页面重试

### 问题2：编辑标题时输入框没有聚焦
**可能原因**：ref绑定问题
**排查方法**：
1. 检查浏览器控制台是否有Vue警告
2. 尝试点击输入框手动聚焦
3. 刷新页面重试

### 问题3：删除操作没有效果
**可能原因**：确认对话框被阻止或JavaScript错误
**排查方法**：
1. 检查浏览器是否阻止了弹窗
2. 查看控制台错误信息
3. 尝试其他章节的删除操作

### 问题4：内容预览不更新
**可能原因**：响应式数据更新问题
**排查方法**：
1. 切换到其他章节再切换回来
2. 刷新页面查看是否恢复
3. 检查控制台是否有Vue相关警告

## ✅ 测试完成标准

所有功能测试通过后，应该能够：
- 流畅地创建和管理文档结构
- 快速编辑章节标题和内容
- 实时预览章节内容摘要
- 安全地删除不需要的章节
- 在不同编辑模式间自由切换

## 📝 测试报告模板

**测试日期**：_______
**测试人员**：_______
**浏览器版本**：_______

| 功能项 | 测试结果 | 备注 |
|--------|----------|------|
| 添加根目录 | ✅/❌ | |
| 添加子章节 | ✅/❌ | |
| 快速编辑标题 | ✅/❌ | |
| 详细编辑 | ✅/❌ | |
| 删除章节 | ✅/❌ | |
| 内容预览 | ✅/❌ | |
| 模式切换 | ✅/❌ | |

**发现的问题**：
1. _______
2. _______

**改进建议**：
1. _______
2. _______
