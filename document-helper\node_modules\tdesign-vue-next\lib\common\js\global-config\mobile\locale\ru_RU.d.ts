import 'dayjs/locale/ru';
declare const _default: {
    actionSheet: {
        cancel: string;
    };
    calendar: {
        confirm: string;
        title: string;
        weekdays: string[];
        monthTitle: string;
        months: string[];
    };
    cascader: {
        title: string;
        placeholder: string;
    };
    dropdownMenu: {
        reset: string;
        confirm: string;
    };
    dateTimePicker: {
        title: string;
        cancel: string;
        confirm: string;
        format: string;
        yearLabel: string;
        monthLabel: string;
        dateLabel: string;
        hourLabel: string;
        minuteLabel: string;
        secondLabel: string;
    };
    picker: {
        cancel: string;
        confirm: string;
    };
    pullDownRefresh: {
        loadingTexts: string[];
    };
    rate: {
        valueText: string;
        noValueText: string;
    };
    tabBar: {
        newsAriaLabel: string;
        moreNewsAriaLabel: string;
        haveMoreNewsAriaLabel: string;
        haveNewsAriaLabel: string;
    };
    table: {
        empty: string;
    };
    list: {
        loading: string;
        loadingMoreText: string;
        pulling: string;
        loosing: string;
        success: string;
    };
    upload: {
        progress: {
            uploadingText: string;
            waitingText: string;
            failText: string;
            successText: string;
        };
    };
    guide: {
        next: string;
        skip: string;
        finish: string;
        back: string;
    };
};
export default _default;
