import { PropType, VNode } from 'vue';
declare const _default: import("vue").DefineComponent<{
    navs: {
        type: PropType<VNode[]>;
    };
    placement: {
        type: PropType<import("./type").TdTabsProps["placement"]>;
        default: import("./type").TdTabsProps["placement"];
        validator(val: import("./type").TdTabsProps["placement"]): boolean;
    };
    value: {
        type: PropType<import("./type").TdTabsProps["value"]>;
        default: import("./type").TdTabsProps["value"];
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    navs: {
        type: PropType<VNode[]>;
    };
    placement: {
        type: PropType<import("./type").TdTabsProps["placement"]>;
        default: import("./type").TdTabsProps["placement"];
        validator(val: import("./type").TdTabsProps["placement"]): boolean;
    };
    value: {
        type: PropType<import("./type").TdTabsProps["value"]>;
        default: import("./type").TdTabsProps["value"];
    };
}>>, {
    value: import("./type").TabValue;
    placement: "left" | "right" | "top" | "bottom";
}, {}>;
export default _default;
