import { TdRangeInputPopupProps } from './type';
declare const _default: import("vue").DefineComponent<{
    autoWidth: BooleanConstructor;
    disabled: BooleanConstructor;
    inputValue: {
        type: import("vue").PropType<TdRangeInputPopupProps["inputValue"]>;
        default: TdRangeInputPopupProps["inputValue"];
    };
    defaultInputValue: {
        type: import("vue").PropType<TdRangeInputPopupProps["defaultInputValue"]>;
    };
    label: {
        type: import("vue").PropType<TdRangeInputPopupProps["label"]>;
    };
    panel: {
        type: import("vue").PropType<TdRangeInputPopupProps["panel"]>;
    };
    popupProps: {
        type: import("vue").PropType<TdRangeInputPopupProps["popupProps"]>;
    };
    popupVisible: BooleanConstructor;
    rangeInputProps: {
        type: import("vue").PropType<TdRangeInputPopupProps["rangeInputProps"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    status: {
        type: import("vue").PropType<TdRangeInputPopupProps["status"]>;
        default: TdRangeInputPopupProps["status"];
        validator(val: TdRangeInputPopupProps["status"]): boolean;
    };
    tips: {
        type: import("vue").PropType<TdRangeInputPopupProps["tips"]>;
    };
    onInputChange: import("vue").PropType<TdRangeInputPopupProps["onInputChange"]>;
    onPopupVisibleChange: import("vue").PropType<TdRangeInputPopupProps["onPopupVisibleChange"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    autoWidth: BooleanConstructor;
    disabled: BooleanConstructor;
    inputValue: {
        type: import("vue").PropType<TdRangeInputPopupProps["inputValue"]>;
        default: TdRangeInputPopupProps["inputValue"];
    };
    defaultInputValue: {
        type: import("vue").PropType<TdRangeInputPopupProps["defaultInputValue"]>;
    };
    label: {
        type: import("vue").PropType<TdRangeInputPopupProps["label"]>;
    };
    panel: {
        type: import("vue").PropType<TdRangeInputPopupProps["panel"]>;
    };
    popupProps: {
        type: import("vue").PropType<TdRangeInputPopupProps["popupProps"]>;
    };
    popupVisible: BooleanConstructor;
    rangeInputProps: {
        type: import("vue").PropType<TdRangeInputPopupProps["rangeInputProps"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    status: {
        type: import("vue").PropType<TdRangeInputPopupProps["status"]>;
        default: TdRangeInputPopupProps["status"];
        validator(val: TdRangeInputPopupProps["status"]): boolean;
    };
    tips: {
        type: import("vue").PropType<TdRangeInputPopupProps["tips"]>;
    };
    onInputChange: import("vue").PropType<TdRangeInputPopupProps["onInputChange"]>;
    onPopupVisibleChange: import("vue").PropType<TdRangeInputPopupProps["onPopupVisibleChange"]>;
}>>, {
    disabled: boolean;
    status: "error" | "default" | "success" | "warning";
    readonly: boolean;
    autoWidth: boolean;
    inputValue: import("./type").RangeInputValue;
    popupVisible: boolean;
}, {}>;
export default _default;
