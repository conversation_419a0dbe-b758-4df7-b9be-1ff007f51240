{"version": 3, "file": "form.js", "sources": ["../../../components/form/form.tsx"], "sourcesContent": ["import { computed, defineComponent, provide, reactive, ref, toRefs } from 'vue';\nimport { isEmpty, isArray, isBoolean, isFunction } from 'lodash-es';\n\nimport { requestSubmit } from '@tdesign/shared-utils';\nimport { FormItemValidateResult } from './form-item';\nimport {\n  Data,\n  FormResetParams,\n  FormValidateMessage,\n  FormValidateParams,\n  FormValidateResult,\n  TdFormProps,\n  ValidateResultList,\n} from './type';\nimport props from './props';\nimport { FormInjectionKey, FormItemContext, useCLASSNAMES } from './consts';\nimport { FormResetEvent, FormSubmitEvent } from '../common';\nimport { getFormItemClassName } from './utils';\nimport { FormDisabledProvider, FormReadonlyProvider } from './hooks';\nimport { usePrefixClass, useTNodeJSX } from '@tdesign/shared-hooks';\n\ntype Result = FormValidateResult<TdFormProps['data']>;\n\nexport default defineComponent({\n  name: 'TForm',\n  props,\n  setup(props, { expose }) {\n    const renderContent = useTNodeJSX();\n    const { disabled, readonly } = toRefs(props);\n    provide<FormDisabledProvider>('formDisabled', {\n      disabled,\n    });\n    provide<FormReadonlyProvider>('formReadonly', {\n      readonly,\n    });\n\n    const formRef = ref<HTMLFormElement>(null);\n    const children = ref<FormItemContext[]>([]);\n\n    const {\n      showErrorMessage,\n      labelWidth,\n      labelAlign,\n      data,\n      colon,\n      requiredMark,\n      requiredMarkPosition,\n      rules,\n      errorMessage,\n      resetType,\n    } = toRefs(props);\n    provide(\n      FormInjectionKey,\n      reactive({\n        showErrorMessage,\n        labelWidth,\n        labelAlign,\n        data,\n        colon,\n        requiredMark,\n        requiredMarkPosition,\n        rules,\n        errorMessage,\n        resetType,\n        children,\n        renderContent,\n      }),\n    );\n\n    const COMPONENT_NAME = usePrefixClass('form');\n    const CLASS_NAMES = useCLASSNAMES();\n    const formClass = computed(() => [\n      CLASS_NAMES.value.form,\n      { [`${COMPONENT_NAME.value}-inline`]: props.layout === 'inline' },\n    ]);\n\n    const FORM_ITEM_CLASS_PREFIX = usePrefixClass('form-item');\n\n    const getFirstError = (result: Result) => {\n      if (isBoolean(result)) return '';\n      const [firstKey] = Object.keys(result);\n      if (props.scrollToFirstError) {\n        const tmpClassName = getFormItemClassName(FORM_ITEM_CLASS_PREFIX.value, firstKey);\n        scrollTo(tmpClassName);\n      }\n      const resArr = result[firstKey] as ValidateResultList;\n      if (!isArray(resArr)) return '';\n      return resArr.filter((item) => !item.result)[0].message;\n    };\n    // 校验不通过时，滚动到第一个错误表单\n    const scrollTo = (selector: string) => {\n      const [dom] = formRef.value.getElementsByClassName(selector);\n      const behavior = props.scrollToFirstError;\n      if (behavior) {\n        dom && dom.scrollIntoView({ behavior });\n      }\n    };\n\n    const needValidate = (name: string | number, fields: string[] | undefined) => {\n      if (!fields || !isArray(fields)) return true;\n      return fields.indexOf(`${name}`) !== -1;\n    };\n    const formatValidateResult = <T extends Data>(validateResultList: FormItemValidateResult<T>[]) => {\n      const result: Record<string, any> = validateResultList.reduce((r, err) => Object.assign(r || {}, err), {});\n      Object.keys(result).forEach((key) => {\n        if (result[key] === true) {\n          delete result[key];\n        }\n      });\n      return isEmpty(result) ? true : result;\n    };\n    const validate = async (param?: FormValidateParams): Promise<Result> => {\n      const { fields, trigger = 'all', showErrorMessage } = param || {};\n      const list = children.value\n        .filter((child) => isFunction(child.validate) && needValidate(String(child.name), fields))\n        .map((child) => child.validate(trigger, showErrorMessage));\n      const arr = await Promise.all(list);\n      const result = formatValidateResult(arr);\n      const firstError = getFirstError(result);\n      props.onValidate?.({\n        validateResult: result,\n        firstError,\n      });\n      return result;\n    };\n    const validateOnly = async (params?: Omit<FormValidateParams, 'showErrorMessage'>) => {\n      const { fields, trigger = 'all' } = params || {};\n      const list = children.value\n        .filter((child) => isFunction(child.validateOnly) && needValidate(String(child.name), fields))\n        .map((child) => child.validateOnly(trigger));\n      const arr = await Promise.all(list);\n      return formatValidateResult(arr);\n    };\n    const submitParams = ref<Pick<FormValidateParams, 'showErrorMessage'>>();\n    const onSubmit = (e?: FormSubmitEvent) => {\n      if (props.preventSubmitDefault && e) {\n        e.preventDefault();\n        e.stopPropagation();\n      }\n      validate(submitParams.value).then((r) => {\n        props.onSubmit?.({ validateResult: r, firstError: getFirstError(r), e });\n      });\n      submitParams.value = undefined;\n    };\n    const submit = async (params?: Pick<FormValidateParams, 'showErrorMessage'>) => {\n      submitParams.value = params;\n      requestSubmit(formRef.value);\n    };\n\n    const resetParams = ref<FormResetParams<Data>>();\n    const onReset = (e?: FormResetEvent) => {\n      if (props.preventSubmitDefault && e) {\n        e.preventDefault();\n        e.stopPropagation();\n      }\n      children.value\n        .filter(\n          (child) =>\n            isFunction(child.resetField) && needValidate(String(child.name), resetParams.value?.fields as string[]),\n        )\n        .forEach((child) => child.resetField(resetParams.value?.type));\n      resetParams.value = undefined;\n      props.onReset?.({ e });\n    };\n    const reset = <FormData extends Data>(params?: FormResetParams<FormData>) => {\n      (resetParams.value as any) = params;\n      formRef.value.reset();\n    };\n\n    const clearValidate = (fields?: Array<string>) => {\n      children.value.forEach((child) => {\n        if (isFunction(child.resetHandler) && needValidate(String(child.name), fields)) {\n          child.resetHandler();\n        }\n      });\n    };\n    const setValidateMessage = (validateMessage: FormValidateMessage<FormData>) => {\n      const keys = Object.keys(validateMessage);\n      if (!keys.length) return;\n      const list = children.value\n        .filter((child) => isFunction(child.setValidateMessage) && keys.includes(`${child.name}`))\n        .map((child) => child.setValidateMessage(validateMessage[child.name as keyof FormData]));\n      Promise.all(list);\n    };\n\n    expose({ validate, submit, reset, clearValidate, setValidateMessage, validateOnly });\n\n    return () => (\n      <form\n        id={props.id}\n        ref={formRef}\n        class={formClass.value}\n        onSubmit={(e) => onSubmit(e)}\n        onReset={(e) => onReset(e)}\n      >\n        {renderContent('default')}\n      </form>\n    );\n  },\n});\n"], "names": ["defineComponent", "name", "props", "setup", "expose", "_ref", "renderContent", "useTNodeJSX", "_toRefs", "toRefs", "disabled", "readonly", "provide", "formRef", "ref", "children", "_toRefs2", "showErrorMessage", "labelWidth", "labelAlign", "data", "colon", "requiredMark", "requiredMarkPosition", "rules", "errorMessage", "resetType", "FormInjectionKey", "reactive", "COMPONENT_NAME", "usePrefixClass", "CLASS_NAMES", "useCLASSNAMES", "formClass", "computed", "value", "form", "_defineProperty", "concat", "layout", "FORM_ITEM_CLASS_PREFIX", "getFirstError", "result", "isBoolean", "_Object$keys", "Object", "keys", "_Object$keys2", "_slicedToArray", "firstKey", "scrollToFirstError", "tmpClassName", "getFormItemClassName", "scrollTo", "resArr", "isArray", "filter", "item", "message", "selector", "_formRef$value$getEle", "getElementsByClassName", "_formRef$value$getEle2", "dom", "behavior", "scrollIntoView", "needValidate", "fields", "indexOf", "formatValidateResult", "validateResultList", "reduce", "r", "err", "assign", "for<PERSON>ach", "key", "isEmpty", "validate", "_ref3", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "param", "_props2$onValidate", "_ref4", "_ref4$trigger", "trigger", "showErrorMessage2", "list", "arr", "firstError", "wrap", "_context", "prev", "next", "child", "isFunction", "String", "map", "Promise", "all", "sent", "onValidate", "validateResult", "abrupt", "stop", "_x", "apply", "arguments", "validateOnly", "_ref5", "_callee2", "params", "_ref6", "_ref6$trigger", "_context2", "_x2", "submitParams", "onSubmit", "e", "preventSubmitDefault", "preventDefault", "stopPropagation", "then", "_props2$onSubmit", "submit", "_ref7", "_callee3", "_context3", "requestSubmit", "_x3", "resetParams", "onReset", "_props2$onReset", "_resetParams$value", "reset<PERSON>ield", "_resetParams$value2", "type", "reset", "clearValidate", "re<PERSON><PERSON><PERSON><PERSON>", "setValidateMessage", "validateMessage", "length", "includes", "_createVNode", "id"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,YAAeA,mBAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,OAAA;AACNC,EAAAA,KAAA,EAAAA,qBAAA;AACAC,EAAAA,KAAMD,WAANC,KAAMD,CAAAA,MAAAA,EAAAA,IAAAA,EAAmB;AAAA,IAAA,IAAVE,MAAA,GAAAC,IAAA,CAAAD,MAAA,CAAA;AACb,IAAA,IAAME,gBAAgBC,iBAAY,EAAA,CAAA;AAClC,IAAA,IAAAC,OAAA,GAA+BC,WAAOP,MAAK,CAAA;MAAnCQ,QAAA,GAAAF,OAAA,CAAAE,QAAA;MAAUC,QAAS,GAAAH,OAAA,CAATG,QAAS,CAAA;IAC3BC,WAAA,CAA8B,cAAgB,EAAA;AAC5CF,MAAAA,QAAA,EAAAA,QAAAA;AACF,KAAC,CAAA,CAAA;IACDE,WAAA,CAA8B,cAAgB,EAAA;AAC5CD,MAAAA,QAAA,EAAAA,QAAAA;AACF,KAAC,CAAA,CAAA;AAEK,IAAA,IAAAE,OAAA,GAAUC,QAAqB,IAAI,CAAA,CAAA;AACnC,IAAA,IAAAC,QAAA,GAAWD,OAAuB,CAAA,EAAE,CAAA,CAAA;AAEpC,IAAA,IAAAE,QAAA,GAWFP,WAAOP,MAAK,CAAA;MAVde,gBAAA,GAAAD,QAAA,CAAAC,gBAAA;MACAC,UAAA,GAAAF,QAAA,CAAAE,UAAA;MACAC,UAAA,GAAAH,QAAA,CAAAG,UAAA;MACAC,IAAA,GAAAJ,QAAA,CAAAI,IAAA;MACAC,KAAA,GAAAL,QAAA,CAAAK,KAAA;MACAC,YAAA,GAAAN,QAAA,CAAAM,YAAA;MACAC,oBAAA,GAAAP,QAAA,CAAAO,oBAAA;MACAC,KAAA,GAAAR,QAAA,CAAAQ,KAAA;MACAC,YAAA,GAAAT,QAAA,CAAAS,YAAA;MACAC,SAAA,GAAAV,QAAA,CAAAU,SAAA,CAAA;AAEFd,IAAAA,WAAA,CACEe,kCAAA,EACAC,YAAS,CAAA;AACPX,MAAAA,gBAAA,EAAAA,gBAAA;AACAC,MAAAA,UAAA,EAAAA,UAAA;AACAC,MAAAA,UAAA,EAAAA,UAAA;AACAC,MAAAA,IAAA,EAAAA,IAAA;AACAC,MAAAA,KAAA,EAAAA,KAAA;AACAC,MAAAA,YAAA,EAAAA,YAAA;AACAC,MAAAA,oBAAA,EAAAA,oBAAA;AACAC,MAAAA,KAAA,EAAAA,KAAA;AACAC,MAAAA,YAAA,EAAAA,YAAA;AACAC,MAAAA,SAAA,EAAAA,SAAA;AACAX,MAAAA,QAAA,EAAAA,QAAA;AACAT,MAAAA,aAAA,EAAAA,aAAAA;AACF,KAAC,CACH,CAAA,CAAA;AAEM,IAAA,IAAAuB,cAAA,GAAiBC,uBAAe,MAAM,CAAA,CAAA;AAC5C,IAAA,IAAMC,cAAcC,+BAAc,EAAA,CAAA;IAC5B,IAAAC,SAAA,GAAYC,aAAS,YAAA;MAAA,OAAM,CAC/BH,YAAYI,KAAM,CAAAC,IAAA,EAAAC,mCAAA,CAAAC,EAAAA,EAAAA,EAAAA,CAAAA,MAAA,CACZT,eAAeM,KAAiBjC,EAAAA,SAAAA,CAAAA,EAAAA,MAAAA,CAAMqC,WAAW,QAAS,CACjE,CAAA,CAAA;KAAA,CAAA,CAAA;AAEK,IAAA,IAAAC,sBAAA,GAAyBV,uBAAe,WAAW,CAAA,CAAA;AAEnD,IAAA,IAAAW,aAAA,GAAgB,SAAhBA,aAAAA,CAAiBC,MAAmB,EAAA;AACxC,MAAA,IAAIC,oBAAUD,MAAM,CAAA,EAAU,OAAA,EAAA,CAAA;AAC9B,MAAA,IAAAE,YAAA,GAAmBC,MAAA,CAAOC,KAAKJ,MAAM,CAAA;QAAAK,aAAA,GAAAC,kCAAA,CAAAJ,YAAA,EAAA,CAAA,CAAA;AAA9BK,QAAAA,QAAQ,GAAAF,aAAA,CAAA,CAAA,CAAA,CAAA;MACf,IAAI7C,OAAMgD,kBAAoB,EAAA;QAC5B,IAAMC,YAAe,GAAAC,wCAAA,CAAqBZ,sBAAuB,CAAAL,KAAA,EAAOc,QAAQ,CAAA,CAAA;QAChFI,QAAA,CAASF,YAAY,CAAA,CAAA;AACvB,OAAA;AACA,MAAA,IAAMG,SAASZ,MAAO,CAAAO,QAAA,CAAA,CAAA;AAClB,MAAA,IAAA,CAACM,gBAAQD,MAAM,CAAA,EAAU,OAAA,EAAA,CAAA;AACtB,MAAA,OAAAA,MAAA,CAAOE,OAAO,UAACC,IAAA,EAAA;QAAA,OAAS,CAACA,IAAK,CAAAf,MAAM,CAAA;AAAA,OAAA,EAAE,CAAG,CAAA,CAAAgB,OAAA,CAAA;KAClD,CAAA;AAEM,IAAA,IAAAL,QAAA,GAAW,SAAXA,QAAAA,CAAYM,QAAqB,EAAA;MACrC,IAAAC,qBAAA,GAAc/C,OAAQ,CAAAsB,KAAA,CAAM0B,uBAAuBF,QAAQ,CAAA;QAAAG,sBAAA,GAAAd,kCAAA,CAAAY,qBAAA,EAAA,CAAA,CAAA;AAApDG,QAAAA,GAAG,GAAAD,sBAAA,CAAA,CAAA,CAAA,CAAA;AACV,MAAA,IAAME,WAAW9D,MAAM,CAAAgD,kBAAA,CAAA;AACvB,MAAA,IAAIc,QAAU,EAAA;AACZD,QAAAA,GAAA,IAAOA,GAAI,CAAAE,cAAA,CAAe;AAAED,UAAAA,QAAA,EAAAA,QAAAA;AAAS,SAAC,CAAA,CAAA;AACxC,OAAA;KACF,CAAA;IAEM,IAAAE,YAAA,GAAe,SAAfA,YAAAA,CAAgBjE,IAAA,EAAuBkE,MAAiC,EAAA;MAC5E,IAAI,CAACA,MAAA,IAAU,CAACZ,eAAA,CAAQY,MAAM,CAAA,EAAU,OAAA,IAAA,CAAA;MACxC,OAAOA,MAAO,CAAAC,OAAA,CAAA9B,EAAAA,CAAAA,MAAA,CAAWrC,IAAA,CAAM,CAAM,KAAA,CAAA,CAAA,CAAA;KACvC,CAAA;AACM,IAAA,IAAAoE,oBAAA,GAAuB,SAAvBA,oBAAAA,CAAwCC,kBAAoD,EAAA;MAChG,IAAM5B,MAA8B,GAAA4B,kBAAA,CAAmBC,MAAO,CAAA,UAACC,GAAGC,GAAQ,EAAA;QAAA,OAAA5B,MAAA,CAAO6B,MAAO,CAAAF,CAAA,IAAK,EAAC,EAAGC,GAAG,CAAA,CAAA;OAAG,EAAA,EAAE,CAAA,CAAA;MACzG5B,MAAA,CAAOC,IAAK,CAAAJ,MAAM,CAAE,CAAAiC,OAAA,CAAQ,UAACC,GAAQ,EAAA;AAC/B,QAAA,IAAAlC,MAAA,CAAOkC,SAAS,IAAM,EAAA;UACxB,OAAOlC,MAAO,CAAAkC,GAAA,CAAA,CAAA;AAChB,SAAA;AACF,OAAC,CAAA,CAAA;AACM,MAAA,OAAAC,eAAA,CAAQnC,MAAM,CAAA,GAAI,IAAO,GAAAA,MAAA,CAAA;KAClC,CAAA;AACM,IAAA,IAAAoC,QAAA,gBAAA,YAAA;MAAA,IAAAC,KAAA,GAAAC,qCAAA,cAAAC,uCAAA,CAAAC,IAAA,CAAW,SAAAC,OAAAA,CAAOC,KAAgD,EAAA;AAAA,QAAA,IAAAC,kBAAA,CAAA;AAAA,QAAA,IAAAC,KAAA,EAAAnB,MAAA,EAAAoB,aAAA,EAAAC,OAAA,EAAAC,iBAAA,EAAAC,IAAA,EAAAC,GAAA,EAAAjD,MAAA,EAAAkD,UAAA,CAAA;AAAA,QAAA,OAAAX,uCAAA,CAAAY,IAAA,CAAA,UAAAC,QAAA,EAAA;AAAA,UAAA,OAAA,CAAA,EAAA,QAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;AAAA,YAAA,KAAA,CAAA;AAAAV,cAAAA,KAAA,GAChBF,SAAS,EAAC,EAAxDjB,eAAAA,8BAAQqB,OAAU,EAAVA,OAAU,GAAAD,aAAA,KAAA,KAAA,CAAA,GAAA,KAAA,GAAAA,aAAA,EAAOtE,iBAAiB,GAAAqE,KAAA,CAAjBrE;cAC3ByE,IAAA,GAAO3E,QAAS,CAAAoB,KAAA,CACnBqB,MAAO,CAAA,UAACyC;uBAAUC,qBAAW,CAAAD,KAAA,CAAMnB,QAAQ,CAAA,IAAKZ,YAAa,CAAAiC,MAAA,CAAOF,MAAMhG,IAAI,CAAA,EAAGkE,MAAM,CAAC,CAAA;AAAA,eAAA,CACxF,CAAAiC,GAAA,CAAI,UAACH,KAAA,EAAA;AAAA,gBAAA,OAAUA,KAAM,CAAAnB,QAAA,CAASU,OAASvE,EAAAA,iBAAgB,CAAC,CAAA;eAAA,CAAA,CAAA;AAAA6E,cAAAA,QAAA,CAAAE,IAAA,GAAA,CAAA,CAAA;AAAA,cAAA,OACzCK,OAAQ,CAAAC,GAAA,CAAIZ,IAAI,CAAA,CAAA;AAAA,YAAA,KAAA,CAAA;cAA5BC,GAAM,GAAAG,QAAA,CAAAS,IAAA,CAAA;AACN7D,cAAAA,MAAA,GAAS2B,qBAAqBsB,GAAG,CAAA,CAAA;AACjCC,cAAAA,UAAA,GAAanD,cAAcC,MAAM,CAAA,CAAA;AACvCxC,cAAAA,CAAAA,kBAAAA,GAAAA,OAAMsG,UAAa,MAAA,IAAA,IAAAnB,kBAAA,KAAA,KAAA,CAAA,IAAnBnF,kBAAAA,CAAAA,IAAAA,CAAAA,QAAmB;AACjBuG,gBAAAA,cAAgB,EAAA/D,MAAA;AAChBkD,gBAAAA,UAAA,EAAAA,UAAAA;AACF,eAAC,CAAA,CAAA;AAAA,cAAA,OAAAE,QAAA,CAAAY,MAAA,CAAA,QAAA,EACMhE,MAAA,CAAA,CAAA;AAAA,YAAA,KAAA,CAAA,CAAA;AAAA,YAAA,KAAA,KAAA;cAAA,OAAAoD,QAAA,CAAAa,IAAA,EAAA,CAAA;AAAA,WAAA;AAAA,SAAA,EAAAxB,OAAA,CAAA,CAAA;OACT,CAAA,CAAA,CAAA;MAAA,OAbML,SAAAA,QAAAA,CAAA8B,EAAA,EAAA;AAAA,QAAA,OAAA7B,KAAA,CAAA8B,KAAA,CAAA,IAAA,EAAAC,SAAA,CAAA,CAAA;AAAA,OAAA,CAAA;KAaN,EAAA,CAAA;AACM,IAAA,IAAAC,YAAA,gBAAA,YAAA;MAAA,IAAAC,KAAA,GAAAhC,qCAAA,cAAAC,uCAAA,CAAAC,IAAA,CAAe,SAAA+B,QAAAA,CAAOC,MAA0D,EAAA;QAAA,IAAAC,KAAA,EAAAhD,MAAA,EAAAiD,aAAA,EAAA5B,OAAA,EAAAE,IAAA,EAAAC,GAAA,CAAA;AAAA,QAAA,OAAAV,uCAAA,CAAAY,IAAA,CAAA,UAAAwB,SAAA,EAAA;AAAA,UAAA,OAAA,CAAA,EAAA,QAAAA,SAAA,CAAAtB,IAAA,GAAAsB,SAAA,CAAArB,IAAA;AAAA,YAAA,KAAA,CAAA;cAAAmB,KAAA,GAChDD,UAAU,EAAC,EAAvC/C,MAAQ,GAAAgD,KAAA,CAARhD,MAAQ,EAAAiD,aAAA,GAAAD,KAAA,CAAA3B,OAAA,EAAAA,OAAA,GAAA4B,aAAA,KAAA,KAAA,CAAA,GAAU,KAAM,GAAAA,aAAA,CAAA;cAC1B1B,IAAA,GAAO3E,QAAS,CAAAoB,KAAA,CACnBqB,MAAO,CAAA,UAACyC;uBAAUC,qBAAW,CAAAD,KAAA,CAAMc,YAAY,CAAA,IAAK7C,YAAa,CAAAiC,MAAA,CAAOF,MAAMhG,IAAI,CAAA,EAAGkE,MAAM,CAAC,CAAA;AAAA,eAAA,CAC5F,CAAAiC,GAAA,CAAI,UAACH,KAAU,EAAA;AAAA,gBAAA,OAAAA,KAAA,CAAMc,YAAa,CAAAvB,OAAO,CAAC,CAAA;eAAA,CAAA,CAAA;AAAA6B,cAAAA,SAAA,CAAArB,IAAA,GAAA,CAAA,CAAA;AAAA,cAAA,OAC3BK,OAAQ,CAAAC,GAAA,CAAIZ,IAAI,CAAA,CAAA;AAAA,YAAA,KAAA,CAAA;cAA5BC,GAAM,GAAA0B,SAAA,CAAAd,IAAA,CAAA;AAAA,cAAA,OAAAc,SAAA,CAAAX,MAAA,WACLrC,qBAAqBsB,GAAG,CAAA,CAAA,CAAA;AAAA,YAAA,KAAA,CAAA,CAAA;AAAA,YAAA,KAAA,KAAA;cAAA,OAAA0B,SAAA,CAAAV,IAAA,EAAA,CAAA;AAAA,WAAA;AAAA,SAAA,EAAAM,QAAA,CAAA,CAAA;OACjC,CAAA,CAAA,CAAA;MAAA,OAPMF,SAAAA,YAAAA,CAAAO,GAAA,EAAA;AAAA,QAAA,OAAAN,KAAA,CAAAH,KAAA,CAAA,IAAA,EAAAC,SAAA,CAAA,CAAA;AAAA,OAAA,CAAA;KAON,EAAA,CAAA;AACA,IAAA,IAAMS,eAAezG,OAAkD,EAAA,CAAA;AACjE,IAAA,IAAA0G,SAAA,GAAW,SAAXA,QAAAA,CAAYC,CAAwB,EAAA;AACpCvH,MAAAA,IAAAA,MAAAA,CAAMwH,wBAAwBD,CAAG,EAAA;QACnCA,CAAA,CAAEE,cAAe,EAAA,CAAA;QACjBF,CAAA,CAAEG,eAAgB,EAAA,CAAA;AACpB,OAAA;MACA9C,QAAA,CAASyC,YAAa,CAAApF,KAAK,CAAE,CAAA0F,IAAA,CAAK,UAACrD,CAAM,EAAA;AAAA,QAAA,IAAAsD,gBAAA,CAAA;AACvC5H,QAAAA,CAAAA,gBAAAA,GAAAA,MAAAA,CAAMsH,QAAW,MAAA,IAAA,IAAAM,gBAAA,KAAA,KAAA,CAAA,IAAjB5H,gBAAAA,CAAAA,IAAAA,CAAAA,MAAAA,EAAiB;AAAEuG,UAAAA,cAAgB,EAAAjC,CAAA;AAAGoB,UAAAA,YAAYnD,aAAc,CAAA+B,CAAC,CAAG;AAAAiD,UAAAA,CAAA,EAAAA,CAAAA;AAAE,SAAC,CAAA,CAAA;AACzE,OAAC,CAAA,CAAA;AACDF,MAAAA,YAAA,CAAapF,KAAQ,GAAA,KAAA,CAAA,CAAA;KACvB,CAAA;AACM,IAAA,IAAA4F,MAAA,gBAAA,YAAA;MAAA,IAAAC,KAAA,GAAAhD,qCAAA,cAAAC,uCAAA,CAAAC,IAAA,CAAS,SAAA+C,QAAAA,CAAOf,MAA0D,EAAA;AAAA,QAAA,OAAAjC,uCAAA,CAAAY,IAAA,CAAA,UAAAqC,SAAA,EAAA;AAAA,UAAA,OAAA,CAAA,EAAA,QAAAA,SAAA,CAAAnC,IAAA,GAAAmC,SAAA,CAAAlC,IAAA;AAAA,YAAA,KAAA,CAAA;cAC9EuB,YAAA,CAAapF,KAAQ,GAAA+E,MAAA,CAAA;AACrBiB,cAAAA,iBAAA,CAActH,QAAQsB,KAAK,CAAA,CAAA;AAAA,YAAA,KAAA,CAAA,CAAA;AAAA,YAAA,KAAA,KAAA;cAAA,OAAA+F,SAAA,CAAAvB,IAAA,EAAA,CAAA;AAAA,WAAA;AAAA,SAAA,EAAAsB,QAAA,CAAA,CAAA;OAC7B,CAAA,CAAA,CAAA;MAAA,OAHMF,SAAAA,MAAAA,CAAAK,GAAA,EAAA;AAAA,QAAA,OAAAJ,KAAA,CAAAnB,KAAA,CAAA,IAAA,EAAAC,SAAA,CAAA,CAAA;AAAA,OAAA,CAAA;KAGN,EAAA,CAAA;AAEA,IAAA,IAAMuB,cAAcvH,OAA2B,EAAA,CAAA;AACzC,IAAA,IAAAwH,QAAA,GAAU,SAAVA,OAAAA,CAAWb,CAAuB,EAAA;AAAA,MAAA,IAAAc,eAAA,CAAA;AAClCrI,MAAAA,IAAAA,MAAAA,CAAMwH,wBAAwBD,CAAG,EAAA;QACnCA,CAAA,CAAEE,cAAe,EAAA,CAAA;QACjBF,CAAA,CAAEG,eAAgB,EAAA,CAAA;AACpB,OAAA;AACA7G,MAAAA,QAAA,CAASoB,KACN,CAAAqB,MAAA,CACC,UAACyC,KAAA,EAAA;AAAA,QAAA,IAAAuC,kBAAA,CAAA;QAAA,OACCtC,qBAAW,CAAAD,KAAA,CAAMwC,UAAU,CAAA,IAAKvE,YAAa,CAAAiC,MAAA,CAAOF,KAAM,CAAAhG,IAAI,CAAG,EAAAuI,CAAAA,kBAAA,GAAAH,WAAA,CAAYlG,0CAAZqG,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,kBAAA,CAAmBrE,MAAkB,CAAA,CAAA;AAAA,OAC1G,CAAA,CACCQ,QAAQ,UAACsB,KAAA,EAAA;AAAA,QAAA,IAAAyC,mBAAA,CAAA;AAAA,QAAA,OAAUzC,MAAMwC,UAAW,CAAA,CAAAC,mBAAA,GAAAL,WAAA,CAAYlG,KAAO,cAAAuG,mBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAnBA,mBAAA,CAAmBC,IAAI,CAAC,CAAA;OAAA,CAAA,CAAA;AAC/DN,MAAAA,WAAA,CAAYlG,KAAQ,GAAA,KAAA,CAAA,CAAA;AACpBjC,MAAAA,CAAAA,eAAAA,GAAAA,MAAM,CAAAoI,OAAA,MAAA,IAAA,IAAAC,eAAA,KAAA,KAAA,CAAA,IAANrI,eAAAA,CAAAA,IAAAA,CAAAA,MAAM,EAAU;AAAEuH,QAAAA,CAAA,EAAAA,CAAAA;AAAE,OAAC,CAAA,CAAA;KACvB,CAAA;AACM,IAAA,IAAAmB,KAAA,GAAQ,SAARA,KAAAA,CAAgC1B,MAAuC,EAAA;MAC1EmB,YAAYlG,KAAgB,GAAA+E,MAAA,CAAA;AAC7BrG,MAAAA,OAAA,CAAQsB,MAAMyG,KAAM,EAAA,CAAA;KACtB,CAAA;AAEM,IAAA,IAAAC,aAAA,GAAgB,SAAhBA,aAAAA,CAAiB1E,MAA2B,EAAA;AACvCpD,MAAAA,QAAA,CAAAoB,KAAA,CAAMwC,OAAQ,CAAA,UAACsB,KAAU,EAAA;AAC5B,QAAA,IAAAC,qBAAA,CAAWD,KAAM,CAAA6C,YAAY,CAAK,IAAA5E,YAAA,CAAaiC,OAAOF,KAAM,CAAAhG,IAAI,CAAG,EAAAkE,MAAM,CAAG,EAAA;UAC9E8B,KAAA,CAAM6C,YAAa,EAAA,CAAA;AACrB,SAAA;AACF,OAAC,CAAA,CAAA;KACH,CAAA;AACM,IAAA,IAAAC,kBAAA,GAAqB,SAArBA,kBAAAA,CAAsBC,eAAmD,EAAA;AACvE,MAAA,IAAAlG,IAAA,GAAOD,MAAO,CAAAC,IAAA,CAAKkG,eAAe,CAAA,CAAA;AACxC,MAAA,IAAI,CAAClG,IAAK,CAAAmG,MAAA,EAAQ,OAAA;MACZ,IAAAvD,IAAA,GAAO3E,QAAS,CAAAoB,KAAA,CACnBqB,MAAO,CAAA,UAACyC;eAAUC,qBAAW,CAAAD,KAAA,CAAM8C,kBAAkB,CAAA,IAAKjG,IAAK,CAAAoG,QAAA,IAAA5G,MAAA,CAAY2D,KAAM,CAAAhG,IAAA,CAAM,CAAC,CAAA;AAAA,OAAA,CAAA,CACxFmG,GAAI,CAAA,UAACH,KAAU,EAAA;QAAA,OAAAA,KAAA,CAAM8C,kBAAmB,CAAAC,eAAA,CAAgB/C,KAAM,CAAAhG,IAAA,CAAuB,CAAC,CAAA;OAAA,CAAA,CAAA;AACzFoG,MAAAA,OAAA,CAAQC,IAAIZ,IAAI,CAAA,CAAA;KAClB,CAAA;AAEAtF,IAAAA,MAAA,CAAO;AAAE0E,MAAAA,QAAU,EAAVA,QAAU;AAAAiD,MAAAA,MAAA,EAAAA,MAAA;AAAQa,MAAAA,OAAAA;AAAOC,MAAAA,aAAe,EAAfA,aAAe;AAAAE,MAAAA,kBAAA,EAAAA,kBAAA;AAAoBhC,MAAAA,cAAAA,YAAAA;AAAa,KAAC,CAAA,CAAA;IAE5E,OAAA,YAAA;AAAA,MAAA,OAAAoC,eAAA,CAAA,MAAA,EAAA;QAAA,IAECjJ,EAAAA,MAAAA,CAAMkJ,EACV;AAAA,QAAA,KAAA,EAAKvI,OACL;QAAA,OAAOoB,EAAAA,SAAU,CAAAE,KAAA;QAAA,UACP,EAAA,SAAAqF,SAACC;iBAAMD,SAAS,CAAAC,CAAC,CAC3B,CAAA;AAAA,SAAA;QAAA,SAAS,EAAA,SAAAa,QAACb,CAAA,EAAA;UAAA,OAAMa,QAAQ,CAAAb,CAAC,CAExB,CAAA;AAAA,SAAA;OAAAnH,EAAAA,CAAAA,aAAA,CAAc,SAAS,CAAA,CAAA,CAAA,CAAA;KAPzB,CAAA;AAUL,GAAA;AACF,CAAC,CAAA;;;;"}