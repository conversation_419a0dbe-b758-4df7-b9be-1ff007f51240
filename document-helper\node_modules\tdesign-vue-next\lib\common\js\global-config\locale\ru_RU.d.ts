import 'dayjs/locale/ru';
declare const _default: {
    readonly autoComplete: {
        readonly empty: "Нет данных";
    };
    readonly pagination: {
        readonly itemsPerPage: "{size} шт./стр.";
        readonly jumpTo: "Перейти к";
        readonly page: "стр.";
        readonly total: "Всего {count} элементов данных";
    };
    readonly cascader: {
        readonly empty: "Нет данных";
        readonly loadingText: "Загрузка";
        readonly placeholder: "Выберите";
    };
    readonly calendar: {
        readonly yearSelection: "{year} год";
        readonly monthSelection: "{month} месяц";
        readonly yearRadio: "Год";
        readonly monthRadio: "Месяц";
        readonly hideWeekend: "Скрыть выходные";
        readonly showWeekend: "Показать выходные";
        readonly today: "Сегодня";
        readonly thisMonth: "Этот месяц";
        readonly week: "Пн,Вт,Ср,Чт,Пт,Сб,Вс";
        readonly cellMonth: "1 мес.,2 мес.,3 мес.,4 мес.,5 мес.,6 мес.,7 мес.,8 мес.,9 мес.,10 мес.,11 мес.,12 мес.";
    };
    readonly transfer: {
        readonly title: "{checked} / {total} шт.";
        readonly empty: "Нет данных";
        readonly placeholder: "Введите ключевое слово для поиска";
    };
    readonly timePicker: {
        readonly dayjsLocale: "zh-cn";
        readonly now: "Сейчас";
        readonly confirm: "Ок";
        readonly anteMeridiem: "До полудня";
        readonly postMeridiem: "После полудня";
        readonly placeholder: "Выберите время";
    };
    readonly dialog: {
        readonly confirm: "Подтвердить";
        readonly cancel: "Отмена";
    };
    readonly drawer: {
        readonly confirm: "Подтвердить";
        readonly cancel: "Отмена";
    };
    readonly popconfirm: {
        readonly confirm: {
            readonly content: "Подтвердить";
        };
        readonly cancel: {
            readonly content: "Отмена";
        };
    };
    readonly table: {
        readonly empty: "Нет данных";
        readonly loadingText: "Загрузка, пожалуйста, подождите";
        readonly loadingMoreText: "Нажмите, чтобы загрузить больше";
        readonly filterInputPlaceholder: "Введите содержание (нет значения по умолчанию)";
        readonly sortAscendingOperationText: "Сортировать по возрастанию";
        readonly sortCancelOperationText: "Отменить сортировку";
        readonly sortDescendingOperationText: "Сортировать по убыванию";
        readonly clearFilterResultButtonText: "Очистить фильтр";
        readonly columnConfigButtonText: "Настройка столбцов";
        readonly columnConfigTitleText: "Настройка столбцов таблицы";
        readonly columnConfigDescriptionText: "Пожалуйста, выберите столбцы данных, которые нужно отобразить в таблице";
        readonly confirmText: "Подтвердить";
        readonly cancelText: "Отмена";
        readonly resetText: "Сбросить";
        readonly selectAllText: "Выбрать все";
        readonly searchResultText: "Поиск «{result}», найдено {count} результатов";
    };
    readonly select: {
        readonly empty: "Нет данных";
        readonly loadingText: "Загрузка";
        readonly placeholder: "Выберите";
    };
    readonly tree: {
        readonly empty: "Нет данных";
    };
    readonly treeSelect: {
        readonly empty: "Нет данных";
        readonly loadingText: "Загрузка";
        readonly placeholder: "Выберите";
    };
    readonly datePicker: {
        readonly dayjsLocale: "ru";
        readonly placeholder: {
            readonly date: "Выберите дату";
            readonly month: "Выберите месяц";
            readonly year: "Выберите год";
            readonly quarter: "Пожалуйста выберите квартал";
            readonly week: "Пожалуйста выберите неделю";
        };
        readonly weekdays: readonly ["Пн", "Вт", "Ср", "Чт", "Пт", "Сб", "Вс"];
        readonly months: readonly ["Январь", "Февраль", "Март", "Апрель", "Май", "Июнь", "Июль", "Август", "Сентябрь", "Октябрь", "Ноябрь", "Декабрь"];
        readonly quarters: readonly ["1-й квартал", "2-й квартал", "3-й квартал", "4-й квартал"];
        readonly rangeSeparator: "-";
        readonly direction: "ltr";
        readonly format: "DD.MM.YYYY";
        readonly dayAriaLabel: "День";
        readonly weekAbbreviation: "Неделя";
        readonly yearAriaLabel: "Год";
        readonly monthAriaLabel: "Месяц";
        readonly confirm: "Подтвердить";
        readonly selectTime: "Выберите время";
        readonly selectDate: "Выберите дату";
        readonly nextYear: "Следующий год";
        readonly preYear: "Предыдущий год";
        readonly nextMonth: "Следующий месяц";
        readonly preMonth: "Предыдущий месяц";
        readonly preDecade: "Предыдущее десятилетие";
        readonly nextDecade: "Следующее десятилетие";
        readonly now: "Сейчас";
    };
    readonly upload: {
        readonly sizeLimitMessage: "Размер файла не должен превышать {sizeLimit}";
        readonly cancelUploadText: "Отменить загрузку";
        readonly triggerUploadText: {
            readonly fileInput: "Выбрать файл";
            readonly image: "Нажмите, чтобы загрузить изображение";
            readonly normal: "Нажмите, чтобы загрузить";
            readonly reupload: "Выбрать заново";
            readonly continueUpload: "Продолжить выбор";
            readonly delete: "Удалить";
            readonly uploading: "Загрузка";
        };
        readonly dragger: {
            readonly dragDropText: "Отпустите мышь";
            readonly draggingText: "Перетащите в эту область";
            readonly clickAndDragText: "Нажмите на кнопку «Выбрать файл» выше или перетащите файл в эту область";
        };
        readonly file: {
            readonly fileNameText: "Имя файла";
            readonly fileSizeText: "Размер файла";
            readonly fileStatusText: "Статус";
            readonly fileOperationText: "Операция";
            readonly fileOperationDateText: "Дата загрузки";
        };
        readonly progress: {
            readonly uploadingText: "Загрузка";
            readonly waitingText: "Ожидание загрузки";
            readonly failText: "Загрузка не удалась";
            readonly successText: "Загрузка успешна";
        };
    };
    readonly form: {
        readonly errorMessage: {
            readonly date: "Введите правильный ${name}";
            readonly url: "Введите правильный ${name}";
            readonly whitespace: "${name} не может быть пустым";
            readonly required: "${name} обязательно для заполнения";
            readonly max: "Длина символов ${name} не должна превышать ${validate} символов";
            readonly min: "Длина символов ${name} не должна быть меньше ${validate} символов";
            readonly len: "Длина символов ${name} должна быть ${validate}";
            readonly enum: "${name} может быть только ${validate} и т.д.";
            readonly idcard: "Введите правильный ${name}";
            readonly telnumber: "Введите правильный ${name}";
            readonly pattern: "Введите правильный ${name}";
            readonly validator: "${name} не соответствует требованиям";
            readonly boolean: "Тип данных ${name} должен быть булевым";
            readonly number: "${name} должно быть числом";
        };
        readonly colonText: ":";
    };
    readonly input: {
        readonly placeholder: "Введите";
    };
    readonly list: {
        readonly loadingText: "Загрузка, пожалуйста, подождите";
        readonly loadingMoreText: "Нажмите, чтобы загрузить больше";
    };
    readonly alert: {
        readonly expandText: "Развернуть больше";
        readonly collapseText: "Свернуть";
    };
    readonly anchor: {
        readonly copySuccessText: "Ссылка скопирована успешно";
        readonly copyText: "Скопировать ссылку";
    };
    readonly colorPicker: {
        readonly swatchColorTitle: "Системные предустановленные цвета";
        readonly recentColorTitle: "Недавно использованные цвета";
        readonly clearConfirmText: "Вы уверены, что хотите очистить недавно использованные цвета?";
        readonly singleColor: "Сплошной";
        readonly gradientColor: "Градиент";
    };
    readonly guide: {
        readonly finishButtonProps: {
            readonly content: "Готово";
            readonly theme: "primary";
        };
        readonly nextButtonProps: {
            readonly content: "Следующий шаг";
            readonly theme: "primary";
        };
        readonly skipButtonProps: {
            readonly content: "Пропустить";
            readonly theme: "default";
        };
        readonly prevButtonProps: {
            readonly content: "Предыдущий шаг";
            readonly theme: "default";
        };
    };
    readonly image: {
        readonly errorText: "Изображение не может быть отображено";
        readonly loadingText: "Загрузка изображения";
    };
    readonly imageViewer: {
        readonly errorText: "Не удалось загрузить изображение, попробуйте перезагрузить";
        readonly mirrorTipText: "Зеркало";
        readonly rotateTipText: "Поворот";
        readonly originalSizeTipText: "Оригинальный размер";
    };
    readonly typography: {
        readonly expandText: "раскрывать";
        readonly collapseText: "убрать";
        readonly copiedText: "Скопировано успешно";
    };
    readonly rate: {
        readonly rateText: readonly ["Ужасно", "Разочарован", "Обычный", "Удовлетворенный", "Удивленный"];
    };
    readonly empty: {
        readonly titleText: {
            readonly maintenance: "Строительство";
            readonly success: "Успех";
            readonly fail: "Провал";
            readonly empty: "Нет данных";
            readonly networkError: "Ошибка сети";
        };
    };
    readonly descriptions: {
        readonly colonText: ":";
    };
    readonly chat: {
        readonly placeholder: "Введите сообщение...";
        readonly stopBtnText: "Остановить";
        readonly refreshTipText: "Сгенерировать заново";
        readonly copyTipText: "Копировать";
        readonly likeTipText: "Нравится";
        readonly dislikeTipText: "Не нравится";
        readonly copyCodeBtnText: "Копировать код";
        readonly copyCodeSuccessText: "Скопировано";
        readonly clearHistoryBtnText: "Очистить историю";
        readonly copyTextSuccess: "Успешно скопировано в буфер обмена";
        readonly copyTextFail: "Не удалось скопировать в буфер обмена";
        readonly confirmClearHistory: "Вы уверены, что хотите очистить все сообщения?";
        readonly loadingText: "Думаю...";
        readonly loadingEndText: "Глубоко обдумано";
        readonly uploadImageText: "загрузить изображение";
        readonly uploadAttachmentText: "загрузить вложение";
    };
    readonly qrcode: {
        readonly expiredText: "истекший";
        readonly refreshText: "обновить";
        readonly scannedText: "сканированный";
    };
};
export default _default;
