import { TdRangeInputPopupProps } from '../range-input/type';
import { PropType } from 'vue';
declare const _default: {
    autoWidth: BooleanConstructor;
    disabled: BooleanConstructor;
    inputValue: {
        type: PropType<TdRangeInputPopupProps["inputValue"]>;
        default: TdRangeInputPopupProps["inputValue"];
    };
    defaultInputValue: {
        type: PropType<TdRangeInputPopupProps["defaultInputValue"]>;
    };
    label: {
        type: PropType<TdRangeInputPopupProps["label"]>;
    };
    panel: {
        type: PropType<TdRangeInputPopupProps["panel"]>;
    };
    popupProps: {
        type: PropType<TdRangeInputPopupProps["popupProps"]>;
    };
    popupVisible: BooleanConstructor;
    rangeInputProps: {
        type: PropType<TdRangeInputPopupProps["rangeInputProps"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    status: {
        type: PropType<TdRangeInputPopupProps["status"]>;
        default: TdRangeInputPopupProps["status"];
        validator(val: TdRangeInputPopupProps["status"]): boolean;
    };
    tips: {
        type: PropType<TdRangeInputPopupProps["tips"]>;
    };
    onInputChange: PropType<TdRangeInputPopupProps["onInputChange"]>;
    onPopupVisibleChange: PropType<TdRangeInputPopupProps["onPopupVisibleChange"]>;
};
export default _default;
