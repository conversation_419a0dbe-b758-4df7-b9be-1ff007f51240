'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var defineProperty = require('../_chunks/dep-304d0f8c.js');
var vue = require('vue');
var utils_renderFn = require('../utils/render-fn.js');
var utils_useSizeProps = require('../utils/use-size-props.js');
require('../utils/use-common-classname.js');
require('../utils/config-context.js');

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { defineProperty._defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var element = {
  "tag": "svg",
  "attrs": {
    "fill": "none",
    "viewBox": "0 0 24 24",
    "width": "1em",
    "height": "1em"
  },
  "children": [{
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M2 2H7.07713C9.24103 2 11 3.75897 11 5.92277V11H5.92287C3.75893 11 2 9.24099 2 7.07705V2ZM4 4V7.07705C4 8.13647 4.86355 9 5.92287 9H9V5.92277C9 4.86357 8.13649 4 7.07713 4H4ZM16.9229 4C15.8635 4 15 4.86357 15 5.92277V9H18.0771C19.1365 9 20 8.13647 20 7.07705V4H16.9229ZM13 5.92277C13 3.75897 14.759 2 16.9229 2H22V7.07705C22 9.24099 20.2411 11 18.0771 11H13V5.92277ZM5.92287 15C4.86355 15 4 15.8635 4 16.9229V20H7.07713C8.13649 20 9 19.1364 9 18.0772V15H5.92287ZM2 16.9229C2 14.759 3.75893 13 5.92287 13H11V18.0772C11 20.241 9.24103 22 7.07713 22H2V16.9229ZM13 13H18.0771C20.2411 13 22 14.759 22 16.9229V22H16.9229C14.759 22 13 20.241 13 18.0772V13ZM15 15V18.0772C15 19.1364 15.8635 20 16.9229 20H20V16.9229C20 15.8635 19.1365 15 18.0771 15H15Z"
    }
  }]
};
var system3 = vue.defineComponent({
  name: "System3Icon",
  props: {
    size: {
      type: String
    },
    onClick: {
      type: Function
    }
  },
  setup(props, _ref) {
    var {
      attrs
    } = _ref;
    var propsSize = vue.computed(() => props.size);
    var {
      className,
      style
    } = utils_useSizeProps['default'](propsSize);
    var finalCls = vue.computed(() => ["t-icon", "t-icon-system-3", className.value]);
    var finalStyle = vue.computed(() => _objectSpread(_objectSpread({}, style.value), attrs.style));
    var finalProps = vue.computed(() => ({
      class: finalCls.value,
      style: finalStyle.value,
      onClick: e => {
        var _props$onClick;
        return (_props$onClick = props.onClick) === null || _props$onClick === void 0 ? void 0 : _props$onClick.call(props, {
          e
        });
      }
    }));
    return () => utils_renderFn['default'](element, finalProps.value);
  }
});

exports.default = system3;
//# sourceMappingURL=system-3.js.map
