import { TreeStore } from './tree-store';
import { TreeNodeValue, TreeNodeState, TypeIdMap, TypeTreeItem, TypeSettingOptions, TypeTreeNodeModel, TypeTreeNodeData } from './types';
export declare const settableStatus: Record<string, boolean | null>;
export declare const settableProps: string[];
export declare const syncableProps: string[];
export declare const privateKey = "__tdesign_id__";
export declare class TreeNode {
    tree: TreeStore;
    [privateKey]: string;
    value: string;
    label: string;
    data: TypeTreeNodeData;
    parent: TreeNode;
    children: TreeNode[] | boolean;
    model: TypeTreeNodeModel;
    vmIsLeaf: boolean;
    vmIsFirst: boolean;
    vmIsLast: boolean;
    vmIsRest: boolean;
    vmIsLocked: boolean;
    expanded: boolean;
    expandMutex: null | boolean;
    actived: boolean;
    activable: null | boolean;
    checkable: null | boolean;
    vmCheckable: boolean;
    checked: boolean;
    indeterminate: boolean;
    disabled: null | boolean;
    draggable: null | boolean;
    visible: boolean;
    level: number;
    loading: boolean;
    constructor(tree: TreeStore, data?: TypeTreeNodeData, parent?: TreeNode);
    private initChecked;
    private initExpanded;
    private initActived;
    append(data: TypeTreeNodeData | TypeTreeNodeData[]): void;
    appendTo(tree: TreeStore, parent?: TreeNode, index?: number): void;
    private insert;
    insertBefore(newData: TypeTreeItem): void;
    insertAfter(newData: TypeTreeItem): void;
    remove(): void;
    private clean;
    private loadChildren;
    set(item: TreeNodeState): void;
    getParent(): TreeNode;
    getParents(): TreeNode[];
    getSiblings(): TreeNode[];
    getChildren(deep?: boolean): boolean | TypeTreeNodeModel[];
    getRoot(): TreeNode;
    getIndex(): number;
    getPath(): TreeNode[];
    getLevel(): number;
    isRest(): boolean;
    isVisible(): boolean;
    isDisabledState(): boolean;
    isDisabled(): boolean;
    isDraggable(): boolean;
    isExpandMutex(): boolean;
    isActivable(): boolean;
    isCheckable(): boolean;
    isActived(map?: Map<string, boolean>): boolean;
    isExpanded(map?: Map<string, boolean>): boolean;
    isChecked(map?: TypeIdMap): boolean;
    isIndeterminate(): boolean;
    isFirst(): boolean;
    isLast(): boolean;
    isLeaf(): boolean;
    lock(lockState: boolean): void;
    afterExpanded(): void;
    toggleExpanded(opts?: TypeSettingOptions): TreeNodeValue[];
    setExpanded(expanded: boolean, opts?: TypeSettingOptions): TreeNodeValue[];
    toggleActived(opts?: TypeSettingOptions): TreeNodeValue[];
    setActived(actived: boolean, opts?: TypeSettingOptions): TreeNodeValue[];
    hasEnableUnCheckedChild(): boolean;
    toggleChecked(opts?: TypeSettingOptions): TreeNodeValue[];
    setChecked(checked: boolean, opts?: TypeSettingOptions): TreeNodeValue[];
    private spreadParentChecked;
    private spreadChildrenChecked;
    setDisabled(disabled: boolean): void;
    update(): void;
    updateChecked(): void;
    updateChildren(): void;
    updateParents(): void;
    updateRelated(): void;
    walk(): TreeNode[];
    private spreadChildren;
    private spreadParents;
    getModel(): TypeTreeNodeModel;
}
export default TreeNode;
