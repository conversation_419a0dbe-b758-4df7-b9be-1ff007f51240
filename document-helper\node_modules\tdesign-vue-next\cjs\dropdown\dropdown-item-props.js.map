{"version": 3, "file": "dropdown-item-props.js", "sources": ["../../../components/dropdown/dropdown-item-props.ts"], "sourcesContent": ["/* eslint-disable */\n\n/**\n * 该文件为脚本自动生成文件，请勿随意修改。如需修改请联系 PMC\n * */\n\nimport { TdDropdownItemProps } from '../dropdown/type';\nimport { PropType } from 'vue';\n\nexport default {\n  /** 是否高亮当前操作项 */\n  active: Boolean,\n  /** 下拉操作项内容 */\n  content: {\n    type: [String, Function] as PropType<TdDropdownItemProps['content']>,\n    default: '',\n  },\n  /** 是否禁用操作项 */\n  disabled: Boolean,\n  /** 是否显示操作项之间的分隔线（分隔线默认在下方） */\n  divider: Boolean,\n  /** 组件前置图标 */\n  prefixIcon: {\n    type: Function as PropType<TdDropdownItemProps['prefixIcon']>,\n  },\n  /** 下拉菜单选项主题 */\n  theme: {\n    type: String as PropType<TdDropdownItemProps['theme']>,\n    default: 'default' as TdDropdownItemProps['theme'],\n    validator(val: TdDropdownItemProps['theme']): boolean {\n      if (!val) return true;\n      return ['default', 'success', 'warning', 'error'].includes(val);\n    },\n  },\n  /** 下拉操作项唯一标识 */\n  value: {\n    type: [String, Number, Object] as PropType<TdDropdownItemProps['value']>,\n  },\n  /** 点击时触发 */\n  onClick: Function as PropType<TdDropdownItemProps['onClick']>,\n};\n"], "names": ["active", "Boolean", "content", "type", "String", "Function", "disabled", "divider", "prefixIcon", "theme", "validator", "val", "includes", "value", "Number", "Object", "onClick"], "mappings": ";;;;;;;;;;AASA,wBAAe;AAEbA,EAAAA,MAAQ,EAAAC,OAAA;AAERC,EAAAA,OAAS,EAAA;AACPC,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,QAAQ,CAAA;IACvB,SAAS,EAAA,EAAA;GACX;AAEAC,EAAAA,QAAU,EAAAL,OAAA;AAEVM,EAAAA,OAAS,EAAAN,OAAA;AAETO,EAAAA,UAAY,EAAA;AACVL,IAAAA,IAAM,EAAAE,QAAAA;GACR;AAEAI,EAAAA,KAAO,EAAA;AACLN,IAAAA,IAAM,EAAAC,MAAA;AACN,IAAA,SAAA,EAAS,SAAA;AACTM,IAAAA,WAAAA,SAAAA,UAAUC,GAA4C,EAAA;AACpD,MAAA,IAAI,CAACA,GAAA,EAAY,OAAA,IAAA,CAAA;AACjB,MAAA,OAAO,CAAC,SAAW,EAAA,SAAA,EAAW,WAAW,OAAO,CAAA,CAAEC,SAASD,GAAG,CAAA,CAAA;AAChE,KAAA;GACF;AAEAE,EAAAA,KAAO,EAAA;AACLV,IAAAA,IAAM,EAAA,CAACC,MAAQ,EAAAU,MAAA,EAAQC,MAAM,CAAA;GAC/B;AAEAC,EAAAA,OAAS,EAAAX,QAAAA;AACX,CAAA;;;;"}