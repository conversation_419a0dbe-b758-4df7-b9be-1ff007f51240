{"version": 3, "file": "getRelativePosition.js", "sources": ["../../../../components/guide/utils/getRelativePosition.ts"], "sourcesContent": ["import { getElmCssPropValue, isFixed, getWindowScroll } from '@tdesign/shared-utils';\n\n/**\n * 获取元素相对于另一个元素的位置（或者说相对于 body）\n * 感谢 `meouw`: http://stackoverflow.com/a/442474/375966\n */\nexport function getRelativePosition(elm: HTMLElement, relativeElm: HTMLElement = document.body) {\n  const { scrollTop, scrollLeft } = getWindowScroll();\n  const { top: elmTop, left: elmLeft } = elm.getBoundingClientRect();\n  const { top: relElmTop, left: relElmLeft } = relativeElm.getBoundingClientRect();\n  const relativeElmPosition = getElmCssPropValue(relativeElm, 'position');\n\n  if (\n    (relativeElm.tagName.toLowerCase() !== 'body' && relativeElmPosition === 'relative') ||\n    relativeElmPosition === 'sticky'\n  ) {\n    return {\n      top: elmTop - relElmTop,\n      left: elmLeft - relElmLeft,\n    };\n  }\n\n  if (isFixed(elm)) {\n    return {\n      top: elmTop,\n      left: elmLeft,\n    };\n  }\n\n  return {\n    top: elmTop + scrollTop,\n    left: elmLeft + scrollLeft,\n  };\n}\n"], "names": ["getRelativePosition", "elm", "relativeElm", "arguments", "length", "undefined", "document", "body", "_getWindowScroll", "getWindowScroll", "scrollTop", "scrollLeft", "_elm$getBoundingClien", "getBoundingClientRect", "elmTop", "top", "elmLeft", "left", "_relativeElm$getBound", "rel<PERSON><PERSON><PERSON><PERSON>", "relElmLeft", "relativeElmPosition", "getElmCssPropValue", "tagName", "toLowerCase", "isFixed"], "mappings": ";;;;;;;;;;;;AAMO,SAASA,mBAAoBA,CAAAC,GAAA,EAA4D;AAAA,EAAA,IAA1CC,WAA2B,GAAAC,SAAA,CAAAC,MAAA,GAAAD,CAAAA,IAAAA,SAAA,CAAAE,CAAAA,CAAAA,KAAAA,SAAA,GAAAF,SAAA,CAAAG,CAAAA,CAAAA,GAAAA,QAAA,CAASC,IAAM,CAAA;AAC9F,EAAA,IAAAC,gBAAA,GAAkCC,eAAgB,EAAA;IAA1CC,SAAA,GAAAF,gBAAA,CAAAE,SAAA;IAAWC,UAAW,GAAAH,gBAAA,CAAXG,UAAW,CAAA;AAC9B,EAAA,IAAAC,qBAAA,GAAuCX,IAAIY,qBAAsB,EAAA;IAApDC,MAAA,GAAAF,qBAAA,CAALG,GAAK;IAAcC,OAAQ,GAAAJ,qBAAA,CAAdK;AACrB,EAAA,IAAAC,qBAAA,GAA6ChB,YAAYW,qBAAsB,EAAA;IAAlEM,SAAA,GAAAD,qBAAA,CAALH,GAAK;IAAiBK,UAAW,GAAAF,qBAAA,CAAjBD;AAClB,EAAA,IAAAI,mBAAA,GAAsBC,kBAAmB,CAAApB,WAAA,EAAa,UAAU,CAAA,CAAA;AAGnE,EAAA,IAAAA,WAAA,CAAYqB,QAAQC,WAAY,EAAA,KAAM,UAAUH,mBAAwB,KAAA,UAAA,IACzEA,wBAAwB,QACxB,EAAA;IACO,OAAA;MACLN,KAAKD,MAAS,GAAAK,SAAA;MACdF,MAAMD,OAAU,GAAAI,UAAAA;KAClB,CAAA;AACF,GAAA;AAEI,EAAA,IAAAK,OAAA,CAAQxB,GAAG,CAAG,EAAA;IACT,OAAA;AACLc,MAAAA,GAAK,EAAAD,MAAA;AACLG,MAAAA,IAAM,EAAAD,OAAAA;KACR,CAAA;AACF,GAAA;EAEO,OAAA;IACLD,KAAKD,MAAS,GAAAJ,SAAA;IACdO,MAAMD,OAAU,GAAAL,UAAAA;GAClB,CAAA;AACF;;;;"}