import mitt from 'mitt';
import { TreeNode } from './tree-node';
import { TreeNodeValue, TypeIdMap, TypeTargetNode, TypeTreeNodeData, TypeTreeItem, TypeTreeStoreOptions, TypeTreeFilterOptions, TypeRelatedNodesOptions, TypeTreeEventState, TypeUpdatedMap } from './types';
export declare class TreeStore {
    children: TreeNode[];
    nodes: TreeNode[];
    nodeMap: Map<TreeNodeValue, TreeNode>;
    privateMap: Map<TreeNodeValue, TreeNode>;
    config: TypeTreeStoreOptions;
    activedMap: TypeIdMap;
    updatedMap: TypeUpdatedMap;
    checkedMap: TypeIdMap;
    expandedMap: TypeIdMap;
    filterMap: TypeIdMap;
    hasFilter: boolean;
    emitter: ReturnType<typeof mitt>;
    private updateTick;
    private shouldReflow;
    private prevFilter;
    constructor(options: TypeTreeStoreOptions);
    setConfig(options: TypeTreeStoreOptions): void;
    getChildren(): TreeNode[];
    getNode(item: TypeTargetNode): TreeNode;
    getIndex(node: TreeNode): number;
    getParent(value: TypeTargetNode): TreeNode;
    getParents(value: TypeTargetNode): TreeNode[];
    getNodeIndex(value: TypeTargetNode): number;
    getNodes(item?: TypeTargetNode, options?: TypeTreeFilterOptions): TreeNode[];
    append(list: TypeTreeNodeData[]): void;
    reload(list: TypeTreeNodeData[]): void;
    private parseNodeData;
    appendNodes(para: TypeTargetNode | TypeTreeNodeData, item?: TypeTreeNodeData | TreeNode): void;
    insertBefore(value: TypeTargetNode, item: TypeTreeItem): void;
    insertAfter(value: TypeTargetNode, item: TypeTreeItem): void;
    refreshNodes(): void;
    reflow(node?: TreeNode): void;
    updated(node?: TreeNode): void;
    getActived(map?: TypeIdMap): TreeNodeValue[];
    getActivedNodes(item?: TypeTargetNode): TreeNode[];
    replaceActived(list: TreeNodeValue[]): void;
    setActived(actived: TreeNodeValue[]): void;
    resetActived(): void;
    getExpanded(map?: TypeIdMap): TreeNodeValue[];
    replaceExpanded(list: TreeNodeValue[]): void;
    setExpanded(list: TreeNodeValue[]): void;
    setExpandedDirectly(list: TreeNodeValue[], expanded?: boolean): void;
    resetExpanded(): void;
    updateExpanded(list: TreeNodeValue[]): void;
    getChecked(map?: TypeIdMap): TreeNodeValue[];
    getCheckedNodes(item?: TypeTargetNode): TreeNode[];
    replaceChecked(list: TreeNodeValue[]): void;
    setChecked(list: TreeNodeValue[]): void;
    resetChecked(): void;
    refreshState(): void;
    updateAll(): void;
    remove(value?: TypeTargetNode): void;
    removeAll(): void;
    getRelatedNodes(list: TreeNodeValue[], options?: TypeRelatedNodesOptions): TreeNode[];
    emit(name: string, state?: TypeTreeEventState): void;
    private lockFilterPathNodes;
}
export default TreeStore;
