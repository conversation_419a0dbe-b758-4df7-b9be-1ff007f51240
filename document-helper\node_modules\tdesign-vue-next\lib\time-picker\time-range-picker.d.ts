import { TimeRangeValue } from './types';
declare const _default: import("vue").DefineComponent<{
    rangeInputProps: ObjectConstructor;
    popupProps: ObjectConstructor;
    allowInput: BooleanConstructor;
    autoSwap: {
        type: BooleanConstructor;
        default: boolean;
    };
    borderless: BooleanConstructor;
    clearable: BooleanConstructor;
    disableTime: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["disableTime"]>;
    };
    disabled: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["disabled"]>;
        default: import("./type").TdTimeRangePickerProps["disabled"];
    };
    format: {
        type: StringConstructor;
        default: string;
    };
    hideDisabledTime: {
        type: BooleanConstructor;
        default: boolean;
    };
    label: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["label"]>;
    };
    placeholder: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["placeholder"]>;
        default: import("./type").TdTimeRangePickerProps["placeholder"];
    };
    presets: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["presets"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    size: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["size"]>;
        default: import("./type").TdTimeRangePickerProps["size"];
        validator(val: import("./type").TdTimeRangePickerProps["size"]): boolean;
    };
    status: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["status"]>;
        default: import("./type").TdTimeRangePickerProps["status"];
        validator(val: import("./type").TdTimeRangePickerProps["status"]): boolean;
    };
    steps: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["steps"]>;
        default: () => import("./type").TdTimeRangePickerProps["steps"];
    };
    tips: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["tips"]>;
    };
    value: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["value"]>;
        default: import("./type").TdTimeRangePickerProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["value"]>;
        default: import("./type").TdTimeRangePickerProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["defaultValue"]>;
    };
    onBlur: import("vue").PropType<import("./type").TdTimeRangePickerProps["onBlur"]>;
    onChange: import("vue").PropType<import("./type").TdTimeRangePickerProps["onChange"]>;
    onFocus: import("vue").PropType<import("./type").TdTimeRangePickerProps["onFocus"]>;
    onInput: import("vue").PropType<import("./type").TdTimeRangePickerProps["onInput"]>;
    onPick: import("vue").PropType<import("./type").TdTimeRangePickerProps["onPick"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    rangeInputProps: ObjectConstructor;
    popupProps: ObjectConstructor;
    allowInput: BooleanConstructor;
    autoSwap: {
        type: BooleanConstructor;
        default: boolean;
    };
    borderless: BooleanConstructor;
    clearable: BooleanConstructor;
    disableTime: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["disableTime"]>;
    };
    disabled: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["disabled"]>;
        default: import("./type").TdTimeRangePickerProps["disabled"];
    };
    format: {
        type: StringConstructor;
        default: string;
    };
    hideDisabledTime: {
        type: BooleanConstructor;
        default: boolean;
    };
    label: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["label"]>;
    };
    placeholder: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["placeholder"]>;
        default: import("./type").TdTimeRangePickerProps["placeholder"];
    };
    presets: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["presets"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    size: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["size"]>;
        default: import("./type").TdTimeRangePickerProps["size"];
        validator(val: import("./type").TdTimeRangePickerProps["size"]): boolean;
    };
    status: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["status"]>;
        default: import("./type").TdTimeRangePickerProps["status"];
        validator(val: import("./type").TdTimeRangePickerProps["status"]): boolean;
    };
    steps: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["steps"]>;
        default: () => import("./type").TdTimeRangePickerProps["steps"];
    };
    tips: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["tips"]>;
    };
    value: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["value"]>;
        default: import("./type").TdTimeRangePickerProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["value"]>;
        default: import("./type").TdTimeRangePickerProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<import("./type").TdTimeRangePickerProps["defaultValue"]>;
    };
    onBlur: import("vue").PropType<import("./type").TdTimeRangePickerProps["onBlur"]>;
    onChange: import("vue").PropType<import("./type").TdTimeRangePickerProps["onChange"]>;
    onFocus: import("vue").PropType<import("./type").TdTimeRangePickerProps["onFocus"]>;
    onInput: import("vue").PropType<import("./type").TdTimeRangePickerProps["onInput"]>;
    onPick: import("vue").PropType<import("./type").TdTimeRangePickerProps["onPick"]>;
}>>, {
    value: TimeRangeValue;
    format: string;
    disabled: boolean | boolean[];
    autoSwap: boolean;
    size: "small" | "medium" | "large";
    status: "error" | "default" | "success" | "warning";
    placeholder: string | string[];
    modelValue: TimeRangeValue;
    readonly: boolean;
    steps: (string | number)[];
    borderless: boolean;
    clearable: boolean;
    allowInput: boolean;
    hideDisabledTime: boolean;
}, {}>;
export default _default;
