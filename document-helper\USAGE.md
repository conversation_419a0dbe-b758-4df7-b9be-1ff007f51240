# 使用说明

## 启动应用

1. 确保已安装 Node.js 16.0 或更高版本
2. 在项目根目录运行：
   ```bash
   npm install
   npm run dev
   ```
3. 打开浏览器访问 `http://localhost:5173/`

## 界面介绍

### 顶部工具栏
- **新建文档**：清空当前文档，开始新的文档编写
- **保存文档**：保存当前文档（功能待完善）
- **导出文档**：导出文档为不同格式（功能待完善）

### 左侧目录面板
- **文档目录**：显示当前文档的章节结构，包含章节标题和内容预览
- **章节预览**：每个目录项下方显示内容摘要（前50个字符）
- **生成目录**：AI智能生成文档大纲（功能待完善）
- **目录操作**：
  - 点击目录项可选中并在右侧编辑内容
  - 双击章节标题可直接编辑标题
  - 悬停在目录项上显示操作按钮
  - 支持快速内联编辑和详细编辑两种模式
- **添加根目录**：在底部点击按钮添加新的根级章节

### 右侧内容编辑区
- **编辑模式**：纯文本编辑器，支持Markdown语法
- **预览模式**：实时渲染Markdown内容
- **分屏模式**：左侧编辑，右侧预览

## 操作流程

### 1. 创建文档结构
1. 点击"添加根目录"创建第一个章节
2. 选中章节后，点击"+"按钮添加子章节
3. 修改章节标题：
   - **快速编辑**：双击章节标题直接修改
   - **详细编辑**：点击设置按钮进行详细编辑（包含描述等）
4. 查看章节预览：每个目录项下方会显示内容摘要

### 2. 编写内容
1. 点击左侧目录中的章节
2. 在右侧编辑器中输入内容
3. 使用Markdown语法格式化文本：
   - `# 标题1`
   - `## 标题2`
   - `**粗体**`
   - `*斜体*`

### 3. 预览效果
1. 点击"预览"按钮查看渲染效果
2. 或使用"分屏"模式同时编辑和预览

### 4. 目录管理技巧
- **内联编辑**：双击任意章节标题即可快速修改
- **批量操作**：悬停在目录项上查看所有可用操作
- **内容预览**：目录树中直接显示每个章节的内容摘要
- **层级管理**：支持无限层级的章节嵌套
- **快捷操作**：
  - 双击标题：快速编辑
  - 单击编辑图标：快速编辑
  - 单击设置图标：详细编辑
  - 单击加号：添加子章节
  - 单击删除：移除章节

## Markdown语法支持

当前支持的Markdown语法：
- `# 一级标题`
- `## 二级标题`
- `### 三级标题`
- `**粗体文本**`
- `*斜体文本*`

## 快捷键

- 目前暂无快捷键支持，后续版本将添加

## 常见问题

### Q: 如何保存文档？
A: 当前版本的保存功能为演示状态，内容会临时保存在浏览器内存中。刷新页面会丢失数据。

### Q: 支持哪些导出格式？
A: 导出功能正在开发中，计划支持PDF、Word、HTML等格式。

### Q: 可以导入现有文档吗？
A: 导入功能将在后续版本中添加。

### Q: 支持协作编辑吗？
A: 协作功能在开发计划中，当前版本为单用户使用。

## 技术支持

如遇到问题，请：
1. 检查浏览器控制台是否有错误信息
2. 确认Node.js版本是否符合要求
3. 重新安装依赖：`npm install`
4. 重启开发服务器：`npm run dev`

## 更新日志

### v0.1.1 (当前版本) - 问题修复版
- ✅ 修复添加根目录功能
- ✅ 修复章节标题编辑功能
- ✅ 修复删除章节功能
- ✅ 改进用户交互体验
- ✅ 优化响应式数据更新
- ✅ 添加操作确认机制

### v0.1.0 (初始版本)
- ✅ 基础目录管理功能
- ✅ 多模式内容编辑
- ✅ 基础Markdown渲染
- ✅ TDesign UI组件集成
- ✅ 响应式布局设计
