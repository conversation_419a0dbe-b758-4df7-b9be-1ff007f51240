{"version": 3, "file": "form-item.js", "sources": ["../../../common/js/utils/stringTemplate.ts", "../../../components/form/form-item.tsx"], "sourcesContent": ["/**\n * 用正则实现模板字符串功能\n * @param str 模板字符串\n * @param vars 取值的对象\n * @returns 替换后的字符串\n */\nexport function template<T extends Record<string, string>>(str: string, vars: T): string {\n  return str.replace(/\\${(.*?)}/g, (_, prop: string) => vars[prop.trim()] ?? '');\n}\n", "import {\n  computed,\n  defineComponent,\n  inject,\n  nextTick,\n  onBeforeUnmount,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  toRefs,\n  VNode,\n  watch,\n} from 'vue';\nimport {\n  CheckCircleFilledIcon as TdCheckCircleFilledIcon,\n  CloseCircleFilledIcon as TdCloseCircleFilledIcon,\n  ErrorCircleFilledIcon as TdErrorCircleFilledIcon,\n  GlobalIconType,\n} from 'tdesign-icons-vue-next';\nimport {\n  isNil,\n  isArray,\n  isNumber,\n  isString,\n  isBoolean,\n  cloneDeep,\n  get as lodashGet,\n  set as lodashSet,\n} from 'lodash-es';\n\nimport { validate } from './utils/form-model';\nimport {\n  AllValidateResult,\n  Data,\n  FormErrorMessage,\n  FormItemValidateMessage,\n  FormRule,\n  ValidateTriggerType,\n  ValueType,\n} from './type';\nimport props from './form-item-props';\nimport {\n  AnalysisValidateResult,\n  ErrorListType,\n  FormIn<PERSON><PERSON>ey,\n  FormItemContext,\n  FormItemInjection<PERSON>ey,\n  SuccessListType,\n  useCLASSNAMES,\n  ValidateStatus,\n} from './consts';\n\nimport { useConfig, useTNodeJSX, useGlobalIcon, usePrefixClass } from '@tdesign/shared-hooks';\nimport { getFormItemClassName } from './utils';\nimport { template } from '@tdesign/common-js/utils/stringTemplate';\n\nexport type FormItemValidateResult<T extends Data = Data> = { [key in keyof T]: boolean | AllValidateResult[] };\n\nexport default defineComponent({\n  name: 'TFormItem',\n  props,\n  setup(props, { slots }) {\n    const renderContent = useTNodeJSX();\n    const CLASS_NAMES = useCLASSNAMES();\n    const { globalConfig } = useConfig('form');\n    const { CheckCircleFilledIcon, CloseCircleFilledIcon, ErrorCircleFilledIcon } = useGlobalIcon({\n      CheckCircleFilledIcon: TdCheckCircleFilledIcon,\n      CloseCircleFilledIcon: TdCloseCircleFilledIcon,\n      ErrorCircleFilledIcon: TdErrorCircleFilledIcon,\n    });\n    const form = inject(FormInjectionKey, undefined);\n\n    const classPrefix = usePrefixClass();\n    const formItemClassPrefix = usePrefixClass('form-item');\n\n    const needRequiredMark = computed(() => {\n      const requiredMark = props.requiredMark ?? form?.requiredMark ?? globalConfig.value.requiredMark;\n      const isRequired = innerRules.value.filter((rule) => rule.required).length > 0;\n      return requiredMark ?? isRequired;\n    });\n\n    const requiredMarkPosition = computed(() => {\n      return form?.requiredMarkPosition ?? globalConfig.value.requiredMarkPosition;\n    });\n\n    const hasLabel = computed(() => slots.label || props.label);\n    const hasColon = computed(() => !!(form?.colon && hasLabel.value));\n    const FROM_LABEL = usePrefixClass('form__label');\n    const labelAlign = computed(() => (isNil(props.labelAlign) ? form?.labelAlign : props.labelAlign));\n    const labelWidth = computed(() => (isNil(props.labelWidth) ? form?.labelWidth : props.labelWidth));\n\n    const labelClasses = computed(() => [\n      CLASS_NAMES.value.label,\n      {\n        [`${FROM_LABEL.value}--required`]: needRequiredMark.value,\n        [`${FROM_LABEL.value}--required-right`]: needRequiredMark.value && requiredMarkPosition.value === 'right',\n        [`${FROM_LABEL.value}--top`]: hasLabel.value && (labelAlign.value === 'top' || !labelWidth.value),\n        [`${FROM_LABEL.value}--left`]: labelAlign.value === 'left' && labelWidth.value,\n        [`${FROM_LABEL.value}--right`]: labelAlign.value === 'right' && labelWidth.value,\n      },\n    ]);\n\n    const statusClass = computed(\n      () =>\n        `${classPrefix.value}-is-${props.status || 'default'} ${\n          props.status === 'success' ? CLASS_NAMES.value.successBorder : ''\n        }`,\n    );\n\n    const renderLabel = () => {\n      if (Number(labelWidth.value) === 0) return;\n\n      let labelStyle = {};\n      if (labelWidth.value && labelAlign.value !== 'top') {\n        if (isNumber(labelWidth.value)) {\n          labelStyle = { width: `${labelWidth.value}px` };\n        } else {\n          labelStyle = { width: labelWidth.value };\n        }\n      }\n\n      return (\n        <div class={labelClasses.value} style={labelStyle}>\n          <label for={props.for || null}>{renderContent('label')}</label>\n          {hasColon.value && globalConfig.value.colonText}\n        </div>\n      );\n    };\n\n    /** Suffix Icon */\n    const getDefaultIcon = (): VNode => {\n      const resultIcon = (Icon: GlobalIconType) => (\n        <span class={CLASS_NAMES.value.status}>\n          <Icon />\n        </span>\n      );\n      const list = errorList.value;\n      if (verifyStatus.value === ValidateStatus.SUCCESS) {\n        return resultIcon(CheckCircleFilledIcon);\n      }\n      if (list?.[0]) {\n        const type = list[0].type || 'error';\n        const icon = {\n          error: CloseCircleFilledIcon,\n          warning: ErrorCircleFilledIcon,\n          success: CheckCircleFilledIcon,\n        }[type];\n        return resultIcon(icon);\n      }\n      return null;\n    };\n    const renderSuffixIcon = () => {\n      const { statusIcon } = props;\n      if (statusIcon === false) return;\n\n      let resultIcon = renderContent('statusIcon', { defaultNode: getDefaultIcon() });\n      if (resultIcon) return <span class={CLASS_NAMES.value.status}>{resultIcon}</span>;\n      if (resultIcon === false) return;\n\n      resultIcon = form?.renderContent('statusIcon', { defaultNode: getDefaultIcon(), params: props });\n      if (resultIcon) return resultIcon;\n    };\n    /** Suffix Icon END */\n\n    /** Content Style */\n    const errorClasses = computed(() => {\n      if (!showErrorMessage.value) return '';\n      if (verifyStatus.value === ValidateStatus.SUCCESS) {\n        return props.successBorder\n          ? [CLASS_NAMES.value.success, CLASS_NAMES.value.successBorder].join(' ')\n          : CLASS_NAMES.value.success;\n      }\n      if (!errorList.value.length) return;\n      const type = errorList.value[0].type || 'error';\n      if (props.status) return statusClass.value;\n      return type === 'error' ? CLASS_NAMES.value.error : CLASS_NAMES.value.warning;\n    });\n    const contentClasses = computed(() => [CLASS_NAMES.value.controls, errorClasses.value]);\n    const contentStyle = computed(() => {\n      let contentStyle = {};\n      if (labelWidth.value && labelAlign.value !== 'top') {\n        if (isNumber(labelWidth.value)) {\n          contentStyle = { marginLeft: `${labelWidth.value}px` };\n        } else {\n          contentStyle = { marginLeft: labelWidth.value };\n        }\n      }\n\n      return contentStyle;\n    });\n    /** Content Style END */\n\n    const errorList = ref<ErrorListType[]>([]);\n    const successList = ref<SuccessListType[]>([]);\n    const verifyStatus = ref(ValidateStatus.TO_BE_VALIDATED);\n    const resetValidating = ref(false);\n    const needResetField = ref(false);\n\n    const resetHandler = () => {\n      needResetField.value = false;\n      errorList.value = [];\n      successList.value = [];\n      verifyStatus.value = ValidateStatus.TO_BE_VALIDATED;\n    };\n    const getEmptyValue = (): ValueType => {\n      const type = Object.prototype.toString.call(lodashGet(form?.data, props.name));\n      let emptyValue: ValueType;\n      if (type === '[object String]') {\n        emptyValue = '';\n      }\n      if (type === '[object Array]') {\n        emptyValue = [];\n      }\n      if (type === '[object Object]') {\n        emptyValue = {};\n      }\n      return emptyValue;\n    };\n    const resetField = async (resetType: 'initial' | 'empty' | undefined = form?.resetType) => {\n      if (!props.name) return;\n\n      if (resetType === 'empty') lodashSet(form?.data, props.name, getEmptyValue());\n      else if (resetType === 'initial') lodashSet(form?.data, props.name, initialValue.value);\n\n      await nextTick();\n      if (resetValidating.value) {\n        needResetField.value = true;\n      } else {\n        resetHandler();\n      }\n    };\n\n    const errorMessages = computed<FormErrorMessage>(() => form?.errorMessage ?? globalConfig.value.errorMessage);\n    const innerRules = computed<FormRule[]>(() => {\n      if (props.rules?.length) return props.rules;\n      if (!props.name) return [];\n      const index = `${props.name}`.lastIndexOf('.') || -1;\n      const pRuleName = `${props.name}`.slice(index + 1);\n      return lodashGet(form?.rules, props.name) || lodashGet(form?.rules, pRuleName) || [];\n    });\n\n    const analysisValidateResult = async (trigger: ValidateTriggerType): Promise<AnalysisValidateResult> => {\n      const result: AnalysisValidateResult = {\n        successList: [],\n        errorList: [],\n        rules: [],\n        resultList: [],\n        allowSetValue: false,\n      };\n      result.rules =\n        trigger === 'all'\n          ? innerRules.value\n          : innerRules.value.filter((item) => (item.trigger || 'change') === trigger);\n      if (innerRules.value.length && !result.rules?.length) {\n        return result;\n      }\n      result.allowSetValue = true;\n      result.resultList = await validate(value.value, result.rules);\n      result.errorList = result.resultList\n        .filter((item) => item.result !== true)\n        .map((item: ErrorListType) => {\n          Object.keys(item).forEach((key) => {\n            // @ts-ignore\n            if (!item.message && errorMessages.value[key]) {\n              const name = isString(props.label) ? props.label : props.name;\n              // @ts-ignore\n              item.message = template(errorMessages.value[key], {\n                name,\n                // @ts-ignore\n                validate: item[key],\n              });\n            }\n          });\n          return item;\n        });\n      // 仅有自定义校验方法才会存在 successList\n      result.successList = result.resultList.filter(\n        (item) => item.result === true && item.message && item.type === 'success',\n      ) as SuccessListType[];\n\n      return result;\n    };\n    const validateHandler = async <T extends Data = Data>(\n      trigger: ValidateTriggerType,\n      showErrorMessage?: boolean,\n    ): Promise<FormItemValidateResult<T>> => {\n      resetValidating.value = true;\n      // undefined | boolean\n      freeShowErrorMessage.value = showErrorMessage;\n      const {\n        successList: innerSuccessList,\n        errorList: innerErrorList,\n        rules,\n        resultList,\n        allowSetValue,\n      } = await analysisValidateResult(trigger);\n\n      if (allowSetValue) {\n        successList.value = innerSuccessList;\n        errorList.value = innerErrorList;\n      }\n      // 根据校验结果设置校验状态\n      if (rules.length) {\n        verifyStatus.value = innerErrorList.length ? ValidateStatus.FAIL : ValidateStatus.SUCCESS;\n      }\n      // 重置处理\n      if (needResetField.value) {\n        resetHandler();\n      }\n      resetValidating.value = false;\n\n      return {\n        [props.name]: innerErrorList.length === 0 ? true : resultList,\n      } as FormItemValidateResult<T>;\n    };\n    const validateOnly = async <T extends Data>(trigger: ValidateTriggerType): Promise<FormItemValidateResult<T>> => {\n      const { errorList: innerErrorList, resultList } = await analysisValidateResult(trigger);\n\n      return {\n        [props.name]: innerErrorList.length === 0 ? true : resultList,\n      } as FormItemValidateResult<T>;\n    };\n\n    const setValidateMessage = (validateMessage: FormItemValidateMessage[]) => {\n      if (!validateMessage && !isArray(validateMessage)) return;\n      if (validateMessage.length === 0) {\n        errorList.value = [];\n        verifyStatus.value = ValidateStatus.SUCCESS;\n      }\n      errorList.value = validateMessage.map((item) => ({ ...item, result: false }));\n      verifyStatus.value = ValidateStatus.FAIL;\n    };\n\n    const value = computed<ValueType>(() => form?.data && lodashGet(form?.data, props.name));\n    const initialValue = ref<ValueType>(undefined);\n    const { name } = toRefs(props);\n    const context: FormItemContext = reactive({\n      name,\n      resetHandler,\n      resetField,\n      validate: validateHandler,\n      validateOnly,\n      setValidateMessage,\n    });\n\n    onMounted(() => {\n      initialValue.value = cloneDeep(value.value);\n      form?.children.push(context);\n    });\n\n    onBeforeUnmount(() => {\n      if (form) form.children = form?.children.filter((ctx) => ctx !== context);\n    });\n\n    watch(\n      value,\n      async () => {\n        await validateHandler('change');\n      },\n      { deep: true },\n    );\n\n    watch(\n      () => [props.name, JSON.stringify(props.rules)].join(','),\n      () => {\n        validateHandler('change');\n      },\n    );\n\n    const freeShowErrorMessage = ref<boolean>(undefined);\n    const showErrorMessage = computed(() => {\n      if (isBoolean(freeShowErrorMessage.value)) return freeShowErrorMessage.value;\n      if (isBoolean(props.showErrorMessage)) return props.showErrorMessage;\n      return form?.showErrorMessage;\n    });\n\n    const classes = computed(() => [\n      CLASS_NAMES.value.formItem,\n      getFormItemClassName(formItemClassPrefix.value, props.name),\n      {\n        [CLASS_NAMES.value.formItemWithHelp]: helpNode.value,\n        [CLASS_NAMES.value.formItemWithExtra]: extraNode.value,\n      },\n    ]);\n    const helpNode = computed<VNode>(() => {\n      const help = renderContent('help');\n      if (help) return <div class={CLASS_NAMES.value.help}>{help}</div>;\n      return null;\n    });\n    const extraNode = computed<VNode>(() => {\n      const getExtraNode = (content: string) => (\n        <div class={CLASS_NAMES.value.extra} title={content}>\n          {content}\n        </div>\n      );\n      const list = errorList.value;\n      if (showErrorMessage.value && list?.[0]?.message) {\n        return getExtraNode(list[0].message);\n      }\n      if (successList.value.length) {\n        return getExtraNode(successList.value[0].message);\n      }\n      return null;\n    });\n\n    const tipsNode = computed<VNode>(() => {\n      const tmpTips = renderContent('tips');\n      if (!tmpTips) return null;\n      const tmpClasses = [`${formItemClassPrefix.value}-tips`, `${classPrefix.value}-tips`, statusClass.value];\n      return <div class={tmpClasses}>{tmpTips}</div>;\n    });\n\n    const handleBlur = async () => {\n      await validateHandler('blur');\n    };\n    provide(FormItemInjectionKey, {\n      handleBlur,\n    });\n\n    return () => (\n      <div class={classes.value}>\n        {renderLabel()}\n        <div class={contentClasses.value} style={contentStyle.value}>\n          <div class={CLASS_NAMES.value.controlsContent}>\n            {renderContent('default')}\n            {renderSuffixIcon()}\n          </div>\n          {helpNode.value}\n          {tipsNode.value}\n          {extraNode.value}\n        </div>\n      </div>\n    );\n  },\n});\n"], "names": ["template", "str", "vars", "replace", "_", "prop", "trim", "_vars$prop$trim", "defineComponent", "name", "props", "setup", "slots", "_ref", "renderContent", "useTNodeJSX", "CLASS_NAMES", "useCLASSNAMES", "_useConfig", "useConfig", "globalConfig", "_useGlobalIcon", "useGlobalIcon", "CheckCircleFilledIcon", "TdCheckCircleFilledIcon", "CloseCircleFilledIcon", "TdCloseCircleFilledIcon", "ErrorCircleFilledIcon", "TdErrorCircleFilledIcon", "form", "inject", "FormInjectionKey", "classPrefix", "usePrefixClass", "formItemClassPrefix", "needRequiredMark", "computed", "_ref2", "_props2$requiredMark", "requiredMark", "value", "isRequired", "innerRules", "filter", "rule", "required", "length", "requiredMarkPosition", "_form$requiredMarkPos", "<PERSON><PERSON><PERSON><PERSON>", "label", "hasColon", "colon", "FROM_LABEL", "labelAlign", "isNil", "labelWidth", "labelClasses", "_defineProperty", "concat", "statusClass", "status", "successBorder", "renderLabel", "Number", "labelStyle", "isNumber", "width", "_createVNode", "colonText", "getDefaultIcon", "resultIcon", "Icon", "list", "errorList", "verifyStatus", "ValidateStatus", "SUCCESS", "type", "icon", "error", "warning", "success", "renderSuffixIcon", "statusIcon", "defaultNode", "params", "errorClasses", "showErrorMessage", "join", "contentClasses", "controls", "contentStyle", "marginLeft", "ref", "successList", "TO_BE_VALIDATED", "resetValidating", "needResetField", "re<PERSON><PERSON><PERSON><PERSON>", "getEmptyValue", "Object", "prototype", "toString", "call", "lodashGet", "data", "emptyValue", "reset<PERSON>ield", "_ref4", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "resetType", "_args", "arguments", "wrap", "_context", "prev", "next", "undefined", "abrupt", "lodashSet", "initialValue", "nextTick", "stop", "apply", "errorMessages", "_form$errorMessage", "errorMessage", "_props2$rules", "rules", "index", "lastIndexOf", "pRuleName", "slice", "analysisValidateResult", "_ref5", "_callee2", "trigger", "_result$rules", "result", "_context2", "resultList", "allowSetValue", "item", "validate", "map", "keys", "for<PERSON>ach", "key", "message", "isString", "_x", "validate<PERSON><PERSON><PERSON>", "_ref6", "_callee3", "_yield$analysisValida", "innerSuccessList", "innerErrorList", "_context3", "freeShowErrorMessage", "sent", "FAIL", "_x2", "_x3", "validateOnly", "_ref8", "_callee4", "_yield$analysisValida2", "_context4", "_x4", "setValidateMessage", "validateMessage", "isArray", "_objectSpread", "_toRefs", "toRefs", "context", "reactive", "onMounted", "cloneDeep", "children", "push", "onBeforeUnmount", "ctx", "watch", "_callee5", "_context5", "deep", "JSON", "stringify", "isBoolean", "classes", "formItem", "getFormItemClassName", "formItemWithHelp", "helpNode", "formItemWithExtra", "extraNode", "help", "_list$", "getExtraNode", "content", "extra", "tipsNode", "tmpTips", "tmpClasses", "handleBlur", "_callee6", "_context6", "provide", "FormItemInjectionKey", "controlsContent"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMgB,SAAAA,QAAAA,CAA2CC,KAAaC,IAAiB,EAAA;EAChF,OAAAD,GAAA,CAAIE,OAAQ,CAAA,YAAA,EAAc,UAACC,CAAA,EAAGC;;8BAAiBH,IAAK,CAAAG,IAAA,CAAKC,IAAK,EAAA,CAAA,MAAAC,IAAAA,IAAAA,eAAA,KAAAA,KAAAA,CAAAA,GAAAA,eAAA,GAAM,EAAE,CAAA;GAAA,CAAA,CAAA;AAC/E;;;;ACmDA,gBAAeC,mBAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,WAAA;AACNC,EAAAA,KAAA,EAAAA,6BAAA;AACAC,EAAAA,KAAMD,WAANC,KAAMD,CAAAA,MAAAA,EAAAA,IAAAA,EAAkB;AAAA,IAAA,IAATE,KAAA,GAAAC,IAAA,CAAAD,KAAA,CAAA;AACb,IAAA,IAAME,gBAAgBC,iBAAY,EAAA,CAAA;AAClC,IAAA,IAAMC,cAAcC,+BAAc,EAAA,CAAA;AAClC,IAAA,IAAAC,UAAA,GAAyBC,wCAAA,CAAU,MAAM,CAAA;MAAjCC,YAAA,GAAAF,UAAA,CAAAE,YAAA,CAAA;IACR,IAAAC,cAAA,GAAgFC,qBAAc,CAAA;AAC5FC,QAAAA,qBAAuB,EAAAC,yCAAA;AACvBC,QAAAA,qBAAuB,EAAAC,yCAAA;AACvBC,QAAAA,qBAAuB,EAAAC,yCAAAA;AACzB,OAAC,CAAA;MAJOL,qBAAA,GAAAF,cAAA,CAAAE,qBAAA;MAAuBE,qBAAuB,GAAAJ,cAAA,CAAvBI,qBAAuB;MAAAE,qBAAA,GAAAN,cAAA,CAAAM,qBAAA,CAAA;IAKhD,IAAAE,IAAA,GAAOC,UAAO,CAAAC,kCAAA,EAAkB,KAAS,CAAA,CAAA,CAAA;AAE/C,IAAA,IAAMC,cAAcC,sBAAe,EAAA,CAAA;AAC7B,IAAA,IAAAC,mBAAA,GAAsBD,uBAAe,WAAW,CAAA,CAAA;AAEhD,IAAA,IAAAE,gBAAA,GAAmBC,aAAS,YAAM;MAAA,IAAAC,KAAA,EAAAC,oBAAA,CAAA;AACtC,MAAA,IAAMC,gDAAe7B,MAAM,CAAA6B,YAAA,MAAA,IAAA,IAAAD,oBAAA,KAAA,KAAA,CAAA,GAAAA,oBAAA,GAAgBT,IAAM,KAANA,IAAAA,IAAAA,IAAM,KAANA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAM,CAAAU,YAAA,MAAAF,IAAAA,IAAAA,KAAA,KAAAA,KAAAA,CAAAA,GAAAA,KAAA,GAAgBjB,aAAaoB,KAAM,CAAAD,YAAA,CAAA;MAC9E,IAAAE,UAAA,GAAaC,WAAWF,KAAM,CAAAG,MAAA,CAAO,UAACC,IAAS,EAAA;QAAA,OAAAA,IAAA,CAAKC,QAAQ,CAAA;OAAA,CAAA,CAAEC,MAAS,GAAA,CAAA,CAAA;AAC7E,MAAA,OAAOP,YAAgB,KAAhBA,IAAAA,IAAAA,YAAgB,KAAhBA,KAAAA,CAAAA,GAAAA,YAAgB,GAAAE,UAAA,CAAA;AACzB,KAAC,CAAA,CAAA;AAEK,IAAA,IAAAM,oBAAA,GAAuBX,aAAS,YAAM;AAAA,MAAA,IAAAY,qBAAA,CAAA;AACnC,MAAA,OAAA,CAAAA,qBAAA,GAAAnB,IAAA,aAAAA,IAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,IAAA,CAAMkB,oBAAwB,MAAAC,IAAAA,IAAAA,qBAAA,cAAAA,qBAAA,GAAA5B,YAAA,CAAaoB,KAAM,CAAAO,oBAAA,CAAA;AAC1D,KAAC,CAAA,CAAA;IAED,IAAME,WAAWb,YAAS,CAAA,YAAA;AAAA,MAAA,OAAMxB,KAAM,CAAAsC,KAAA,IAASxC,OAAMwC,KAAK,CAAA;KAAA,CAAA,CAAA;IACpD,IAAAC,QAAA,GAAWf,aAAS,YAAA;AAAA,MAAA,OAAM,CAAC,EAAEP,IAAM,KAAA,IAAA,IAANA,IAAM,KAAA,KAAA,CAAA,IAANA,IAAM,CAAAuB,KAAA,IAASH,SAAST,KAAM,CAAA,CAAA;KAAA,CAAA,CAAA;AAC3D,IAAA,IAAAa,UAAA,GAAapB,uBAAe,aAAa,CAAA,CAAA;IACzC,IAAAqB,UAAA,GAAalB,YAAS,CAAA,YAAA;AAAA,MAAA,OAAOmB,WAAM7C,CAAAA,MAAAA,CAAM4C,UAAU,CAAI,GAAAzB,IAAA,KAAAA,IAAAA,IAAAA,IAAA,uBAAAA,IAAA,CAAMyB,UAAa5C,GAAAA,MAAAA,CAAM4C,UAAW,CAAA;KAAA,CAAA,CAAA;IAC3F,IAAAE,UAAA,GAAapB,YAAS,CAAA,YAAA;AAAA,MAAA,OAAOmB,WAAM7C,CAAAA,MAAAA,CAAM8C,UAAU,CAAI,GAAA3B,IAAA,KAAAA,IAAAA,IAAAA,IAAA,uBAAAA,IAAA,CAAM2B,UAAa9C,GAAAA,MAAAA,CAAM8C,UAAW,CAAA;KAAA,CAAA,CAAA;IAE3F,IAAAC,YAAA,GAAerB,aAAS,YAAA;MAAA,OAAM,CAClCpB,YAAYwB,KAAM,CAAAU,KAAA,EAAAQ,mCAAA,CAAAA,mCAAA,CAAAA,mCAAA,CAAAA,mCAAA,CAAAA,mCAAA,QAAAC,MAAA,CAEZN,UAAW,CAAAb,KAAA,EAAA,YAAA,CAAA,EAAoBL,gBAAiB,CAAAK,KAAA,MAAAmB,MAAA,CAChDN,UAAA,CAAWb,4BAA0BL,gBAAiB,CAAAK,KAAA,IAASO,qBAAqBP,KAAU,KAAA,OAAA,CAAAmB,EAAAA,EAAAA,CAAAA,MAAA,CAC9FN,UAAA,CAAWb,KAAe,YAAAS,QAAA,CAAST,UAAUc,UAAW,CAAAd,KAAA,KAAU,KAAS,IAAA,CAACgB,UAAW,CAAAhB,KAAA,CAAA,CAAAmB,EAAAA,EAAAA,CAAAA,MAAA,CACvFN,UAAA,CAAWb,kBAAgBc,UAAW,CAAAd,KAAA,KAAU,UAAUgB,UAAW,CAAAhB,KAAA,CAAAmB,EAAAA,EAAAA,CAAAA,MAAA,CACrEN,UAAA,CAAWb,OAAiBc,SAAAA,CAAAA,EAAAA,UAAW,CAAAd,KAAA,KAAU,WAAWgB,UAAW,CAAAhB,KAAA,CAE9E,CAAA,CAAA;KAAA,CAAA,CAAA;IAED,IAAMoB,WAAc,GAAAxB,YAAA,CAClB,YAAA;AAAA,MAAA,OAAA,EAAA,CAAAuB,MAAA,CACK3B,WAAY,CAAAQ,KAAA,EAAA,MAAA,CAAA,CAAAmB,MAAA,CAAYjD,MAAM,CAAAmD,MAAA,IAAU,SACzCnD,EAAAA,GAAAA,CAAAA,CAAAA,MAAAA,CAAAA,MAAAA,CAAMmD,MAAW,KAAA,SAAA,GAAY7C,WAAY,CAAAwB,KAAA,CAAMsB,aAAgB,GAAA,EAAA,CAAA,CAAA;AAAA,KAErE,CAAA,CAAA;AAEA,IAAA,IAAMC,cAAc,SAAdA,cAAoB;MACpB,IAAAC,MAAA,CAAOR,UAAW,CAAAhB,KAAK,CAAM,KAAA,CAAA,EAAG,OAAA;MAEpC,IAAIyB,aAAa,EAAC,CAAA;MAClB,IAAIT,UAAW,CAAAhB,KAAA,IAASc,UAAW,CAAAd,KAAA,KAAU,KAAO,EAAA;AAC9C,QAAA,IAAA0B,iBAAA,CAASV,UAAW,CAAAhB,KAAK,CAAG,EAAA;AAC9ByB,UAAAA,UAAA,GAAa;AAAEE,YAAAA,KAAA,EAAAR,EAAAA,CAAAA,MAAA,CAAUH,UAAA,CAAWhB,KAAU,EAAA,IAAA,CAAA;WAAA,CAAA;AAChD,SAAO,MAAA;AACQyB,UAAAA,UAAA,GAAA;YAAEE,KAAO,EAAAX,UAAA,CAAWhB,KAAAA;WAAM,CAAA;AACzC,SAAA;AACF,OAAA;AAEA,MAAA,OAAA4B,eAAA,CAAA,KAAA,EAAA;QAAA,OACcX,EAAAA,YAAA,CAAajB;eAAcyB,EAAAA,UAAAA;AAAA,OAAA,EAAA,CAAAG,eAAA,CAAA,OAAA,EAAA;QAAA,KACzB1D,EAAAA,MAAAA,WAAa,IAAA;AAAO,OAAA,EAAA,CAAAI,aAAA,CAAc,OAAO,CAAA,CACpDqC,CAAAA,EAAAA,QAAA,CAASX,KAAS,IAAApB,YAAA,CAAaoB,KAAM,CAAA6B,SAAA,CAAA,CAAA,CAAA;KAG5C,CAAA;AAGA,IAAA,IAAMC,iBAAiB,SAAjBA,iBAA8B;AAClC,MAAA,IAAMC,UAAa,GAAA,SAAbA,UAAaA,CAACC,IAClB,EAAA;AAAA,QAAA,OAAAJ,eAAA,CAAA,MAAA,EAAA;UAAA,OAAapD,EAAAA,WAAY,CAAAwB,KAAA,CAAMqB,MAAAA;SAC7BO,EAAAA,CAAAA,eAAA,CAAAI,IAAA,EAAA,IAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;OADD,CAAA;AAIH,MAAA,IAAMC,OAAOC,SAAU,CAAAlC,KAAA,CAAA;AACnB,MAAA,IAAAmC,YAAA,CAAanC,KAAU,KAAAoC,gCAAA,CAAeC,OAAS,EAAA;QACjD,OAAON,WAAWhD,qBAAqB,CAAA,CAAA;AACzC,OAAA;MACA,IAAIkD,iBAAAA,mBAAAA,KAAO,CAAI,CAAA,EAAA;QACP,IAAAK,IAAA,GAAOL,IAAK,CAAA,CAAA,CAAA,CAAGK,IAAQ,IAAA,OAAA,CAAA;AAC7B,QAAA,IAAMC,IAAO,GAAA;AACXC,UAAAA,KAAO,EAAAvD,qBAAA;AACPwD,UAAAA,OAAS,EAAAtD,qBAAA;AACTuD,UAAAA,OAAS,EAAA3D,qBAAAA;SACT,CAAAuD,IAAA,CAAA,CAAA;QACF,OAAOP,WAAWQ,IAAI,CAAA,CAAA;AACxB,OAAA;AACO,MAAA,OAAA,IAAA,CAAA;KACT,CAAA;AACA,IAAA,IAAMI,mBAAmB,SAAnBA,mBAAyB;AACvB,MAAA,IAAEC,aAAe1E,MAAAA,CAAf0E;MACR,IAAIA,UAAe,KAAA,KAAA,EAAO,OAAA;AAE1B,MAAA,IAAIb,aAAazD,aAAc,CAAA,YAAA,EAAc;QAAEuE,WAAa,EAAAf,cAAA,EAAe;AAAE,OAAC,CAAA,CAAA;MAC1E,IAAAC,UAAA,EAAY,OAAAH,eAAA,CAAA,MAAA,EAAA;QAAA,OAAoBpD,EAAAA,YAAYwB,KAAM,CAAAqB,MAAAA;AAAA,OAAA,EAAA,CAASU;MAC/D,IAAIA,UAAe,KAAA,KAAA,EAAO,OAAA;MAEbA,UAAA,GAAA1C,IAAA,KAAA,IAAA,IAAAA,IAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,IAAA,CAAMf,cAAc,YAAc,EAAA;QAAEuE,aAAaf,cAAe,EAAA;AAAGgB,QAAAA,MAAQ5E,EAAAA,MAAAA;AAAM,OAAC,CAAA,CAAA;MAC3F,IAAA6D,UAAA,EAAmB,OAAAA,UAAA,CAAA;KACzB,CAAA;AAIM,IAAA,IAAAgB,YAAA,GAAenD,aAAS,YAAM;AAClC,MAAA,IAAI,CAACoD,gBAAiB,CAAAhD,KAAA,EAAc,OAAA,EAAA,CAAA;AAChC,MAAA,IAAAmC,YAAA,CAAanC,KAAU,KAAAoC,gCAAA,CAAeC,OAAS,EAAA;QACjD,OAAOnE,MAAM,CAAAoD,aAAA,GACT,CAAC9C,WAAA,CAAYwB,MAAM0C,OAAS,EAAAlE,WAAA,CAAYwB,KAAM,CAAAsB,aAAa,CAAE,CAAA2B,IAAA,CAAK,GAAG,CAAA,GACrEzE,YAAYwB,KAAM,CAAA0C,OAAA,CAAA;AACxB,OAAA;AACI,MAAA,IAAA,CAACR,UAAUlC,KAAM,CAAAM,MAAA,EAAQ,OAAA;MAC7B,IAAMgC,IAAO,GAAAJ,SAAA,CAAUlC,KAAM,CAAA,CAAA,CAAA,CAAGsC,IAAQ,IAAA,OAAA,CAAA;AACxC,MAAA,IAAIpE,MAAM,CAAAmD,MAAA,EAAQ,OAAOD,WAAY,CAAApB,KAAA,CAAA;AACrC,MAAA,OAAOsC,SAAS,OAAU,GAAA9D,WAAA,CAAYwB,KAAM,CAAAwC,KAAA,GAAQhE,YAAYwB,KAAM,CAAAyC,OAAA,CAAA;AACxE,KAAC,CAAA,CAAA;IACK,IAAAS,cAAA,GAAiBtD,aAAS,YAAA;MAAA,OAAM,CAACpB,YAAYwB,KAAM,CAAAmD,QAAA,EAAUJ,YAAa,CAAA/C,KAAK,CAAC,CAAA;KAAA,CAAA,CAAA;AAChF,IAAA,IAAAoD,YAAA,GAAexD,aAAS,YAAM;MAClC,IAAIwD,gBAAe,EAAC,CAAA;MACpB,IAAIpC,UAAW,CAAAhB,KAAA,IAASc,UAAW,CAAAd,KAAA,KAAU,KAAO,EAAA;AAC9C,QAAA,IAAA0B,iBAAA,CAASV,UAAW,CAAAhB,KAAK,CAAG,EAAA;AAC9BoD,UAAAA,aAAe,GAAA;AAAEC,YAAAA,UAAY,EAAAlC,EAAAA,CAAAA,MAAA,CAAGH,WAAWhB,KAAU,EAAA,IAAA,CAAA;WAAA,CAAA;AACvD,SAAO,MAAA;AACLoD,UAAAA,aAAe,GAAA;YAAEC,UAAY,EAAArC,UAAA,CAAWhB,KAAAA;WAAM,CAAA;AAChD,SAAA;AACF,OAAA;AAEOoD,MAAAA,OAAAA,aAAAA,CAAAA;AACT,KAAC,CAAA,CAAA;AAGK,IAAA,IAAAlB,SAAA,GAAYoB,OAAqB,CAAA,EAAE,CAAA,CAAA;AACnC,IAAA,IAAAC,WAAA,GAAcD,OAAuB,CAAA,EAAE,CAAA,CAAA;AACvC,IAAA,IAAAnB,YAAA,GAAemB,OAAI,CAAAlB,gCAAA,CAAeoB,eAAe,CAAA,CAAA;AACjD,IAAA,IAAAC,eAAA,GAAkBH,QAAI,KAAK,CAAA,CAAA;AAC3B,IAAA,IAAAI,cAAA,GAAiBJ,QAAI,KAAK,CAAA,CAAA;AAEhC,IAAA,IAAMK,eAAe,SAAfA,eAAqB;MACzBD,cAAA,CAAe1D,KAAQ,GAAA,KAAA,CAAA;MACvBkC,SAAA,CAAUlC,QAAQ,EAAC,CAAA;MACnBuD,WAAA,CAAYvD,QAAQ,EAAC,CAAA;AACrBmC,MAAAA,YAAA,CAAanC,QAAQoC,gCAAe,CAAAoB,eAAA,CAAA;KACtC,CAAA;AACA,IAAA,IAAMI,gBAAgB,SAAhBA,gBAAiC;MAC/B,IAAAtB,IAAA,GAAOuB,MAAO,CAAAC,SAAA,CAAUC,QAAS,CAAAC,IAAA,CAAKC,QAAU5E,IAAM,KAANA,IAAAA,IAAAA,IAAM,KAANA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAM,CAAA6E,IAAA,EAAMhG,MAAM,CAAAD,IAAI,CAAC,CAAA,CAAA;AACzE,MAAA,IAAAkG,UAAA,CAAA;MACJ,IAAI7B,SAAS,iBAAmB,EAAA;AACjB6B,QAAAA,UAAA,GAAA,EAAA,CAAA;AACf,OAAA;MACA,IAAI7B,SAAS,gBAAkB,EAAA;AAC7B6B,QAAAA,UAAA,GAAa,EAAC,CAAA;AAChB,OAAA;MACA,IAAI7B,SAAS,iBAAmB,EAAA;QAC9B6B,UAAA,GAAa,EAAC,CAAA;AAChB,OAAA;AACO,MAAA,OAAAA,UAAA,CAAA;KACT,CAAA;AACA,IAAA,IAAMC,UAAa,gBAAA,YAAA;MAAA,IAAAC,KAAA,GAAAC,qCAAA,cAAAC,uCAAA,CAAAC,IAAA,CAAA,SAAAC,OAAA,GAAA;AAAA,QAAA,IAAAC,SAAA;AAAAC,UAAAA,KAAA,GAAAC,SAAA,CAAA;AAAA,QAAA,OAAAL,uCAAA,CAAAM,IAAA,CAAA,UAAAC,QAAA,EAAA;AAAA,UAAA,OAAA,CAAA,EAAA,QAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;AAAA,YAAA,KAAA,CAAA;AAAON,cAAAA,SAA6C,GAAAC,KAAA,CAAArE,MAAA,GAAA,CAAA,IAAAqE,KAAA,CAAAM,CAAAA,CAAAA,KAAAA,SAAA,GAAAN,KAAA,MAAAtF,IAAA,KAAA,IAAA,IAAAA,IAAA,KAAAA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAA,CAAMqF,SAAc,CAAA;cAAA,IACpFxG,MAAM,CAAAD,IAAA,EAAA;AAAA6G,gBAAAA,QAAA,CAAAE,IAAA,GAAA,CAAA,CAAA;AAAA,gBAAA,MAAA;AAAA,eAAA;cAAA,OAAAF,QAAA,CAAAI,MAAA,CAAA,QAAA,CAAA,CAAA;AAAA,YAAA,KAAA,CAAA;cAEX,IAAIR,SAAc,KAAA,OAAA,EAASS,OAAA,CAAU9F,IAAM,aAANA,IAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAANA,IAAM,CAAA6E,IAAA,EAAMhG,MAAM,CAAAD,IAAA,EAAM2F,eAAe,CAAA,CAAA,KAAA,IACnEc,SAAc,KAAA,SAAA,EAAWS,OAAA,CAAU9F,IAAM,aAANA,IAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAANA,IAAM,CAAA6E,IAAA,EAAMhG,MAAM,CAAAD,IAAA,EAAMmH,aAAapF,KAAK,CAAA,CAAA;AAAA8E,cAAAA,QAAA,CAAAE,IAAA,GAAA,CAAA,CAAA;cAAA,OAEhFK,YAAS,EAAA,CAAA;AAAA,YAAA,KAAA,CAAA;cACf,IAAI5B,gBAAgBzD,KAAO,EAAA;gBACzB0D,cAAA,CAAe1D,KAAQ,GAAA,IAAA,CAAA;AACzB,eAAO,MAAA;AACQ2D,gBAAAA,YAAA,EAAA,CAAA;AACf,eAAA;AAAA,YAAA,KAAA,CAAA,CAAA;AAAA,YAAA,KAAA,KAAA;cAAA,OAAAmB,QAAA,CAAAQ,IAAA,EAAA,CAAA;AAAA,WAAA;AAAA,SAAA,EAAAb,OAAA,CAAA,CAAA;OACF,CAAA,CAAA,CAAA;AAAA,MAAA,OAAA,SAZML,UAAaA,GAAA;AAAA,QAAA,OAAAC,KAAA,CAAAkB,KAAA,CAAA,IAAA,EAAAX,SAAA,CAAA,CAAA;AAAA,OAAA,CAAA;KAYnB,EAAA,CAAA;IAEA,IAAMY,gBAAgB5F,YAA2B,CAAA,YAAA;AAAA,MAAA,IAAA6F,kBAAA,CAAA;AAAA,MAAA,OAAA,CAAAA,kBAAA,GAAMpG,iBAAAA,2BAAAA,KAAMqG,YAAgB,MAAAD,IAAAA,IAAAA,kBAAA,cAAAA,kBAAA,GAAA7G,YAAA,CAAaoB,MAAM0F,YAAY,CAAA;KAAA,CAAA,CAAA;AACtG,IAAA,IAAAxF,UAAA,GAAaN,aAAqB,YAAM;AAAA,MAAA,IAAA+F,aAAA,CAAA;AAC5C,MAAA,IAAA,CAAAA,aAAA,GAAIzH,OAAM0H,KAAO,MAAAD,IAAAA,IAAAA,aAAA,KAAbzH,KAAAA,CAAAA,IAAAA,aAAAA,CAAaoC,MAAA,EAAQ,OAAOpC,MAAM,CAAA0H,KAAA,CAAA;AACtC,MAAA,IAAI,CAAC1H,MAAM,CAAAD,IAAA,EAAM,OAAO,EAAC,CAAA;AACzB,MAAA,IAAM4H,QAAQ,EAAA1E,CAAAA,MAAA,CAAGjD,MAAAA,CAAMD,IAAO,CAAA,CAAA6H,WAAA,CAAY,GAAG,CAAK,IAAA,CAAA,CAAA,CAAA;AAClD,MAAA,IAAMC,YAAY,EAAA5E,CAAAA,MAAA,CAAGjD,MAAAA,CAAMD,IAAO,CAAA,CAAA+H,KAAA,CAAMH,QAAQ,CAAC,CAAA,CAAA;AAC1C,MAAA,OAAA5B,OAAA,CAAU5E,IAAM,KAAA,IAAA,IAANA,IAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAANA,IAAM,CAAAuG,KAAA,EAAO1H,MAAM,CAAAD,IAAI,CAAK,IAAAgG,OAAA,CAAU5E,IAAM,KAANA,IAAAA,IAAAA,IAAM,KAANA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAM,CAAAuG,KAAA,EAAOG,SAAS,CAAA,IAAK,EAAC,CAAA;AACrF,KAAC,CAAA,CAAA;AAEK,IAAA,IAAAE,sBAAA,gBAAA,YAAA;MAAA,IAAAC,KAAA,GAAA5B,qCAAA,cAAAC,uCAAA,CAAAC,IAAA,CAAyB,SAAA2B,QAAAA,CAAOC,OAAkE,EAAA;AAAA,QAAA,IAAAC,aAAA,CAAA;AAAA,QAAA,IAAAC,MAAA,CAAA;AAAA,QAAA,OAAA/B,uCAAA,CAAAM,IAAA,CAAA,UAAA0B,SAAA,EAAA;AAAA,UAAA,OAAA,CAAA,EAAA,QAAAA,SAAA,CAAAxB,IAAA,GAAAwB,SAAA,CAAAvB,IAAA;AAAA,YAAA,KAAA,CAAA;AAChGsB,cAAAA,MAAiC,GAAA;AACrC/C,gBAAAA,aAAa,EAAC;AACdrB,gBAAAA,WAAW,EAAC;AACZ0D,gBAAAA,OAAO,EAAC;AACRY,gBAAAA,YAAY,EAAC;AACbC,gBAAAA,aAAe,EAAA,KAAA;eACjB,CAAA;AACAH,cAAAA,MAAA,CAAOV,KACL,GAAAQ,OAAA,KAAY,KACR,GAAAlG,UAAA,CAAWF,KACX,GAAAE,UAAA,CAAWF,KAAM,CAAAG,MAAA,CAAO,UAACuG,IAAA,EAAA;AAAA,gBAAA,OAAA,CAAUA,IAAK,CAAAN,OAAA,IAAW,cAAcA,OAAO,CAAA;eAAA,CAAA,CAAA;AAAA,cAAA,IAAA,EAC1ElG,WAAWF,KAAM,CAAAM,MAAA,IAAU,EAAA+F,CAAAA,aAAA,GAACC,MAAA,CAAOV,qCAAPS,KAAAA,CAAAA,IAAAA,aAAA,CAAc/F,MAAQ,CAAA,CAAA,EAAA;AAAAiG,gBAAAA,SAAA,CAAAvB,IAAA,GAAA,CAAA,CAAA;AAAA,gBAAA,MAAA;AAAA,eAAA;AAAA,cAAA,OAAAuB,SAAA,CAAArB,MAAA,CAAA,QAAA,EAC7CoB,MAAA,CAAA,CAAA;AAAA,YAAA,KAAA,CAAA;cAETA,MAAA,CAAOG,aAAgB,GAAA,IAAA,CAAA;AAAAF,cAAAA,SAAA,CAAAvB,IAAA,GAAA,CAAA,CAAA;cAAA,OACG2B,6BAAA,CAAS3G,KAAM,CAAAA,KAAA,EAAOsG,OAAOV,KAAK,CAAA,CAAA;AAAA,YAAA,KAAA,CAAA;AAA5DU,cAAAA,MAAA,CAAOE;cACPF,MAAA,CAAOpE,SAAY,GAAAoE,MAAA,CAAOE,UACvB,CAAArG,MAAA,CAAO,UAACuG,IAAA,EAAA;AAAA,gBAAA,OAASA,IAAK,CAAAJ,MAAA,KAAW,IAAI,CAAA;AAAA,eAAA,CAAA,CACrCM,GAAI,CAAA,UAACF,IAAwB,EAAA;gBAC5B7C,MAAA,CAAOgD,IAAK,CAAAH,IAAI,CAAE,CAAAI,OAAA,CAAQ,UAACC,GAAQ,EAAA;kBAEjC,IAAI,CAACL,IAAA,CAAKM,OAAW,IAAAxB,aAAA,CAAcxF,MAAM+G,GAAM,CAAA,EAAA;AAC7C,oBAAA,IAAM9I,QAAOgJ,iBAAS/I,CAAAA,MAAAA,CAAMwC,KAAK,CAAIxC,GAAAA,MAAAA,CAAMwC,QAAQxC,MAAM,CAAAD,IAAA,CAAA;oBAEzDyI,IAAA,CAAKM,OAAU,GAAAxJ,QAAA,CAASgI,aAAc,CAAAxF,KAAA,CAAM+G,GAAM,CAAA,EAAA;AAChD9I,sBAAAA,IAAAA,EAAAA,KAAAA;sBAEA0I,UAAUD,IAAK,CAAAK,GAAA,CAAA;AACjB,qBAAC,CAAA,CAAA;AACH,mBAAA;AACF,iBAAC,CAAA,CAAA;AACM,gBAAA,OAAAL,IAAA,CAAA;AACT,eAAC,CAAA,CAAA;cAEIJ,MAAA,CAAA/C,WAAA,GAAc+C,OAAOE,UAAW,CAAArG,MAAA,CACrC,UAACuG;uBAASA,IAAK,CAAAJ,MAAA,KAAW,QAAQI,IAAK,CAAAM,OAAA,IAAWN,KAAKpE,IAAS,KAAA,SAAA,CAAA;AAAA,eAClE,CAAA,CAAA;AAAA,cAAA,OAAAiE,SAAA,CAAArB,MAAA,CAAA,QAAA,EAEOoB,MAAA,CAAA,CAAA;AAAA,YAAA,KAAA,CAAA,CAAA;AAAA,YAAA,KAAA,KAAA;cAAA,OAAAC,SAAA,CAAAjB,IAAA,EAAA,CAAA;AAAA,WAAA;AAAA,SAAA,EAAAa,QAAA,CAAA,CAAA;OACT,CAAA,CAAA,CAAA;MAAA,OAxCMF,SAAAA,sBAAAA,CAAAiB,EAAA,EAAA;AAAA,QAAA,OAAAhB,KAAA,CAAAX,KAAA,CAAA,IAAA,EAAAX,SAAA,CAAA,CAAA;AAAA,OAAA,CAAA;KAwCN,EAAA,CAAA;AACM,IAAA,IAAAuC,eAAA,gBAAA,YAAA;AAAA,MAAA,IAAAC,KAAA,GAAA9C,qCAAA,cAAAC,uCAAA,CAAAC,IAAA,CAAkB,SAAA6C,QAAAA,CACtBjB,OAAA,EACApD,iBACuC,EAAA;QAAA,IAAAsE,qBAAA,EAAAC,gBAAA,EAAAC,cAAA,EAAA5B,KAAA,EAAAY,UAAA,EAAAC,aAAA,CAAA;AAAA,QAAA,OAAAlC,uCAAA,CAAAM,IAAA,CAAA,UAAA4C,SAAA,EAAA;AAAA,UAAA,OAAA,CAAA,EAAA,QAAAA,SAAA,CAAA1C,IAAA,GAAA0C,SAAA,CAAAzC,IAAA;AAAA,YAAA,KAAA,CAAA;cACvCvB,eAAA,CAAgBzD,KAAQ,GAAA,IAAA,CAAA;cAExB0H,oBAAA,CAAqB1H,KAAQgD,GAAAA,iBAAAA,CAAAA;AAAAA,cAAAA,SAAAA,CAAAA,IAAAA,GAAAA,CAAAA,CAAAA;cAAAA,OAOnBiD,sBAAA,CAAuBG,OAAO,CAAA,CAAA;AAAA,YAAA,KAAA,CAAA;cAAAkB,qBAAA,GAAAG,SAAA,CAAAE,IAAA,CAAA;cALzBJ,gBAAA,GAAAD,qBAAA,CAAb/D,WAAa,CAAA;cACFiE,cAAA,GAAAF,qBAAA,CAAXpF,SAAW,CAAA;cACX0D,KAAA,GAAA0B,qBAAA,CAAA1B,KAAA,CAAA;cACAY,UAAA,GAAAc,qBAAA,CAAAd,UAAA,CAAA;cACAC,aAAA,GAAAa,qBAAA,CAAAb,aAAA,CAAA;AAGF,cAAA,IAAIA,aAAe,EAAA;gBACjBlD,WAAA,CAAYvD,KAAQ,GAAAuH,gBAAA,CAAA;gBACpBrF,SAAA,CAAUlC,KAAQ,GAAAwH,cAAA,CAAA;AACpB,eAAA;cAEA,IAAI5B,MAAMtF,MAAQ,EAAA;AAChB6B,gBAAAA,YAAA,CAAanC,KAAQ,GAAAwH,cAAA,CAAelH,MAAS,GAAA8B,gCAAA,CAAewF,OAAOxF,gCAAe,CAAAC,OAAA,CAAA;AACpF,eAAA;cAEA,IAAIqB,eAAe1D,KAAO,EAAA;AACX2D,gBAAAA,YAAA,EAAA,CAAA;AACf,eAAA;cACAF,eAAA,CAAgBzD,KAAQ,GAAA,KAAA,CAAA;AAAA,cAAA,OAAAyH,SAAA,CAAAvC,MAAA,WAAAhE,mCAAA,CAAA,EAAA,EAGrBhD,MAAM,CAAAD,IAAA,EAAOuJ,cAAe,CAAAlH,MAAA,KAAW,IAAI,IAAO,GAAAkG,UAAA,CAAA,CAAA,CAAA;AAAA,YAAA,KAAA,CAAA,CAAA;AAAA,YAAA,KAAA,KAAA;cAAA,OAAAiB,SAAA,CAAAnC,IAAA,EAAA,CAAA;AAAA,WAAA;AAAA,SAAA,EAAA+B,QAAA,CAAA,CAAA;OAEvD,CAAA,CAAA,CAAA;AAAA,MAAA,OAAA,SAhCMF,eAAAA,CAAAU,GAAA,EAAAC,GAAA,EAAA;AAAA,QAAA,OAAAV,KAAA,CAAA7B,KAAA,CAAA,IAAA,EAAAX,SAAA,CAAA,CAAA;AAAA,OAAA,CAAA;KAgCN,EAAA,CAAA;AACM,IAAA,IAAAmD,YAAA,gBAAA,YAAA;MAAA,IAAAC,KAAA,GAAA1D,qCAAA,cAAAC,uCAAA,CAAAC,IAAA,CAAe,SAAAyD,QAAAA,CAAuB7B,OAAqE,EAAA;AAAA,QAAA,IAAA8B,sBAAA,EAAAV,cAAA,EAAAhB,UAAA,CAAA;AAAA,QAAA,OAAAjC,uCAAA,CAAAM,IAAA,CAAA,UAAAsD,SAAA,EAAA;AAAA,UAAA,OAAA,CAAA,EAAA,QAAAA,SAAA,CAAApD,IAAA,GAAAoD,SAAA,CAAAnD,IAAA;AAAA,YAAA,KAAA,CAAA;AAAAmD,cAAAA,SAAA,CAAAnD,IAAA,GAAA,CAAA,CAAA;cAAA,OACvDiB,uBAAuBG,OAAO,CAAA,CAAA;AAAA,YAAA,KAAA,CAAA;cAAA8B,sBAAA,GAAAC,SAAA,CAAAR,IAAA,CAAA;cAAnEH,cAAA,GAAAU,sBAAA,CAAXhG,SAAW,CAAA;cAAgBsE,oCAAAA;wFAGhCtI,MAAM,CAAAD,IAAA,EAAOuJ,cAAe,CAAAlH,MAAA,KAAW,IAAI,IAAO,GAAAkG,UAAA,CAAA,CAAA,CAAA;AAAA,YAAA,KAAA,CAAA,CAAA;AAAA,YAAA,KAAA,KAAA;cAAA,OAAA2B,SAAA,CAAA7C,IAAA,EAAA,CAAA;AAAA,WAAA;AAAA,SAAA,EAAA2C,QAAA,CAAA,CAAA;OAEvD,CAAA,CAAA,CAAA;MAAA,OANMF,SAAAA,YAAAA,CAAAK,GAAA,EAAA;AAAA,QAAA,OAAAJ,KAAA,CAAAzC,KAAA,CAAA,IAAA,EAAAX,SAAA,CAAA,CAAA;AAAA,OAAA,CAAA;KAMN,EAAA,CAAA;AAEM,IAAA,IAAAyD,kBAAA,GAAqB,SAArBA,kBAAAA,CAAsBC,eAA+C,EAAA;MACzE,IAAI,CAACA,eAAA,IAAmB,CAACC,eAAA,CAAQD,eAAe,CAAA,EAAG,OAAA;AAC/C,MAAA,IAAAA,eAAA,CAAgBhI,WAAW,CAAG,EAAA;QAChC4B,SAAA,CAAUlC,QAAQ,EAAC,CAAA;AACnBmC,QAAAA,YAAA,CAAanC,QAAQoC,gCAAe,CAAAC,OAAA,CAAA;AACtC,OAAA;MACUH,SAAA,CAAAlC,KAAA,GAAQsI,eAAgB,CAAA1B,GAAA,CAAI,UAACF,IAAA,EAAA;AAAA,QAAA,OAAA8B,aAAA,CAAAA,aAAA,CAAA,EAAA,EAAe9B,IAAA,CAAA,EAAA,EAAA,EAAA;AAAMJ,UAAAA,MAAQ,EAAA,KAAA;AAAA,SAAA,CAAA,CAAA;AAAA,OAAQ,CAAA,CAAA;AAC5EnE,MAAAA,YAAA,CAAanC,QAAQoC,gCAAe,CAAAwF,IAAA,CAAA;KACtC,CAAA;IAEM,IAAA5H,KAAA,GAAQJ,YAAoB,CAAA,YAAA;MAAA,OAAM,CAAAP,IAAM,KAANA,IAAAA,IAAAA,IAAM,uBAANA,IAAM,CAAA6E,IAAA,KAAQD,QAAU5E,IAAM,KAANA,IAAAA,IAAAA,IAAM,uBAANA,IAAM,CAAA6E,IAAA,EAAMhG,MAAM,CAAAD,IAAI,CAAC,CAAA;KAAA,CAAA,CAAA;AACjF,IAAA,IAAAmH,YAAA,GAAe9B,QAAe,KAAS,CAAA,CAAA,CAAA;AAC7C,IAAA,IAAAmF,OAAA,GAAiBC,UAAA,CAAOxK,MAAK,CAAA;MAArBD,IAAA,GAAAwK,OAAA,CAAAxK,IAAA,CAAA;IACR,IAAM0K,UAA2BC,YAAS,CAAA;AACxC3K,MAAAA,IAAA,EAAAA,IAAA;AACA0F,MAAAA,YAAA,EAAAA,YAAA;AACAS,MAAAA,UAAA,EAAAA,UAAA;AACAuC,MAAAA,QAAU,EAAAQ,eAAA;AACVY,MAAAA,YAAA,EAAAA,YAAA;AACAM,MAAAA,kBAAA,EAAAA,kBAAAA;AACF,KAAC,CAAA,CAAA;AAEDQ,IAAAA,aAAA,CAAU,YAAM;MACDzD,YAAA,CAAApF,KAAA,GAAQ8I,mBAAU,CAAA9I,KAAA,CAAMA,KAAK,CAAA,CAAA;MACpCX,IAAA,KAAA,IAAA,IAAAA,IAAA,KAAA,KAAA,CAAA,IAAAA,IAAA,CAAA0J,QAAA,CAASC,KAAKL,OAAO,CAAA,CAAA;AAC7B,KAAC,CAAA,CAAA;AAEDM,IAAAA,mBAAA,CAAgB,YAAM;AAChB,MAAA,IAAA5J,IAAA,EAAMA,IAAA,CAAK0J,WAAW1J,IAAM,KAAA,IAAA,IAANA,IAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAANA,IAAM,CAAA0J,QAAA,CAAS5I,OAAO,UAAC+I,GAAA,EAAA;QAAA,OAAQA,QAAQP,OAAO,CAAA;OAAA,CAAA,CAAA;AAC1E,KAAC,CAAA,CAAA;IAEDQ,SAAA,CACEnJ,KAAA,eAAAsE,qCAAA,cAAAC,uCAAA,CAAAC,IAAA,CACA,SAAA4E,QAAA,GAAA;AAAA,MAAA,OAAA7E,uCAAA,CAAAM,IAAA,CAAA,UAAAwE,SAAA,EAAA;AAAA,QAAA,OAAA,CAAA,EAAA,QAAAA,SAAA,CAAAtE,IAAA,GAAAsE,SAAA,CAAArE,IAAA;AAAA,UAAA,KAAA,CAAA;AAAAqE,YAAAA,SAAA,CAAArE,IAAA,GAAA,CAAA,CAAA;YAAA,OACQmC,gBAAgB,QAAQ,CAAA,CAAA;AAAA,UAAA,KAAA,CAAA,CAAA;AAAA,UAAA,KAAA,KAAA;YAAA,OAAAkC,SAAA,CAAA/D,IAAA,EAAA,CAAA;AAAA,SAAA;AAAA,OAAA,EAAA8D,QAAA,CAAA,CAAA;AAAA,KAChC,CACA,CAAA,EAAA;AAAEE,MAAAA,MAAM,IAAA;AAAK,KACf,CAAA,CAAA;AAEAH,IAAAA,SAAA,CACE,YAAA;AAAA,MAAA,OAAM,CAACjL,MAAAA,CAAMD,IAAM,EAAAsL,IAAA,CAAKC,SAAUtL,CAAAA,MAAAA,CAAM0H,KAAK,CAAC,CAAE,CAAA3C,IAAA,CAAK,GAAG,CAAA,CAAA;AAAA,KAAA,EACxD,YAAM;MACJkE,eAAA,CAAgB,QAAQ,CAAA,CAAA;AAC1B,KACF,CAAA,CAAA;AAEM,IAAA,IAAAO,oBAAA,GAAuBpE,QAAa,KAAS,CAAA,CAAA,CAAA;AAC7C,IAAA,IAAAN,gBAAA,GAAmBpD,aAAS,YAAM;MAClC,IAAA6J,mBAAA,CAAU/B,qBAAqB1H,KAAK,CAAA,EAAG,OAAO0H,oBAAqB,CAAA1H,KAAA,CAAA;MACnE,IAAAyJ,mBAAA,CAAUvL,OAAM8E,gBAAgB,CAAA,EAAG,OAAO9E,MAAM,CAAA8E,gBAAA,CAAA;AACpD,MAAA,OAAO3D,IAAM,KAANA,IAAAA,IAAAA,IAAM,KAANA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAM,CAAA2D,gBAAA,CAAA;AACf,KAAC,CAAA,CAAA;IAEK,IAAA0G,OAAA,GAAU9J,aAAS,YAAA;AAAA,MAAA,OAAM,CAC7BpB,YAAYwB,KAAM,CAAA2J,QAAA,EAClBC,wCAAqB,CAAAlK,mBAAA,CAAoBM,KAAO9B,EAAAA,MAAAA,CAAMD,IAAI,CAAA,EAAAiD,mCAAA,CAAAA,mCAAA,CAAA,EAAA,EAEvD1C,WAAA,CAAYwB,KAAM,CAAA6J,gBAAA,EAAmBC,QAAS,CAAA9J,KAAA,GAC9CxB,WAAA,CAAYwB,KAAM,CAAA+J,iBAAA,EAAoBC,SAAU,CAAAhK,KAAA,CAEpD,CAAA,CAAA;KAAA,CAAA,CAAA;AACK,IAAA,IAAA8J,QAAA,GAAWlK,aAAgB,YAAM;AAC/B,MAAA,IAAAqK,IAAA,GAAO3L,cAAc,MAAM,CAAA,CAAA;MAC7B,IAAA2L,IAAA,EAAM,OAAArI,eAAA,CAAA,KAAA,EAAA;QAAA,OAAmBpD,EAAAA,YAAYwB,KAAM,CAAAiK,IAAAA;AAAA,OAAA,EAAA,CAAOA;AAC/C,MAAA,OAAA,IAAA,CAAA;AACT,KAAC,CAAA,CAAA;AACK,IAAA,IAAAD,SAAA,GAAYpK,aAAgB,YAAM;AAAA,MAAA,IAAAsK,MAAA,CAAA;AACtC,MAAA,IAAMC,YAAe,GAAA,SAAfA,YAAeA,CAACC,OACpB,EAAA;AAAA,QAAA,OAAAxI,eAAA,CAAA,KAAA,EAAA;AAAA,UAAA,OAAA,EAAYpD,WAAY,CAAAwB,KAAA,CAAMqK,KAAO;UAAA,OAAOD,EAAAA,OAAAA;AACzC,SAAA,EAAA,CAAAA,OAAA,CAAA,CAAA,CAAA;OADF,CAAA;AAIH,MAAA,IAAMnI,OAAOC,SAAU,CAAAlC,KAAA,CAAA;MACvB,IAAIgD,gBAAiB,CAAAhD,KAAA,IAASiC,IAAO,KAAPA,IAAAA,IAAAA,IAAO,gBAAAiI,MAAA,GAAPjI,IAAO,CAAA,CAAA,CAAA,MAAAiI,IAAAA,IAAAA,MAAA,eAAPA,MAAA,CAAWlD,OAAS,EAAA;QACzC,OAAAmD,YAAA,CAAalI,IAAK,CAAA,CAAA,CAAA,CAAG+E,OAAO,CAAA,CAAA;AACrC,OAAA;AACI,MAAA,IAAAzD,WAAA,CAAYvD,MAAMM,MAAQ,EAAA;QAC5B,OAAO6J,YAAa,CAAA5G,WAAA,CAAYvD,KAAM,CAAA,CAAA,CAAA,CAAGgH,OAAO,CAAA,CAAA;AAClD,OAAA;AACO,MAAA,OAAA,IAAA,CAAA;AACT,KAAC,CAAA,CAAA;AAEK,IAAA,IAAAsD,QAAA,GAAW1K,aAAgB,YAAM;AAC/B,MAAA,IAAA2K,OAAA,GAAUjM,cAAc,MAAM,CAAA,CAAA;AACpC,MAAA,IAAI,CAACiM,OAAA,EAAgB,OAAA,IAAA,CAAA;AACf,MAAA,IAAAC,UAAA,GAAa,CAAA,EAAA,CAAArJ,MAAA,CAAIzB,mBAAA,CAAoBM,2BAAiBR,WAAA,CAAYQ,KAAc,YAAAoB,WAAA,CAAYpB,KAAK,CAAA,CAAA;AACvG,MAAA,OAAA4B,eAAA,CAAA,KAAA,EAAA;QAAA,OAAmB4I,EAAAA,UAAAA;AAAA,OAAA,EAAA,CAAaD;AAClC,KAAC,CAAA,CAAA;AAED,IAAA,IAAME;mHAAa,SAAAC,QAAA,GAAA;AAAA,QAAA,OAAAnG,uCAAA,CAAAM,IAAA,CAAA,UAAA8F,SAAA,EAAA;AAAA,UAAA,OAAA,CAAA,EAAA,QAAAA,SAAA,CAAA5F,IAAA,GAAA4F,SAAA,CAAA3F,IAAA;AAAA,YAAA,KAAA,CAAA;AAAA2F,cAAAA,SAAA,CAAA3F,IAAA,GAAA,CAAA,CAAA;cAAA,OACXmC,gBAAgB,MAAM,CAAA,CAAA;AAAA,YAAA,KAAA,CAAA,CAAA;AAAA,YAAA,KAAA,KAAA;cAAA,OAAAwD,SAAA,CAAArF,IAAA,EAAA,CAAA;AAAA,WAAA;AAAA,SAAA,EAAAoF,QAAA,CAAA,CAAA;OAC9B,CAAA,CAAA,CAAA;AAAA,MAAA,OAAA,SAFMD;;;KAEN,EAAA,CAAA;IACAG,WAAA,CAAQC,sCAAsB,EAAA;AAC5BJ,MAAAA,UAAA,EAAAA,UAAAA;AACF,KAAC,CAAA,CAAA;IAED,OAAO,YAAA;AAAA,MAAA,OAAA7I,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EACO8H,OAAQ,CAAA1J,KAAAA;AAAA,OAAA,EAAA,CACjBuB,WAAY,EAAA,EAAAK,eAAA,CAAA,KAAA,EAAA;QAAA,OACDsB,EAAAA,cAAe,CAAAlD,KAAA;AAAA,QAAA,OAAA,EAAcoD,YAAa,CAAApD,KAAAA;AAAA,OAAA,EAAA,CAAA4B,eAAA,CAAA,KAAA,EAAA;QAAA,OACxCpD,EAAAA,WAAA,CAAYwB,KAAM,CAAA8K,eAAAA;AAAA,OAAA,EAAA,CAC3BxM,cAAc,SAAS,CAAA,EACvBqE,gBAAiB,EAAA,CAAA,CAAA,EAEnBmH,QAAS,CAAA9J,KAAA,EACTsK,QAAS,CAAAtK,KAAA,EACTgK,SAAU,CAAAhK,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;KATd,CAAA;AAaL,GAAA;AACF,CAAC,CAAA;;;;"}