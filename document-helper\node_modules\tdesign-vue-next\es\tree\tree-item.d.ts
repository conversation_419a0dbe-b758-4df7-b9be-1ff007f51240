import { TypePropType, TypeCreateElement } from './utils/adapt';
import { TypeTreeItemProps } from './types';
export declare const treeItemProps: {
    stateId: {
        type: TypePropType<TypeTreeItemProps["stateId"]>;
    };
    itemKey: {
        type: TypePropType<TypeTreeItemProps["itemKey"]>;
    };
    rowIndex: {
        type: TypePropType<TypeTreeItemProps["rowIndex"]>;
    };
    treeScope: {
        type: TypePropType<TypeTreeItemProps["treeScope"]>;
    };
};
declare const _default: import("vue").DefineComponent<{
    stateId: {
        type: TypePropType<TypeTreeItemProps["stateId"]>;
    };
    itemKey: {
        type: TypePropType<TypeTreeItemProps["itemKey"]>;
    };
    rowIndex: {
        type: TypePropType<TypeTreeItemProps["rowIndex"]>;
    };
    treeScope: {
        type: TypePropType<TypeTreeItemProps["treeScope"]>;
    };
}, {
    treeItemRef: import("./utils/adapt").TypeRef<HTMLDivElement>;
    renderItemNode: (h: TypeCreateElement) => JSX.Element;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    stateId: {
        type: TypePropType<TypeTreeItemProps["stateId"]>;
    };
    itemKey: {
        type: TypePropType<TypeTreeItemProps["itemKey"]>;
    };
    rowIndex: {
        type: TypePropType<TypeTreeItemProps["rowIndex"]>;
    };
    treeScope: {
        type: TypePropType<TypeTreeItemProps["treeScope"]>;
    };
}>>, {}, {}>;
export default _default;
