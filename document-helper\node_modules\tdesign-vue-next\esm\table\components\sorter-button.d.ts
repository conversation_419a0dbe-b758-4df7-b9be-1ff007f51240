import { PropType } from 'vue';
import { SortType } from '../type';
import { TooltipProps } from '../../tooltip';
import type { TNode } from '../../common';
import type { TableConfig } from '../../config-provider';
declare const _default: import("vue").DefineComponent<{
    sortType: {
        type: PropType<SortType>;
        default: string;
    };
    sortOrder: {
        type: StringConstructor;
        default: () => string;
    };
    locale: PropType<TableConfig>;
    sortIcon: PropType<TNode>;
    tooltipProps: PropType<TooltipProps>;
    hideSortTips: BooleanConstructor;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "sort-icon-click"[], "sort-icon-click", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    sortType: {
        type: PropType<SortType>;
        default: string;
    };
    sortOrder: {
        type: StringConstructor;
        default: () => string;
    };
    locale: PropType<TableConfig>;
    sortIcon: PropType<TNode>;
    tooltipProps: PropType<TooltipProps>;
    hideSortTips: BooleanConstructor;
}>> & {
    "onSort-icon-click"?: (...args: any[]) => any;
}, {
    hideSortTips: boolean;
    sortType: SortType;
    sortOrder: string;
}, {}>;
export default _default;
