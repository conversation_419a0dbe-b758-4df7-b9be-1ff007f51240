{"version": 3, "file": "verify.js", "sources": ["../../src/components/verify.tsx"], "sourcesContent": ["import { computed, PropType, defineComponent } from 'vue';\nimport renderFn from '../utils/render-fn';\nimport {\n  TdIconSVGProps, SVGJson,\n} from '../utils/types';\nimport useSizeProps from '../utils/use-size-props';\n\nimport '../style/css';\n\nconst element: SVGJson = {\"tag\":\"svg\",\"attrs\":{\"fill\":\"none\",\"viewBox\":\"0 0 24 24\",\"width\":\"1em\",\"height\":\"1em\"},\"children\":[{\"tag\":\"path\",\"attrs\":{\"fill\":\"currentColor\",\"d\":\"M1 3L23 3V21H1L1 3ZM3 5L3 19H21V5L3 5ZM15.5 9.5C16.0523 9.5 16.5 9.94772 16.5 10.5C16.5 11.0523 16.0523 11.5 15.5 11.5C14.9477 11.5 14.5 11.0523 14.5 10.5C14.5 9.94772 14.9477 9.5 15.5 9.5ZM17.9003 12.2999C18.2769 11.7985 18.5 11.1753 18.5 10.5C18.5 8.84315 17.1569 7.5 15.5 7.5C13.8431 7.5 12.5 8.84315 12.5 10.5C12.5 11.1753 12.7231 11.7985 13.0997 12.2999C12.1283 13.0297 11.5 14.1915 11.5 15.5V16.5H13.5V15.5C13.5 14.3954 14.3954 13.5 15.5 13.5C16.6046 13.5 17.5 14.3954 17.5 15.5V16.5H19.5V15.5C19.5 14.1915 18.8717 13.0297 17.9003 12.2999ZM5 9H9.5V11H5V9ZM5 13H9.5V15H5V13Z\"}}]};\n\nexport default defineComponent({\n  name: 'VerifyIcon',\n  props: {\n    size: {\n      type: String,\n    },\n    onClick: {\n      type: Function as PropType<TdIconSVGProps['onClick']>,\n    },\n  },\n  setup(props, { attrs }) {\n    const propsSize = computed(() => props.size);\n\n    const { className, style } = useSizeProps(propsSize);\n\n    const finalCls = computed(() => ['t-icon', 't-icon-verify', className.value]);\n    const finalStyle = computed(() => ({ ...style.value, ...(attrs.style as Styles) }));\n    const finalProps = computed(() => ({\n      class: finalCls.value,\n      style: finalStyle.value,\n      onClick: (e:MouseEvent) => props.onClick?.({ e }),\n    }));\n    return () => renderFn(element, finalProps.value);\n  },\n\n});\n"], "names": ["element", "defineComponent", "name", "props", "size", "type", "String", "onClick", "Function", "setup", "attrs", "propsSize", "computed", "className", "style", "useSizeProps", "finalCls", "value", "finalStyle", "_objectSpread", "finalProps", "class", "e", "_props$onClick", "call", "renderFn"], "mappings": ";;;;;;;;;;;;;AASA,IAAMA,UAAmB;EAAC,OAAM;EAAM,SAAQ;IAAC,QAAO;IAAO,WAAU;IAAY,SAAQ;IAAM,UAAS;;EAAO,YAAW,CAAC;IAAC,OAAM;IAAO,SAAQ;MAAC,QAAO;MAAe,KAAI;;;;AAE9K,aAAeC,oBAAgB;EAC7BC,MAAM;EACNC,OAAO;IACLC,MAAM;MACJC,MAAMC;;IAERC,SAAS;MACPF,MAAMG;;;EAGVC,MAAMN,aAAkB;IAAA,IAAX;MAAEO;;QACPC,YAAYC,aAAS,MAAMT,MAAMC;QAEjC;MAAES;MAAWC;QAAUC,8BAAaJ;QAEpCK,WAAWJ,aAAS,MAAM,CAAC,UAAU,iBAAiBC,UAAUI;QAChEC,aAAaN,aAAS,MAAAO,aAAA,CAAAA,aAAA,KAAYL,MAAMG,QAAWP,MAAMI;QACzDM,aAAaR,aAAS;MAC1BS,OAAOL,SAASC;MAChBH,OAAOI,WAAWD;MAClBV,SAAUe;;iCAAiBnB,MAAMI,0DAANgB,cAAA,CAAAC,IAAA,CAAArB,OAAgB;UAAEmB;;;;WAExC,MAAMG,0BAASzB,SAASoB,WAAWH;;AAAA;;;;"}