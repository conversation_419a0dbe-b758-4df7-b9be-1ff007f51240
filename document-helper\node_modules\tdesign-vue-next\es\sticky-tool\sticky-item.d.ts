import { PropType } from 'vue';
import type { TdStickyItemProps } from './type';
import type { TdPopupProps } from '../popup/type';
declare const _default: import("vue").DefineComponent<{
    type: {
        type: PropType<import("./type").TdStickyToolProps["type"]>;
        default: import("./type").TdStickyToolProps["type"];
        validator(val: import("./type").TdStickyToolProps["type"]): boolean;
    };
    shape: {
        type: PropType<import("./type").TdStickyToolProps["shape"]>;
        default: import("./type").TdStickyToolProps["shape"];
        validator(val: import("./type").TdStickyToolProps["shape"]): boolean;
    };
    placement: {
        type: PropType<import("./type").TdStickyToolProps["placement"]>;
        default: import("./type").TdStickyToolProps["placement"];
        validator(val: import("./type").TdStickyToolProps["placement"]): boolean;
    };
    basePopupProps: PropType<TdPopupProps>;
    baseWidth: {
        type: PropType<import("./type").TdStickyToolProps["width"]>;
    };
    onClick: PropType<(context: {
        e: MouseEvent;
        item: TdStickyItemProps;
    }) => void>;
    onHover: PropType<(context: {
        e: MouseEvent;
        item: TdStickyItemProps;
    }) => void>;
    icon: {
        type: PropType<TdStickyItemProps["icon"]>;
    };
    label: {
        type: PropType<TdStickyItemProps["label"]>;
    };
    popup: {
        type: PropType<TdStickyItemProps["popup"]>;
    };
    popupProps: {
        type: PropType<TdStickyItemProps["popupProps"]>;
    };
    trigger: {
        type: PropType<TdStickyItemProps["trigger"]>;
        default: TdStickyItemProps["trigger"];
        validator(val: TdStickyItemProps["trigger"]): boolean;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    type: {
        type: PropType<import("./type").TdStickyToolProps["type"]>;
        default: import("./type").TdStickyToolProps["type"];
        validator(val: import("./type").TdStickyToolProps["type"]): boolean;
    };
    shape: {
        type: PropType<import("./type").TdStickyToolProps["shape"]>;
        default: import("./type").TdStickyToolProps["shape"];
        validator(val: import("./type").TdStickyToolProps["shape"]): boolean;
    };
    placement: {
        type: PropType<import("./type").TdStickyToolProps["placement"]>;
        default: import("./type").TdStickyToolProps["placement"];
        validator(val: import("./type").TdStickyToolProps["placement"]): boolean;
    };
    basePopupProps: PropType<TdPopupProps>;
    baseWidth: {
        type: PropType<import("./type").TdStickyToolProps["width"]>;
    };
    onClick: PropType<(context: {
        e: MouseEvent;
        item: TdStickyItemProps;
    }) => void>;
    onHover: PropType<(context: {
        e: MouseEvent;
        item: TdStickyItemProps;
    }) => void>;
    icon: {
        type: PropType<TdStickyItemProps["icon"]>;
    };
    label: {
        type: PropType<TdStickyItemProps["label"]>;
    };
    popup: {
        type: PropType<TdStickyItemProps["popup"]>;
    };
    popupProps: {
        type: PropType<TdStickyItemProps["popupProps"]>;
    };
    trigger: {
        type: PropType<TdStickyItemProps["trigger"]>;
        default: TdStickyItemProps["trigger"];
        validator(val: TdStickyItemProps["trigger"]): boolean;
    };
}>>, {
    type: "normal" | "compact";
    shape: "round" | "square";
    placement: "left-top" | "left-bottom" | "right-top" | "right-bottom" | "right-center" | "left-center";
    trigger: "click" | "hover";
}, {}>;
export default _default;
