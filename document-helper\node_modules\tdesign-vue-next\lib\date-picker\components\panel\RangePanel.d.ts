import { PropType } from 'vue';
import { TdDateRangePickerProps } from '../../type';
declare const _default: import("vue").DefineComponent<{
    hoverValue: PropType<Array<string>>;
    activeIndex: NumberConstructor;
    isFirstValueSelected: BooleanConstructor;
    disableDate: PropType<TdDateRangePickerProps["disableDate"]>;
    mode: {
        type: PropType<TdDateRangePickerProps["mode"]>;
        default: string;
    };
    format: PropType<TdDateRangePickerProps["format"]>;
    presetsPlacement: {
        type: PropType<TdDateRangePickerProps["presetsPlacement"]>;
        default: string;
    };
    value: PropType<Array<string>>;
    timePickerProps: PropType<TdDateRangePickerProps["timePickerProps"]>;
    presets: PropType<TdDateRangePickerProps["presets"]>;
    popupVisible: BooleanConstructor;
    enableTimePicker: BooleanConstructor;
    panelPreselection: BooleanConstructor;
    firstDayOfWeek: NumberConstructor;
    year: PropType<Array<number>>;
    month: PropType<Array<number>>;
    time: PropType<Array<string>>;
    cancelRangeSelectLimit: BooleanConstructor;
    onClick: FunctionConstructor;
    onCellClick: FunctionConstructor;
    onCellMouseEnter: FunctionConstructor;
    onCellMouseLeave: FunctionConstructor;
    onJumperClick: FunctionConstructor;
    onConfirmClick: FunctionConstructor;
    onPresetClick: FunctionConstructor;
    onYearChange: FunctionConstructor;
    onMonthChange: FunctionConstructor;
    onTimePickerChange: FunctionConstructor;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    hoverValue: PropType<Array<string>>;
    activeIndex: NumberConstructor;
    isFirstValueSelected: BooleanConstructor;
    disableDate: PropType<TdDateRangePickerProps["disableDate"]>;
    mode: {
        type: PropType<TdDateRangePickerProps["mode"]>;
        default: string;
    };
    format: PropType<TdDateRangePickerProps["format"]>;
    presetsPlacement: {
        type: PropType<TdDateRangePickerProps["presetsPlacement"]>;
        default: string;
    };
    value: PropType<Array<string>>;
    timePickerProps: PropType<TdDateRangePickerProps["timePickerProps"]>;
    presets: PropType<TdDateRangePickerProps["presets"]>;
    popupVisible: BooleanConstructor;
    enableTimePicker: BooleanConstructor;
    panelPreselection: BooleanConstructor;
    firstDayOfWeek: NumberConstructor;
    year: PropType<Array<number>>;
    month: PropType<Array<number>>;
    time: PropType<Array<string>>;
    cancelRangeSelectLimit: BooleanConstructor;
    onClick: FunctionConstructor;
    onCellClick: FunctionConstructor;
    onCellMouseEnter: FunctionConstructor;
    onCellMouseLeave: FunctionConstructor;
    onJumperClick: FunctionConstructor;
    onConfirmClick: FunctionConstructor;
    onPresetClick: FunctionConstructor;
    onYearChange: FunctionConstructor;
    onMonthChange: FunctionConstructor;
    onTimePickerChange: FunctionConstructor;
}>>, {
    mode: "date" | "month" | "year" | "quarter" | "week";
    enableTimePicker: boolean;
    cancelRangeSelectLimit: boolean;
    popupVisible: boolean;
    presetsPlacement: "left" | "right" | "top" | "bottom";
    panelPreselection: boolean;
    isFirstValueSelected: boolean;
}, {}>;
export default _default;
