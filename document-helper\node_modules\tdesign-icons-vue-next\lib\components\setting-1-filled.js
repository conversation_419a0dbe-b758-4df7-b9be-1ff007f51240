'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var defineProperty = require('../_chunks/dep-304d0f8c.js');
var vue = require('vue');
var utils_renderFn = require('../utils/render-fn.js');
var utils_useSizeProps = require('../utils/use-size-props.js');
require('../utils/use-common-classname.js');
require('../utils/config-context.js');

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { defineProperty._defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var element = {
  "tag": "svg",
  "attrs": {
    "fill": "none",
    "viewBox": "0 0 24 24",
    "width": "1em",
    "height": "1em"
  },
  "children": [{
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M14.82 1H9.18034L8.53289 4.23728C7.99386 4.47836 7.48479 4.77378 7.0126 5.11643L3.88377 4.05799L1.06396 8.94203L3.54498 11.1224C3.51532 11.4112 3.50015 11.704 3.50015 12C3.50015 12.2961 3.51532 12.5888 3.54498 12.8776L1.06396 15.058L3.88377 19.942L7.01262 18.8836C7.4848 19.2262 7.99387 19.5216 8.53289 19.7627L9.18034 23H14.82L15.4674 19.7627C16.0064 19.5216 16.5155 19.2262 16.9877 18.8836L20.1165 19.942L22.9363 15.058L20.4553 12.8776C20.485 12.5888 20.5001 12.2961 20.5001 12C20.5001 11.704 20.485 11.4112 20.4553 11.1224L22.9363 8.94203L20.1165 4.05799L16.9877 5.11643C16.5155 4.77378 16.0064 4.47836 15.4674 4.23728L14.82 1ZM12 16C9.79086 16 8 14.2091 8 12C8 9.79086 9.79086 8 12 8C14.2091 8 16 9.79086 16 12C16 14.2091 14.2091 16 12 16Z"
    }
  }]
};
var setting1Filled = vue.defineComponent({
  name: "Setting1FilledIcon",
  props: {
    size: {
      type: String
    },
    onClick: {
      type: Function
    }
  },
  setup(props, _ref) {
    var {
      attrs
    } = _ref;
    var propsSize = vue.computed(() => props.size);
    var {
      className,
      style
    } = utils_useSizeProps['default'](propsSize);
    var finalCls = vue.computed(() => ["t-icon", "t-icon-setting-1-filled", className.value]);
    var finalStyle = vue.computed(() => _objectSpread(_objectSpread({}, style.value), attrs.style));
    var finalProps = vue.computed(() => ({
      class: finalCls.value,
      style: finalStyle.value,
      onClick: e => {
        var _props$onClick;
        return (_props$onClick = props.onClick) === null || _props$onClick === void 0 ? void 0 : _props$onClick.call(props, {
          e
        });
      }
    }));
    return () => utils_renderFn['default'](element, finalProps.value);
  }
});

exports.default = setting1Filled;
//# sourceMappingURL=setting-1-filled.js.map
