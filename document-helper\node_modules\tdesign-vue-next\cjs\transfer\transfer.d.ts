import { TransferListType, TransferValue, EmptyType } from './types';
declare const _default: import("vue").DefineComponent<{
    checkboxProps: {
        type: import("vue").PropType<import("./type").TdTransferProps["checkboxProps"]>;
    };
    checked: {
        type: import("vue").PropType<import("./type").TdTransferProps["checked"]>;
        default: import("./type").TdTransferProps["checked"];
    };
    defaultChecked: {
        type: import("vue").PropType<import("./type").TdTransferProps["defaultChecked"]>;
        default: () => import("./type").TdTransferProps["defaultChecked"];
    };
    data: {
        type: import("vue").PropType<import("./type").TdTransferProps["data"]>;
        default: () => import("./type").TdTransferProps["data"];
    };
    direction: {
        type: import("vue").PropType<import("./type").TdTransferProps["direction"]>;
        default: import("./type").TdTransferProps["direction"];
        validator(val: import("./type").TdTransferProps["direction"]): boolean;
    };
    disabled: {
        type: import("vue").PropType<import("./type").TdTransferProps["disabled"]>;
        default: any;
    };
    empty: {
        type: import("vue").PropType<import("./type").TdTransferProps["empty"]>;
        default: import("./type").TdTransferProps["empty"];
    };
    footer: {
        type: import("vue").PropType<import("./type").TdTransferProps["footer"]>;
    };
    keys: {
        type: import("vue").PropType<import("./type").TdTransferProps["keys"]>;
    };
    operation: {
        type: import("vue").PropType<import("./type").TdTransferProps["operation"]>;
    };
    pagination: {
        type: import("vue").PropType<import("./type").TdTransferProps["pagination"]>;
    };
    search: {
        type: import("vue").PropType<import("./type").TdTransferProps["search"]>;
        default: boolean;
    };
    showCheckAll: {
        type: import("vue").PropType<import("./type").TdTransferProps["showCheckAll"]>;
        default: import("./type").TdTransferProps["showCheckAll"];
    };
    targetDraggable: BooleanConstructor;
    targetSort: {
        type: import("vue").PropType<import("./type").TdTransferProps["targetSort"]>;
        default: import("./type").TdTransferProps["targetSort"];
        validator(val: import("./type").TdTransferProps["targetSort"]): boolean;
    };
    title: {
        type: import("vue").PropType<import("./type").TdTransferProps["title"]>;
        default: () => import("./type").TdTransferProps["title"];
    };
    transferItem: {
        type: import("vue").PropType<import("./type").TdTransferProps["transferItem"]>;
    };
    value: {
        type: import("vue").PropType<import("./type").TdTransferProps["value"]>;
        default: import("./type").TdTransferProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<import("./type").TdTransferProps["value"]>;
        default: import("./type").TdTransferProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<import("./type").TdTransferProps["defaultValue"]>;
        default: () => import("./type").TdTransferProps["defaultValue"];
    };
    onChange: import("vue").PropType<import("./type").TdTransferProps["onChange"]>;
    onCheckedChange: import("vue").PropType<import("./type").TdTransferProps["onCheckedChange"]>;
    onPageChange: import("vue").PropType<import("./type").TdTransferProps["onPageChange"]>;
    onScroll: import("vue").PropType<import("./type").TdTransferProps["onScroll"]>;
    onSearch: import("vue").PropType<import("./type").TdTransferProps["onSearch"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    checkboxProps: {
        type: import("vue").PropType<import("./type").TdTransferProps["checkboxProps"]>;
    };
    checked: {
        type: import("vue").PropType<import("./type").TdTransferProps["checked"]>;
        default: import("./type").TdTransferProps["checked"];
    };
    defaultChecked: {
        type: import("vue").PropType<import("./type").TdTransferProps["defaultChecked"]>;
        default: () => import("./type").TdTransferProps["defaultChecked"];
    };
    data: {
        type: import("vue").PropType<import("./type").TdTransferProps["data"]>;
        default: () => import("./type").TdTransferProps["data"];
    };
    direction: {
        type: import("vue").PropType<import("./type").TdTransferProps["direction"]>;
        default: import("./type").TdTransferProps["direction"];
        validator(val: import("./type").TdTransferProps["direction"]): boolean;
    };
    disabled: {
        type: import("vue").PropType<import("./type").TdTransferProps["disabled"]>;
        default: any;
    };
    empty: {
        type: import("vue").PropType<import("./type").TdTransferProps["empty"]>;
        default: import("./type").TdTransferProps["empty"];
    };
    footer: {
        type: import("vue").PropType<import("./type").TdTransferProps["footer"]>;
    };
    keys: {
        type: import("vue").PropType<import("./type").TdTransferProps["keys"]>;
    };
    operation: {
        type: import("vue").PropType<import("./type").TdTransferProps["operation"]>;
    };
    pagination: {
        type: import("vue").PropType<import("./type").TdTransferProps["pagination"]>;
    };
    search: {
        type: import("vue").PropType<import("./type").TdTransferProps["search"]>;
        default: boolean;
    };
    showCheckAll: {
        type: import("vue").PropType<import("./type").TdTransferProps["showCheckAll"]>;
        default: import("./type").TdTransferProps["showCheckAll"];
    };
    targetDraggable: BooleanConstructor;
    targetSort: {
        type: import("vue").PropType<import("./type").TdTransferProps["targetSort"]>;
        default: import("./type").TdTransferProps["targetSort"];
        validator(val: import("./type").TdTransferProps["targetSort"]): boolean;
    };
    title: {
        type: import("vue").PropType<import("./type").TdTransferProps["title"]>;
        default: () => import("./type").TdTransferProps["title"];
    };
    transferItem: {
        type: import("vue").PropType<import("./type").TdTransferProps["transferItem"]>;
    };
    value: {
        type: import("vue").PropType<import("./type").TdTransferProps["value"]>;
        default: import("./type").TdTransferProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<import("./type").TdTransferProps["value"]>;
        default: import("./type").TdTransferProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<import("./type").TdTransferProps["defaultValue"]>;
        default: () => import("./type").TdTransferProps["defaultValue"];
    };
    onChange: import("vue").PropType<import("./type").TdTransferProps["onChange"]>;
    onCheckedChange: import("vue").PropType<import("./type").TdTransferProps["onCheckedChange"]>;
    onPageChange: import("vue").PropType<import("./type").TdTransferProps["onPageChange"]>;
    onScroll: import("vue").PropType<import("./type").TdTransferProps["onScroll"]>;
    onSearch: import("vue").PropType<import("./type").TdTransferProps["onSearch"]>;
}>>, {
    search: boolean;
    value: TransferValue[];
    disabled: boolean | boolean[];
    data: import("./type").DataOption[];
    direction: "left" | "right" | "both";
    checked: TransferValue[];
    title: import("./type").TitleType[] | ((h: typeof import("vue").h, props: {
        type: TransferListType;
    }) => import("..").TNodeReturnValue);
    defaultChecked: TransferValue[];
    defaultValue: TransferValue[];
    empty: EmptyType | EmptyType[];
    modelValue: TransferValue[];
    showCheckAll: boolean | boolean[];
    targetSort: "push" | "unshift" | "original";
    targetDraggable: boolean;
}, {}>;
export default _default;
