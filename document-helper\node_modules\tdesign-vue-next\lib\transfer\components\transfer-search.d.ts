import { PropType } from 'vue';
import { SearchOption } from '../types';
declare const _default: import("vue").DefineComponent<{
    value: {
        type: StringConstructor;
        default: string;
    };
    search: {
        type: PropType<SearchOption>;
        default: boolean;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    onChange: FunctionConstructor;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    search: {
        type: PropType<SearchOption>;
        default: boolean;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    onChange: FunctionConstructor;
}>>, {
    search: boolean;
    value: string;
    placeholder: string;
}, {}>;
export default _default;
