declare const _default: import("vue").DefineComponent<{
    allowHalf: BooleanConstructor;
    clearable: BooleanConstructor;
    color: {
        type: import("vue").PropType<import("./type").TdRateProps["color"]>;
        default: import("./type").TdRateProps["color"];
    };
    count: {
        type: NumberConstructor;
        default: number;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    gap: {
        type: NumberConstructor;
        default: number;
    };
    icon: {
        type: import("vue").PropType<import("./type").TdRateProps["icon"]>;
    };
    showText: BooleanConstructor;
    size: {
        type: StringConstructor;
        default: string;
    };
    texts: {
        type: import("vue").PropType<import("./type").TdRateProps["texts"]>;
        default: () => import("./type").TdRateProps["texts"];
    };
    value: {
        type: NumberConstructor;
        default: any;
    };
    modelValue: {
        type: NumberConstructor;
        default: any;
    };
    defaultValue: {
        type: NumberConstructor;
        default: number;
    };
    onChange: import("vue").PropType<import("./type").TdRateProps["onChange"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    allowHalf: BooleanConstructor;
    clearable: BooleanConstructor;
    color: {
        type: import("vue").PropType<import("./type").TdRateProps["color"]>;
        default: import("./type").TdRateProps["color"];
    };
    count: {
        type: NumberConstructor;
        default: number;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    gap: {
        type: NumberConstructor;
        default: number;
    };
    icon: {
        type: import("vue").PropType<import("./type").TdRateProps["icon"]>;
    };
    showText: BooleanConstructor;
    size: {
        type: StringConstructor;
        default: string;
    };
    texts: {
        type: import("vue").PropType<import("./type").TdRateProps["texts"]>;
        default: () => import("./type").TdRateProps["texts"];
    };
    value: {
        type: NumberConstructor;
        default: any;
    };
    modelValue: {
        type: NumberConstructor;
        default: any;
    };
    defaultValue: {
        type: NumberConstructor;
        default: number;
    };
    onChange: import("vue").PropType<import("./type").TdRateProps["onChange"]>;
}>>, {
    value: number;
    disabled: boolean;
    count: number;
    color: string | string[];
    size: string;
    defaultValue: number;
    modelValue: number;
    gap: number;
    clearable: boolean;
    texts: string[];
    allowHalf: boolean;
    showText: boolean;
}, {}>;
export default _default;
