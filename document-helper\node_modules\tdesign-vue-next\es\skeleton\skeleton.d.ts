import { TdSkeletonProps } from './type';
declare const _default: import("vue").DefineComponent<{
    animation: {
        type: import("vue").PropType<TdSkeletonProps["animation"]>;
        default: TdSkeletonProps["animation"];
        validator(val: TdSkeletonProps["animation"]): boolean;
    };
    delay: {
        type: NumberConstructor;
        default: number;
    };
    loading: {
        type: BooleanConstructor;
        default: boolean;
    };
    rowCol: {
        type: import("vue").PropType<TdSkeletonProps["rowCol"]>;
    };
    theme: {
        type: import("vue").PropType<TdSkeletonProps["theme"]>;
        validator(val: TdSkeletonProps["theme"]): boolean;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    animation: {
        type: import("vue").PropType<TdSkeletonProps["animation"]>;
        default: TdSkeletonProps["animation"];
        validator(val: TdSkeletonProps["animation"]): boolean;
    };
    delay: {
        type: NumberConstructor;
        default: number;
    };
    loading: {
        type: BooleanConstructor;
        default: boolean;
    };
    rowCol: {
        type: import("vue").PropType<TdSkeletonProps["rowCol"]>;
    };
    theme: {
        type: import("vue").PropType<TdSkeletonProps["theme"]>;
        validator(val: TdSkeletonProps["theme"]): boolean;
    };
}>>, {
    loading: boolean;
    animation: "none" | "gradient" | "flashed";
    delay: number;
}, {}>;
export default _default;
