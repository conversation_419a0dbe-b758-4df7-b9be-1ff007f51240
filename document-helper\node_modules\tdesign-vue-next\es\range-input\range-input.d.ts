import { RangeInputValue } from './type';
declare const _default: import("vue").DefineComponent<{
    activeIndex: {
        type: NumberConstructor;
    };
    borderless: BooleanConstructor;
    clearable: BooleanConstructor;
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    format: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["format"]>;
    };
    inputProps: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["inputProps"]>;
    };
    label: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["label"]>;
    };
    placeholder: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["placeholder"]>;
    };
    prefixIcon: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["prefixIcon"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    separator: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["separator"]>;
        default: import("./type").TdRangeInputProps["separator"];
    };
    showClearIconOnEmpty: BooleanConstructor;
    size: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["size"]>;
        default: import("./type").TdRangeInputProps["size"];
        validator(val: import("./type").TdRangeInputProps["size"]): boolean;
    };
    status: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["status"]>;
        default: import("./type").TdRangeInputProps["status"];
        validator(val: import("./type").TdRangeInputProps["status"]): boolean;
    };
    suffix: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["suffix"]>;
    };
    suffixIcon: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["suffixIcon"]>;
    };
    tips: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["tips"]>;
    };
    value: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["value"]>;
        default: import("./type").TdRangeInputProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["value"]>;
        default: import("./type").TdRangeInputProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["defaultValue"]>;
        default: () => import("./type").TdRangeInputProps["defaultValue"];
    };
    onBlur: import("vue").PropType<import("./type").TdRangeInputProps["onBlur"]>;
    onChange: import("vue").PropType<import("./type").TdRangeInputProps["onChange"]>;
    onClear: import("vue").PropType<import("./type").TdRangeInputProps["onClear"]>;
    onClick: import("vue").PropType<import("./type").TdRangeInputProps["onClick"]>;
    onEnter: import("vue").PropType<import("./type").TdRangeInputProps["onEnter"]>;
    onFocus: import("vue").PropType<import("./type").TdRangeInputProps["onFocus"]>;
    onMouseenter: import("vue").PropType<import("./type").TdRangeInputProps["onMouseenter"]>;
    onMouseleave: import("vue").PropType<import("./type").TdRangeInputProps["onMouseleave"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    activeIndex: {
        type: NumberConstructor;
    };
    borderless: BooleanConstructor;
    clearable: BooleanConstructor;
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    format: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["format"]>;
    };
    inputProps: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["inputProps"]>;
    };
    label: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["label"]>;
    };
    placeholder: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["placeholder"]>;
    };
    prefixIcon: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["prefixIcon"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    separator: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["separator"]>;
        default: import("./type").TdRangeInputProps["separator"];
    };
    showClearIconOnEmpty: BooleanConstructor;
    size: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["size"]>;
        default: import("./type").TdRangeInputProps["size"];
        validator(val: import("./type").TdRangeInputProps["size"]): boolean;
    };
    status: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["status"]>;
        default: import("./type").TdRangeInputProps["status"];
        validator(val: import("./type").TdRangeInputProps["status"]): boolean;
    };
    suffix: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["suffix"]>;
    };
    suffixIcon: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["suffixIcon"]>;
    };
    tips: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["tips"]>;
    };
    value: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["value"]>;
        default: import("./type").TdRangeInputProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["value"]>;
        default: import("./type").TdRangeInputProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<import("./type").TdRangeInputProps["defaultValue"]>;
        default: () => import("./type").TdRangeInputProps["defaultValue"];
    };
    onBlur: import("vue").PropType<import("./type").TdRangeInputProps["onBlur"]>;
    onChange: import("vue").PropType<import("./type").TdRangeInputProps["onChange"]>;
    onClear: import("vue").PropType<import("./type").TdRangeInputProps["onClear"]>;
    onClick: import("vue").PropType<import("./type").TdRangeInputProps["onClick"]>;
    onEnter: import("vue").PropType<import("./type").TdRangeInputProps["onEnter"]>;
    onFocus: import("vue").PropType<import("./type").TdRangeInputProps["onFocus"]>;
    onMouseenter: import("vue").PropType<import("./type").TdRangeInputProps["onMouseenter"]>;
    onMouseleave: import("vue").PropType<import("./type").TdRangeInputProps["onMouseleave"]>;
}>>, {
    value: RangeInputValue;
    disabled: boolean;
    size: "small" | "medium" | "large";
    status: "error" | "default" | "success" | "warning";
    defaultValue: RangeInputValue;
    modelValue: RangeInputValue;
    readonly: boolean;
    separator: string | ((h: typeof import("vue").h) => import("..").TNodeReturnValue);
    borderless: boolean;
    clearable: boolean;
    showClearIconOnEmpty: boolean;
}, {}>;
export default _default;
