import { TdSubmenuProps } from './type';
declare const _default: import("vue").DefineComponent<{
    content: {
        type: import("vue").PropType<TdSubmenuProps["content"]>;
    };
    default: {
        type: import("vue").PropType<TdSubmenuProps["default"]>;
    };
    disabled: BooleanConstructor;
    icon: {
        type: import("vue").PropType<TdSubmenuProps["icon"]>;
    };
    popupProps: {
        type: import("vue").PropType<TdSubmenuProps["popupProps"]>;
    };
    title: {
        type: import("vue").PropType<TdSubmenuProps["title"]>;
    };
    value: {
        type: import("vue").PropType<TdSubmenuProps["value"]>;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    content: {
        type: import("vue").PropType<TdSubmenuProps["content"]>;
    };
    default: {
        type: import("vue").PropType<TdSubmenuProps["default"]>;
    };
    disabled: BooleanConstructor;
    icon: {
        type: import("vue").PropType<TdSubmenuProps["icon"]>;
    };
    popupProps: {
        type: import("vue").PropType<TdSubmenuProps["popupProps"]>;
    };
    title: {
        type: import("vue").PropType<TdSubmenuProps["title"]>;
    };
    value: {
        type: import("vue").PropType<TdSubmenuProps["value"]>;
    };
}>>, {
    disabled: boolean;
}, {}>;
export default _default;
