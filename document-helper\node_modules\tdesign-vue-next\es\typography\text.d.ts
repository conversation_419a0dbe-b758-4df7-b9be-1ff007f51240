import type { TdTextProps } from './type';
declare const _default: import("vue").DefineComponent<{
    code: BooleanConstructor;
    content: {
        type: import("vue").PropType<TdTextProps["content"]>;
    };
    copyable: {
        type: import("vue").PropType<TdTextProps["copyable"]>;
        default: TdTextProps["copyable"];
    };
    default: {
        type: import("vue").PropType<TdTextProps["default"]>;
    };
    delete: BooleanConstructor;
    disabled: BooleanConstructor;
    ellipsis: {
        type: import("vue").PropType<TdTextProps["ellipsis"]>;
        default: TdTextProps["ellipsis"];
    };
    italic: BooleanConstructor;
    keyboard: BooleanConstructor;
    mark: {
        type: import("vue").PropType<TdTextProps["mark"]>;
        default: TdTextProps["mark"];
    };
    strong: BooleanConstructor;
    theme: {
        type: import("vue").PropType<TdTextProps["theme"]>;
        validator(val: TdTextProps["theme"]): boolean;
    };
    underline: BooleanConstructor;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    code: BooleanConstructor;
    content: {
        type: import("vue").PropType<TdTextProps["content"]>;
    };
    copyable: {
        type: import("vue").PropType<TdTextProps["copyable"]>;
        default: TdTextProps["copyable"];
    };
    default: {
        type: import("vue").PropType<TdTextProps["default"]>;
    };
    delete: BooleanConstructor;
    disabled: BooleanConstructor;
    ellipsis: {
        type: import("vue").PropType<TdTextProps["ellipsis"]>;
        default: TdTextProps["ellipsis"];
    };
    italic: BooleanConstructor;
    keyboard: BooleanConstructor;
    mark: {
        type: import("vue").PropType<TdTextProps["mark"]>;
        default: TdTextProps["mark"];
    };
    strong: BooleanConstructor;
    theme: {
        type: import("vue").PropType<TdTextProps["theme"]>;
        validator(val: TdTextProps["theme"]): boolean;
    };
    underline: BooleanConstructor;
}>>, {
    code: boolean;
    disabled: boolean;
    delete: boolean;
    mark: string | boolean;
    strong: boolean;
    ellipsis: boolean | import("./type").TypographyEllipsis;
    italic: boolean;
    underline: boolean;
    copyable: boolean | import("./type").TypographyCopyable;
    keyboard: boolean;
}, {}>;
export default _default;
