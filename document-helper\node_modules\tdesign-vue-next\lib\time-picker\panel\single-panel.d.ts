import dayjs from 'dayjs';
import { EPickerCols } from 'tdesign-vue-next/lib/common/js/time-picker/const';
declare const _default: import("vue").DefineComponent<{
    position: StringConstructor;
    triggerScroll: BooleanConstructor;
    onChange: FunctionConstructor;
    resetTriggerScroll: FunctionConstructor;
    isShowPanel: BooleanConstructor;
    format: {
        type: StringConstructor;
        default: string;
    };
    cols: {
        type: import("vue").PropType<Array<EPickerCols>>;
        default: () => EPickerCols[];
    };
    value: {
        type: StringConstructor;
        default: string;
    };
    range: {
        type: import("vue").PropType<Array<dayjs.Dayjs>>;
        default: () => Array<dayjs.Dayjs>;
    };
    steps: {
        default: number[];
        type: import("vue").PropType<Array<string | number>>;
    };
    hideDisabledTime: {
        type: BooleanConstructor;
        default: boolean;
    };
    disableTime: {
        type: FunctionConstructor;
    };
    localeMeridiems: {
        type: import("vue").PropType<Array<string>>;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    position: StringConstructor;
    triggerScroll: BooleanConstructor;
    onChange: FunctionConstructor;
    resetTriggerScroll: FunctionConstructor;
    isShowPanel: BooleanConstructor;
    format: {
        type: StringConstructor;
        default: string;
    };
    cols: {
        type: import("vue").PropType<Array<EPickerCols>>;
        default: () => EPickerCols[];
    };
    value: {
        type: StringConstructor;
        default: string;
    };
    range: {
        type: import("vue").PropType<Array<dayjs.Dayjs>>;
        default: () => Array<dayjs.Dayjs>;
    };
    steps: {
        default: number[];
        type: import("vue").PropType<Array<string | number>>;
    };
    hideDisabledTime: {
        type: BooleanConstructor;
        default: boolean;
    };
    disableTime: {
        type: FunctionConstructor;
    };
    localeMeridiems: {
        type: import("vue").PropType<Array<string>>;
    };
}>>, {
    value: string;
    format: string;
    steps: (string | number)[];
    range: dayjs.Dayjs[];
    triggerScroll: boolean;
    isShowPanel: boolean;
    cols: EPickerCols[];
    hideDisabledTime: boolean;
}, {}>;
export default _default;
