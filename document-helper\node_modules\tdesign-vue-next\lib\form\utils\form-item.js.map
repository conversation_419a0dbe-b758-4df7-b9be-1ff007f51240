{"version": 3, "file": "form-item.js", "sources": ["../../../../components/form/utils/form-item.ts"], "sourcesContent": ["export function getFormItemClassName(componentName: string, name?: string) {\n  if (!name) return '';\n  return `${componentName}__${name}`.replace(/(\\[|\\]|\\.)+/g, '_');\n}\n"], "names": ["getFormItemClassName", "componentName", "name", "concat", "replace"], "mappings": ";;;;;;AAAgB,SAAAA,oBAAAA,CAAqBC,eAAuBC,IAAe,EAAA;AACzE,EAAA,IAAI,CAACA,IAAA,EAAa,OAAA,EAAA,CAAA;AAClB,EAAA,OAAO,EAAAC,CAAAA,MAAA,CAAGF,aAAA,QAAAE,MAAA,CAAkBD,IAAO,CAAA,CAAAE,OAAA,CAAQ,gBAAgB,GAAG,CAAA,CAAA;AAChE;;;;"}