import { TdTimelineItemProps } from '../timeline/type';
import { PropType } from 'vue';
declare const _default: {
    content: {
        type: PropType<TdTimelineItemProps["content"]>;
    };
    dot: {
        type: PropType<TdTimelineItemProps["dot"]>;
    };
    dotColor: {
        type: PropType<TdTimelineItemProps["dotColor"]>;
        default: string;
    };
    label: {
        type: PropType<TdTimelineItemProps["label"]>;
    };
    labelAlign: {
        type: PropType<TdTimelineItemProps["labelAlign"]>;
        validator(val: TdTimelineItemProps["labelAlign"]): boolean;
    };
    loading: BooleanConstructor;
    onClick: PropType<TdTimelineItemProps["onClick"]>;
};
export default _default;
