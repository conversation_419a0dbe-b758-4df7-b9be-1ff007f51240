declare const _default: import("vue").DefineComponent<{
    current: {
        type: import("vue").PropType<import("./type").TdStepsProps["current"]>;
        default: import("./type").TdStepsProps["current"];
    };
    modelValue: {
        type: import("vue").PropType<import("./type").TdStepsProps["current"]>;
        default: import("./type").TdStepsProps["current"];
    };
    defaultCurrent: {
        type: import("vue").PropType<import("./type").TdStepsProps["defaultCurrent"]>;
    };
    layout: {
        type: import("vue").PropType<import("./type").TdStepsProps["layout"]>;
        default: import("./type").TdStepsProps["layout"];
        validator(val: import("./type").TdStepsProps["layout"]): boolean;
    };
    options: {
        type: import("vue").PropType<import("./type").TdStepsProps["options"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    separator: {
        type: import("vue").PropType<import("./type").TdStepsProps["separator"]>;
        default: import("./type").TdStepsProps["separator"];
        validator(val: import("./type").TdStepsProps["separator"]): boolean;
    };
    sequence: {
        type: import("vue").PropType<import("./type").TdStepsProps["sequence"]>;
        default: import("./type").TdStepsProps["sequence"];
        validator(val: import("./type").TdStepsProps["sequence"]): boolean;
    };
    theme: {
        type: import("vue").PropType<import("./type").TdStepsProps["theme"]>;
        default: import("./type").TdStepsProps["theme"];
        validator(val: import("./type").TdStepsProps["theme"]): boolean;
    };
    onChange: import("vue").PropType<import("./type").TdStepsProps["onChange"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    current: {
        type: import("vue").PropType<import("./type").TdStepsProps["current"]>;
        default: import("./type").TdStepsProps["current"];
    };
    modelValue: {
        type: import("vue").PropType<import("./type").TdStepsProps["current"]>;
        default: import("./type").TdStepsProps["current"];
    };
    defaultCurrent: {
        type: import("vue").PropType<import("./type").TdStepsProps["defaultCurrent"]>;
    };
    layout: {
        type: import("vue").PropType<import("./type").TdStepsProps["layout"]>;
        default: import("./type").TdStepsProps["layout"];
        validator(val: import("./type").TdStepsProps["layout"]): boolean;
    };
    options: {
        type: import("vue").PropType<import("./type").TdStepsProps["options"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    separator: {
        type: import("vue").PropType<import("./type").TdStepsProps["separator"]>;
        default: import("./type").TdStepsProps["separator"];
        validator(val: import("./type").TdStepsProps["separator"]): boolean;
    };
    sequence: {
        type: import("vue").PropType<import("./type").TdStepsProps["sequence"]>;
        default: import("./type").TdStepsProps["sequence"];
        validator(val: import("./type").TdStepsProps["sequence"]): boolean;
    };
    theme: {
        type: import("vue").PropType<import("./type").TdStepsProps["theme"]>;
        default: import("./type").TdStepsProps["theme"];
        validator(val: import("./type").TdStepsProps["theme"]): boolean;
    };
    onChange: import("vue").PropType<import("./type").TdStepsProps["onChange"]>;
}>>, {
    theme: "default" | "dot";
    modelValue: string | number;
    readonly: boolean;
    current: string | number;
    layout: "vertical" | "horizontal";
    separator: "line" | "dashed" | "arrow";
    sequence: "reverse" | "positive";
}, {}>;
export default _default;
