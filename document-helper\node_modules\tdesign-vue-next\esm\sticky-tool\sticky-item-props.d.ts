import { TdStickyItemProps } from '../sticky-tool/type';
import { PropType } from 'vue';
declare const _default: {
    icon: {
        type: PropType<TdStickyItemProps["icon"]>;
    };
    label: {
        type: PropType<TdStickyItemProps["label"]>;
    };
    popup: {
        type: PropType<TdStickyItemProps["popup"]>;
    };
    popupProps: {
        type: PropType<TdStickyItemProps["popupProps"]>;
    };
    trigger: {
        type: PropType<TdStickyItemProps["trigger"]>;
        default: TdStickyItemProps["trigger"];
        validator(val: TdStickyItemProps["trigger"]): boolean;
    };
};
export default _default;
