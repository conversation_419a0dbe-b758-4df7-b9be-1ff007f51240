{"version": 3, "file": "dropdown-menu.mjs", "sources": ["../../../components/dropdown/dropdown-menu.tsx"], "sourcesContent": ["import { defineComponent, ref, onMounted, h, reactive } from 'vue';\nimport { ChevronRightIcon as TdChevronRightIcon } from 'tdesign-icons-vue-next';\nimport DropdownItem from './dropdown-item';\n\nimport { DropdownOption } from './type';\nimport props from './props';\nimport TDivider from '../divider';\nimport { useGlobalIcon, usePrefixClass } from '@tdesign/shared-hooks';\n\nimport { TNode } from '../common';\nimport { isFunction } from 'lodash-es';\n\nexport default defineComponent({\n  name: 'TDropdownMenu',\n  props,\n  setup(props) {\n    const dropdownClass = usePrefixClass('dropdown');\n    const dropdownMenuClass = usePrefixClass('dropdown__menu');\n    const scrollTopMap = reactive<Record<string, number>>({});\n    const itemHeight = ref(null);\n    const menuRef = ref<HTMLElement>();\n    const isOverMaxHeight = ref(false);\n    const { ChevronRightIcon } = useGlobalIcon({\n      ChevronRightIcon: TdChevronRightIcon,\n    });\n\n    const handleItemClick = (options: { data: DropdownOption; context: { e: MouseEvent } }) => {\n      const { data, context } = options;\n      data?.onClick?.(data, context);\n      props.onClick?.(data, context);\n    };\n\n    const handleScroll = (e: MouseEvent, deep: number) => {\n      const { scrollTop } = e.target as HTMLElement;\n      scrollTopMap[deep] = scrollTop;\n    };\n\n    onMounted(() => {\n      if (menuRef.value) {\n        const menuHeight = parseInt(window?.getComputedStyle(menuRef.value).height, 10);\n        if (menuHeight >= props.maxHeight) isOverMaxHeight.value = true;\n      }\n      itemHeight.value = document.querySelector(`.${dropdownClass.value}__item`).scrollHeight + 2;\n    });\n\n    const getContent = (content: string | TNode) => {\n      if (isFunction(content)) {\n        return content(h);\n      }\n      return content;\n    };\n\n    // 处理options渲染的场景\n    const renderOptions = (data: Array<DropdownOption>, deep: number) => {\n      const arr: Array<unknown> = [];\n      let renderContent;\n      data.forEach?.((menu, idx) => {\n        const optionItem = { ...(menu as DropdownOption) };\n        const onViewIdx = idx - Math.ceil(scrollTopMap[deep] / itemHeight.value);\n        const renderIdx = onViewIdx >= 0 ? onViewIdx : idx;\n\n        if (optionItem.children) {\n          optionItem.children = renderOptions(optionItem.children, deep + 1);\n          renderContent = (\n            <div key={idx}>\n              <DropdownItem\n                style={optionItem.style}\n                class={[`${dropdownClass.value}__item`, `${dropdownClass.value}__item--suffix`, optionItem.class]}\n                value={optionItem.value}\n                theme={optionItem.theme}\n                active={optionItem.active}\n                prefixIcon={optionItem.prefixIcon}\n                disabled={optionItem.disabled}\n                minColumnWidth={props.minColumnWidth}\n                maxColumnWidth={props.maxColumnWidth}\n                isSubmenu={true}\n              >\n                <div class={`${dropdownClass.value}__item-content`}>\n                  <span class={`${dropdownClass.value}__item-text`}>{getContent(optionItem.content)}</span>\n                  <ChevronRightIcon class={`${dropdownClass.value}__item-direction`} size=\"16\" />\n                </div>\n                <div\n                  class={[\n                    `${dropdownClass.value}__submenu-wrapper`,\n                    {\n                      [`${dropdownClass.value}__submenu-wrapper--${props.direction}`]: props.direction,\n                    },\n                  ]}\n                  style={{\n                    position: 'absolute',\n                    top: `${renderIdx * itemHeight.value}px`,\n                  }}\n                >\n                  <div\n                    class={[\n                      `${dropdownClass.value}__submenu`,\n                      {\n                        [`${dropdownClass.value}__submenu--disabled`]: optionItem.disabled,\n                      },\n                    ]}\n                    style={{\n                      position: 'static',\n                      maxHeight: `${props.maxHeight}px`,\n                    }}\n                    onScroll={(e: MouseEvent) => handleScroll(e, deep + 1)}\n                  >\n                    <ul>{optionItem.children}</ul>\n                  </div>\n                </div>\n              </DropdownItem>\n              {optionItem.divider ? <TDivider /> : null}\n            </div>\n          );\n        } else {\n          renderContent = (\n            <div key={idx}>\n              <DropdownItem\n                style={optionItem.style}\n                class={[`${dropdownClass.value}__item`, optionItem.class]}\n                value={optionItem.value}\n                theme={optionItem.theme}\n                active={optionItem.active}\n                prefixIcon={optionItem.prefixIcon}\n                disabled={optionItem.disabled}\n                minColumnWidth={props.minColumnWidth}\n                maxColumnWidth={props.maxColumnWidth}\n                onClick={\n                  optionItem.disabled || optionItem.children\n                    ? () => null\n                    : (value: string | number | { [key: string]: any }, context: { e: MouseEvent }) =>\n                        handleItemClick({ data: optionItem, context })\n                }\n              >\n                <span class={`${dropdownClass.value}__item-text`}>{getContent(optionItem.content)}</span>\n              </DropdownItem>\n              {optionItem.divider ? <TDivider /> : null}\n            </div>\n          );\n        }\n        arr.push(renderContent);\n      });\n      return arr;\n    };\n\n    return () => {\n      return (\n        <div\n          class={[\n            dropdownMenuClass.value,\n            `${dropdownMenuClass.value}--${props.direction}`,\n            {\n              [`${dropdownMenuClass.value}--overflow`]: isOverMaxHeight.value,\n            },\n          ]}\n          style={{\n            maxHeight: `${props.maxHeight}px`,\n          }}\n          ref={menuRef}\n          onScroll={(e: MouseEvent) => handleScroll(e, 0)}\n        >\n          {renderOptions(props.options, 0)}\n        </div>\n      );\n    };\n  },\n});\n"], "names": ["defineComponent", "name", "props", "setup", "dropdownClass", "usePrefixClass", "dropdownMenuClass", "scrollTopMap", "reactive", "itemHeight", "ref", "menuRef", "isOverMaxHeight", "_useGlobalIcon", "useGlobalIcon", "ChevronRightIcon", "TdChevronRightIcon", "handleItemClick", "options", "_data$onClick", "_props2$onClick", "data", "context", "onClick", "call", "handleScroll", "e", "deep", "scrollTop", "target", "onMounted", "value", "_window", "menuHeight", "parseInt", "window", "getComputedStyle", "height", "maxHeight", "document", "querySelector", "concat", "scrollHeight", "get<PERSON>ontent", "content", "isFunction", "h", "renderOptions", "_data$forEach", "arr", "renderContent", "for<PERSON>ach", "menu", "idx", "optionItem", "_objectSpread", "onViewIdx", "Math", "ceil", "renderIdx", "children", "_createVNode", "DropdownItem", "style", "theme", "active", "prefixIcon", "disabled", "minColumn<PERSON>idth", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_default", "_defineProperty", "direction", "position", "top", "onScroll", "divider", "TDivider", "push"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,oBAAeA,eAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,eAAA;AACNC,EAAAA,KAAA,EAAAA,KAAA;AACAC,EAAAA,OAAAA,SAAAA,MAAMD,MAAO,EAAA;AACL,IAAA,IAAAE,aAAA,GAAgBC,eAAe,UAAU,CAAA,CAAA;AACzC,IAAA,IAAAC,iBAAA,GAAoBD,eAAe,gBAAgB,CAAA,CAAA;AACnD,IAAA,IAAAE,YAAA,GAAeC,QAAiC,CAAA,EAAE,CAAA,CAAA;AAClD,IAAA,IAAAC,UAAA,GAAaC,IAAI,IAAI,CAAA,CAAA;AAC3B,IAAA,IAAMC,UAAUD,GAAiB,EAAA,CAAA;AAC3B,IAAA,IAAAE,eAAA,GAAkBF,IAAI,KAAK,CAAA,CAAA;IAC3B,IAAAG,cAAA,GAAuBC,aAAc,CAAA;AACzCC,QAAAA,gBAAkB,EAAAC,gBAAAA;AACpB,OAAC,CAAA;MAFOD,kBAAiB,GAAAF,cAAA,CAAjBE,gBAAiB,CAAA;AAInB,IAAA,IAAAE,eAAA,GAAkB,SAAlBA,eAAAA,CAAmBC,OAAkE,EAAA;MAAA,IAAAC,aAAA,EAAAC,eAAA,CAAA;AACnF,MAAA,IAAEC,IAAM,GAAYH,OAAA,CAAlBG,IAAM;QAAAC,OAAA,GAAYJ,OAAA,CAAZI,OAAA,CAAA;MACRD,IAAA,KAAA,IAAA,IAAAA,IAAA,KAAAF,KAAAA,CAAAA,IAAAA,CAAAA,aAAA,GAAAE,IAAA,CAAAE,OAAA,MAAAJ,IAAAA,IAAAA,aAAA,eAAAA,aAAA,CAAAK,IAAA,CAAAH,IAAA,EAAUA,MAAMC,OAAO,CAAA,CAAA;AAC7BpB,MAAAA,CAAAA,eAAAA,GAAAA,MAAAA,CAAMqB,OAAU,cAAAH,eAAA,KAAA,KAAA,CAAA,IAAhBlB,eAAAA,CAAAA,IAAAA,CAAAA,MAAAA,EAAgBmB,IAAA,EAAMC,OAAO,CAAA,CAAA;KAC/B,CAAA;IAEM,IAAAG,YAAA,GAAe,SAAfA,YAAAA,CAAgBC,CAAA,EAAeC,IAAiB,EAAA;AAC9C,MAAA,IAAEC,SAAU,GAAIF,CAAE,CAAAG,MAAA,CAAhBD,SAAU,CAAA;AAClBrB,MAAAA,YAAA,CAAaoB,IAAQ,CAAA,GAAAC,SAAA,CAAA;KACvB,CAAA;AAEAE,IAAAA,SAAA,CAAU,YAAM;MACd,IAAInB,QAAQoB,KAAO,EAAA;AAAA,QAAA,IAAAC,OAAA,CAAA;QACX,IAAAC,UAAA,GAAaC,oBAASC,MAAQ,MAAAH,IAAAA,IAAAA,OAAA,KAARA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAA,CAAQI,gBAAA,CAAiBzB,QAAQoB,KAAK,CAAA,CAAEM,QAAQ,EAAE,CAAA,CAAA;QAC9E,IAAIJ,cAAc/B,MAAM,CAAAoC,SAAA,EAAW1B,eAAA,CAAgBmB,KAAQ,GAAA,IAAA,CAAA;AAC7D,OAAA;AACAtB,MAAAA,UAAA,CAAWsB,QAAQQ,QAAS,CAAAC,aAAA,CAAAC,GAAAA,CAAAA,MAAA,CAAkBrC,aAAc,CAAA2B,KAAA,EAAA,QAAA,CAAa,EAAEW,YAAe,GAAA,CAAA,CAAA;AAC5F,KAAC,CAAA,CAAA;AAEK,IAAA,IAAAC,UAAA,GAAa,SAAbA,UAAAA,CAAcC,OAA4B,EAAA;AAC1C,MAAA,IAAAC,UAAA,CAAWD,OAAO,CAAG,EAAA;QACvB,OAAOA,QAAQE,CAAC,CAAA,CAAA;AAClB,OAAA;AACO,MAAA,OAAAF,OAAA,CAAA;KACT,CAAA;IAGM,IAAAG,cAAA,GAAgB,SAAhBA,aAAAA,CAAiB1B,IAAA,EAA6BM,IAAiB,EAAA;AAAA,MAAA,IAAAqB,aAAA,CAAA;MACnE,IAAMC,MAAsB,EAAC,CAAA;AACzB,MAAA,IAAAC,aAAA,CAAA;AACC,MAAA,CAAAF,aAAA,GAAA3B,IAAA,CAAA8B,OAAA,MAAA,IAAA,IAAAH,aAAA,KAAAA,KAAAA,CAAAA,IAAAA,aAAA,CAAAxB,IAAA,CAAAH,IAAA,EAAU,UAAC+B,IAAA,EAAMC,GAAQ,EAAA;AACtB,QAAA,IAAAC,UAAA,GAAAC,aAAA,CAAA,EAAA,EAAmBH,IAAwB,CAAA,CAAA;AACjD,QAAA,IAAMI,YAAYH,GAAM,GAAAI,IAAA,CAAKC,KAAKnD,YAAa,CAAAoB,IAAA,CAAA,GAAQlB,WAAWsB,KAAK,CAAA,CAAA;QACjE,IAAA4B,SAAA,GAAYH,SAAa,IAAA,CAAA,GAAIA,SAAY,GAAAH,GAAA,CAAA;QAE/C,IAAIC,WAAWM,QAAU,EAAA;AACvBN,UAAAA,UAAA,CAAWM,QAAW,GAAAb,cAAA,CAAcO,UAAW,CAAAM,QAAA,EAAUjC,OAAO,CAAC,CAAA,CAAA;AAE/DuB,UAAAA,aAAA,GAAAW,WAAA,CAAA,KAAA,EAAA;YAAA,KAAUR,EAAAA,GAAAA;WAAAQ,EAAAA,CAAAA,WAAA,CAAAC,aAAA,EAAA;YAAA,OAECR,EAAAA,UAAW,CAAAS,KAAA;AAAA,YAAA,OAAA,EACX,CAAAtB,EAAAA,CAAAA,MAAA,CAAIrC,aAAc,CAAA2B,KAAA,EAAA,QAAA,CAAA,EAAA,EAAA,CAAAU,MAAA,CAAkBrC,aAAc,CAAA2B,KAAA,EAAuBuB,gBAAAA,CAAAA,EAAAA,UAAW,SAAK;mBACzFA,EAAAA,UAAA,CAAWvB,KAClB;YAAA,OAAOuB,EAAAA,WAAWU,KAClB;YAAA,QAAQV,EAAAA,UAAW,CAAAW,MAAA;YAAA,YACPX,EAAAA,UAAW,CAAAY,UAAA;YAAA,UACbZ,EAAAA,UAAA,CAAWa;4BACLjE,EAAAA,MAAAA,CAAMkE;4BACNlE,EAAAA,MAAAA,CAAMmE;uBACX,EAAA,IAAA;AAAA,WAAA,EAAA;AAAA,YAAA,SAAA,EAAA,SAAAC,QAAA,GAAA;AAAA,cAAA,OAAA,CAAAT,WAAA,CAAA,KAAA,EAAA;AAAA,gBAAA,OAAA,EAAA,EAAA,CAAApB,MAAA,CAEIrC,aAAc,CAAA2B,KAAA,EAAA,gBAAA,CAAA;AAAA,eAAA,EAAA,CAAA8B,WAAA,CAAA,MAAA,EAAA;AAAA,gBAAA,OAAA,EAAA,EAAA,CAAApB,MAAA,CACXrC,aAAA,CAAc2B;eAAqBY,EAAAA,CAAAA,UAAW,CAAAW,UAAA,CAAWV,OAAO,CAAA,CAAA,CAAA,EAAAiB,WAAA,CAAA9C,kBAAA,EAAA;AAAA,gBAAA,OAAA,EAAA,EAAA,CAAA0B,MAAA,CACpDrC,aAAc,CAAA2B,KAAA,EAAA,kBAAA,CAAA;AAAA,gBAAA,MAAA,EAAA,IAAA;AAAA,eAAA,EAAA,IAAA,CAAA,CAAA,CAAA,EAAA8B,WAAA,CAAA,KAAA,EAAA;gBAAA,OAGnC,EAAA,CAAA,EAAA,CAAApB,MAAA,CACFrC,aAAc,CAAA2B,KAAA,EAAAwC,mBAAAA,CAAAA,EAAAA,eAAA,CAAA9B,EAAAA,EAAAA,EAAAA,CAAAA,MAAA,CAEXrC,aAAA,CAAc2B,KAA2B7B,EAAAA,qBAAAA,CAAAA,CAAAA,MAAAA,CAAAA,MAAAA,CAAMsE,UAActE,EAAAA,MAAM,CAAAsE,SAAA;uBAGpE,EAAA;AACLC,kBAAAA,QAAU,EAAA,UAAA;AACVC,kBAAAA,GAAA,KAAAjC,MAAA,CAAQkB,SAAA,GAAYlD,UAAW,CAAAsB,KAAA,EAAA,IAAA,CAAA;AACjC,iBAAA;AAAA,eAAA,EAAA,CAAA8B,WAAA,CAAA,KAAA,EAAA;AAAA,gBAAA,OAAA,EAGS,IAAApB,MAAA,CACFrC,aAAc,CAAA2B,KAAA,gBAAAwC,eAAA,CAAA,EAAA,EAAA,EAAA,CAAA9B,MAAA,CAEXrC,aAAc,CAAA2B,KAAA,0BAA6BuB,UAAW,CAAAa,QAAA;uBAGvD,EAAA;AACLM,kBAAAA,QAAU,EAAA,QAAA;AACVnC,kBAAAA,SAAA,EAAAG,EAAAA,CAAAA,MAAA,CAAcvC,MAAM,CAAAoC,SAAA,EAAA,IAAA,CAAA;;0BAEZ,EAAA,SAAAqC,SAACjD,CAAkB,EAAA;AAAA,kBAAA,OAAAD,YAAA,CAAaC,GAAGC,IAAO,GAAA,CAAC,CAErD,CAAA;AAAA,iBAAA;AAAA,eAAA,EAAA,CAAAkC,WAAA,CAAA,IAAA,EAAA,IAAA,EAAA,CAAKP,UAAW,CAAAM,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,aAAA;WAIrBN,CAAAA,EAAAA,UAAW,CAAAsB,OAAA,GAAAf,WAAA,CAAAgB,OAAA,EAAyB,IAAA,EAAA,IAAA,CAAA,GAAA,IAAA,CA9CtC,CAAA,CAAA;AAiDL,SAAO,MAAA;AAEH3B,UAAAA,aAAA,GAAAW,WAAA,CAAA,KAAA,EAAA;YAAA,KAAUR,EAAAA,GAAAA;WAAAQ,EAAAA,CAAAA,WAAA,CAAAC,aAAA,EAAA;YAAA,OAECR,EAAAA,UAAA,CAAWS,KAClB;YAAA,OAAO,EAAA,CAAA,EAAA,CAAAtB,MAAA,CAAIrC,aAAc,CAAA2B,KAAA,EAAA,QAAA,CAAA,EAAeuB,UAAW,CAAA,OAAA,CAAK,CACxD;YAAA,OAAOA,EAAAA,UAAW,CAAAvB,KAAA;YAAA,OACXuB,EAAAA,UAAA,CAAWU,KAClB;YAAA,QAAQV,EAAAA,UAAW,CAAAW,MAAA;YAAA,YACPX,EAAAA,UAAA,CAAWY,UACvB;YAAA,UAAUZ,EAAAA,UAAW,CAAAa,QAAA;YAAA,gBACLjE,EAAAA,MAAM,CAAAkE,cAAA;YAAA,gBACNlE,EAAAA,MAAAA,CAAMmE,cACtB;AAAA,YAAA,SAAA,EACEf,UAAW,CAAAa,QAAA,IAAYb,UAAW,CAAAM,QAAA,GAC9B,YAAA;AAAA,cAAA,OAAM,IAAA,CAAA;aACN,GAAA,UAAC7B,KAAA,EAAiDT,OAChD,EAAA;AAAA,cAAA,OAAAL,eAAA,CAAgB;AAAEI,gBAAAA,IAAM,EAAAiC,UAAA;AAAYhC,gBAAAA,OAAQ,EAARA,OAAAA;AAAQ,eAAC,CAGrD,CAAA;AAAA,aAAA;AAAA,WAAA,EAAA;AAAA,YAAA,SAAA,EAAA,SAAAgD,QAAA,GAAA;AAAA,cAAA,OAAA,CAAAT,WAAA,CAAA,MAAA,EAAA;AAAA,gBAAA,OAAA,EAAA,EAAA,CAAApB,MAAA,CAAgBrC,aAAA,CAAc2B,KAAqB,EAAA,aAAA,CAAA;AAAA,eAAA,EAAA,CAAAY,UAAA,CAAWW,UAAW,CAAAV,OAAO,CAAE,CAAA,CAAA,CAAA,CAAA;AAAA,aAAA;WAEnFU,CAAAA,EAAAA,UAAW,CAAAsB,OAAA,GAAAf,WAAA,CAAAgB,OAAA,EAAyB,IAAA,EAAA,IAAA,CAAA,GAAA,IAAA,CApBtC,CAAA,CAAA;AAuBL,SAAA;AACA5B,QAAAA,GAAA,CAAI6B,KAAK5B,aAAa,CAAA,CAAA;AACxB,OAAC,CAAA,CAAA;AACM,MAAA,OAAAD,GAAA,CAAA;KACT,CAAA;AAEA,IAAA,OAAO,YAAM;AAET,MAAA,OAAAY,WAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EACS,CACLvD,iBAAkB,CAAAyB,KAAA,EAAAU,EAAAA,CAAAA,MAAA,CACfnC,iBAAkB,CAAAyB,KAAA,EAAAU,IAAAA,CAAAA,CAAAA,MAAA,CAAUvC,MAAM,CAAAsE,SAAA,CAAAD,EAAAA,eAAA,CAAA9B,EAAAA,EAAAA,EAAAA,CAAAA,MAAA,CAE/BnC,iBAAkB,CAAAyB,KAAA,EAAoBnB,YAAAA,CAAAA,EAAAA,eAAgB,CAAAmB,KAAA;eAGvD,EAAA;AACLO,UAAAA,SAAA,EAAAG,EAAAA,CAAAA,MAAA,CAAcvC,MAAM,CAAAoC,SAAA,EAAA,IAAA,CAAA;SAEtB;AAAA,QAAA,KAAA,EAAK3B,OACL;QAAA,UAAU,EAAA,SAAAgE,SAACjD,CAAkB,EAAA;AAAA,UAAA,OAAAD,YAAA,CAAaC,CAAG,EAAA,CAAC;;UAE7CqB,cAAc7C,CAAAA,MAAAA,CAAMgB,OAAS,EAAA,CAAC;KAGrC,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}