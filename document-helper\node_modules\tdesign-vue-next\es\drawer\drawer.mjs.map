{"version": 3, "file": "drawer.mjs", "sources": ["../../../components/drawer/drawer.tsx"], "sourcesContent": ["import { onBeforeUnmount, onMounted, computed, defineComponent, nextTick, onUpdated, ref, watch, Teleport } from 'vue';\nimport { CloseIcon as TdCloseIcon } from 'tdesign-icons-vue-next';\nimport {\n  useConfig,\n  useContent,\n  useTNodeJSX,\n  useTeleport,\n  useGlobalIcon,\n  usePrefixClass,\n  usePopupManager,\n} from '@tdesign/shared-hooks';\n\nimport { isServer } from '@tdesign/shared-utils';\nimport { getScrollbarWidth } from '@tdesign/common-js/utils/getScrollbarWidth';\nimport props from './props';\nimport { DrawerCloseContext } from './type';\nimport { useAction } from '../dialog/hooks';\n\nimport { useDrag } from './hooks';\nimport type { TdDrawerProps } from './type';\n\nlet key = 1;\n\nexport default defineComponent({\n  name: 'TDrawer',\n  inheritAttrs: false,\n  props,\n  emits: ['update:visible'],\n  setup(props, context) {\n    const destroyOnCloseVisible = ref(false);\n    const isVisible = ref(false);\n    const styleEl = ref();\n    const styleTimer = ref();\n    const { globalConfig } = useConfig('drawer');\n    const { CloseIcon } = useGlobalIcon({ CloseIcon: TdCloseIcon });\n    const renderTNodeJSX = useTNodeJSX();\n    const renderContent = useContent();\n    const COMPONENT_NAME = usePrefixClass('drawer');\n    const { draggedSizeValue, enableDrag, draggableLineStyles, draggingStyles } = useDrag(props as TdDrawerProps);\n    const computedVisible = computed(() => props.visible);\n    const isMounted = ref(false);\n\n    // teleport容器\n    const teleportElement = useTeleport(() => props.attach);\n\n    const confirmBtnAction = (e: MouseEvent) => {\n      props.onConfirm?.({ e });\n    };\n    const cancelBtnAction = (e: MouseEvent) => {\n      props.onCancel?.({ e });\n      closeDrawer({ trigger: 'cancel', e });\n    };\n    const { getConfirmBtn, getCancelBtn } = useAction({ confirmBtnAction, cancelBtnAction });\n    const drawerEle = ref<HTMLElement | null>(null);\n    const drawerClasses = computed(() => [\n      COMPONENT_NAME.value,\n      `${COMPONENT_NAME.value}--${props.placement}`,\n      {\n        [`${COMPONENT_NAME.value}--open`]: isVisible.value,\n        [`${COMPONENT_NAME.value}--attach`]: props.showInAttachedElement,\n        [`${COMPONENT_NAME.value}--without-mask`]: !props.showOverlay,\n      },\n      props.drawerClassName,\n    ]);\n\n    const sizeValue = computed(() => {\n      if (draggedSizeValue.value) return draggedSizeValue.value;\n      const size = props.size ?? globalConfig.value.size;\n      const defaultSize = isNaN(Number(size)) ? size : `${size}px`;\n      return (\n        {\n          small: '300px',\n          medium: '500px',\n          large: '760px',\n        }[size] || defaultSize\n      );\n    });\n    const wrapperStyles = computed(() => ({\n      transform: isVisible.value ? 'translateX(0)' : undefined,\n      width: ['left', 'right'].includes(props.placement) ? sizeValue.value : '',\n      height: ['top', 'bottom'].includes(props.placement) ? sizeValue.value : '',\n    }));\n\n    const wrapperClasses = computed(() => [\n      `${COMPONENT_NAME.value}__content-wrapper`,\n      `${COMPONENT_NAME.value}__content-wrapper--${props.placement}`,\n    ]);\n\n    const parentNode = computed<HTMLElement>(() => drawerEle.value?.parentNode as HTMLElement);\n\n    const modeAndPlacement = computed<string>(() => [props.mode, props.placement].join());\n    const footerStyle = computed(() => ({\n      display: 'flex',\n      justifyContent: props.placement === 'right' ? 'flex-start' : 'flex-end',\n    }));\n\n    const handleEscKeydown = (e: KeyboardEvent) => {\n      if (\n        (props.closeOnEscKeydown ?? globalConfig.value.closeOnEscKeydown) &&\n        e.key === 'Escape' &&\n        isVisible.value &&\n        isTopInteractivePopup()\n      ) {\n        props.onEscKeydown?.({ e });\n        closeDrawer({ trigger: 'esc', e });\n        // 阻止事件冒泡\n        e.stopImmediatePropagation();\n      }\n    };\n\n    const clearStyleEl = () => {\n      clearTimeout(styleTimer.value);\n      styleTimer.value = setTimeout(() => {\n        styleEl.value?.parentNode?.removeChild?.(styleEl.value);\n        styleEl.value = null;\n      }, 150);\n      nextTick(() => {\n        drawerEle.value?.focus?.();\n      });\n    };\n\n    const createStyleEl = () => {\n      if (!styleEl.value) return;\n      const hasScrollBar = window.innerWidth > document.documentElement.clientWidth;\n      const scrollWidth = hasScrollBar ? getScrollbarWidth() : 0;\n      styleEl.value = document.createElement('style');\n      styleEl.value.dataset.id = `td_drawer_${+new Date()}_${(key += 1)}`;\n      styleEl.value.innerHTML = `\n        html body {\n          overflow-y: hidden;\n          transition: margin 300ms cubic-bezier(0.7, 0.3, 0.1, 1) 0s;\n          ${props.mode === 'push' ? '' : `width: calc(100% - ${scrollWidth}px);`}\n        }\n      `;\n    };\n\n    const handlePushMode = () => {\n      if (props.mode !== 'push') return;\n      nextTick(() => {\n        if (!parentNode.value) return;\n        parentNode.value.style.cssText = 'transition: margin 300ms cubic-bezier(0.7, 0.3, 0.1, 1) 0s;';\n      });\n    };\n\n    // push 动画效果处理\n    const updatePushMode = () => {\n      if (!parentNode.value || props.mode !== 'push') return;\n      const marginValueData = {\n        left: { name: 'margin-left', value: sizeValue.value },\n        right: { name: 'margin-right', value: `-${sizeValue.value}` },\n        top: { name: 'margin-top', value: sizeValue.value },\n        bottom: { name: 'margin-bottom', value: `-${sizeValue.value}` },\n      }[props.placement];\n      if (isVisible.value) {\n        parentNode.value.style.setProperty(marginValueData.name, marginValueData.value);\n      } else {\n        parentNode.value.style.removeProperty(marginValueData.name);\n      }\n    };\n\n    // locale 全局配置，插槽，props，默认值，决定了按钮最终呈现\n    const getDefaultFooter = () => {\n      const confirmBtn = getConfirmBtn({\n        confirmBtn: props.confirmBtn as TdDrawerProps['confirmBtn'],\n        globalConfirm: globalConfig.value.confirm,\n        className: `${COMPONENT_NAME.value}__confirm`,\n      });\n      const cancelBtn = getCancelBtn({\n        cancelBtn: props.cancelBtn as TdDrawerProps['cancelBtn'],\n        globalCancel: globalConfig.value.cancel,\n        className: `${COMPONENT_NAME.value}__cancel`,\n      });\n      return (\n        <div style={footerStyle.value}>\n          {props.placement === 'right' ? confirmBtn : null}\n          {cancelBtn}\n          {props.placement !== 'right' ? confirmBtn : null}\n        </div>\n      );\n    };\n\n    const { isTopInteractivePopup } = usePopupManager('drawer', {\n      visible: computedVisible,\n    });\n\n    watch(modeAndPlacement, handlePushMode, { immediate: true });\n\n    const updateVisibleState = (value: boolean) => {\n      if (value) {\n        isMounted.value = true;\n      }\n\n      if (props.destroyOnClose) {\n        if (value) {\n          destroyOnCloseVisible.value = false;\n          setTimeout(() => (isVisible.value = true));\n        } else {\n          isVisible.value = false;\n          // immediate 的 watch 的第一次触发，会将设置为 true 的行为延后\n          // 插件场景下，watch -> create 方法 的立刻调用，导致 destroyOnCloseVisible 被 watch 的第一次触发覆盖\n          // 所以关闭时候，默认先置为 false\n          // 后续考虑移除 immediate 的 watch ?\n          if (destroyOnCloseVisible.value) {\n            destroyOnCloseVisible.value = false;\n          }\n          setTimeout(() => (destroyOnCloseVisible.value = true), 300);\n        }\n        return;\n      }\n\n      if (destroyOnCloseVisible.value && value) {\n        destroyOnCloseVisible.value = false;\n        setTimeout(() => (isVisible.value = true));\n        return;\n      }\n\n      setTimeout(() => (isVisible.value = value));\n    };\n\n    const addStyleElToHead = () => {\n      if (\n        !props.showInAttachedElement &&\n        props.preventScrollThrough &&\n        isVisible.value &&\n        (isMounted.value || !props.lazy)\n      ) {\n        if (!styleEl.value) {\n          createStyleEl();\n        }\n        if (styleEl.value && !document.head.contains(styleEl.value)) {\n          document.head.appendChild(styleEl.value);\n        }\n      }\n    };\n\n    watch(\n      () => props.visible,\n      (value) => {\n        if (isServer) return;\n        if (value) {\n          addStyleElToHead();\n          props.onBeforeOpen?.();\n        } else {\n          clearStyleEl();\n          props.onBeforeClose?.();\n        }\n\n        updateVisibleState(value);\n      },\n      { immediate: true },\n    );\n\n    const handleCloseBtnClick = (e: MouseEvent) => {\n      props.onCloseBtnClick?.({ e });\n      closeDrawer({ trigger: 'close-btn', e });\n    };\n    const handleWrapperClick = (e: MouseEvent) => {\n      props.onOverlayClick?.({ e });\n      if (props.closeOnOverlayClick ?? globalConfig.value.closeOnOverlayClick) {\n        closeDrawer({ trigger: 'overlay', e });\n      }\n    };\n\n    const closeDrawer = (params: DrawerCloseContext) => {\n      props.onClose?.(params);\n      context.emit('update:visible', false);\n    };\n\n    onUpdated(updatePushMode);\n\n    onMounted(() => {\n      addStyleElToHead();\n      window.addEventListener('keydown', handleEscKeydown);\n    });\n\n    onBeforeUnmount(() => {\n      clearStyleEl();\n      window.removeEventListener('keydown', handleEscKeydown);\n    });\n\n    const shouldRender = computed(() => {\n      if (!isMounted.value) {\n        return !props.lazy;\n      } else {\n        return isVisible.value || !destroyOnCloseVisible.value;\n      }\n    });\n\n    return () => {\n      if (!shouldRender.value) return;\n\n      const body = renderContent('body', 'default');\n      const headerContent = renderTNodeJSX('header');\n      const defaultFooter = getDefaultFooter();\n\n      return (\n        <Teleport disabled={!props.attach || !teleportElement.value} to={teleportElement.value}>\n          <div\n            ref={drawerEle}\n            class={drawerClasses.value}\n            style={{ zIndex: props.zIndex }}\n            onKeydown={handleEscKeydown}\n            tabindex={0}\n            {...context.attrs}\n          >\n            {props.showOverlay && <div class={`${COMPONENT_NAME.value}__mask`} onClick={handleWrapperClick} />}\n            <div class={wrapperClasses.value} style={{ ...wrapperStyles.value, ...draggingStyles.value }}>\n              {headerContent && <div class={`${COMPONENT_NAME.value}__header`}>{headerContent}</div>}\n              {props.closeBtn && (\n                <div class={`${COMPONENT_NAME.value}__close-btn`} onClick={handleCloseBtnClick}>\n                  {renderTNodeJSX('closeBtn', <CloseIcon />)}\n                </div>\n              )}\n              <div class={[`${COMPONENT_NAME.value}__body`, 'narrow-scrollbar']}>{body}</div>\n              {props.footer && (\n                <div class={`${COMPONENT_NAME.value}__footer`}>{renderTNodeJSX('footer', defaultFooter)}</div>\n              )}\n              {props.sizeDraggable && <div style={draggableLineStyles.value} onMousedown={enableDrag}></div>}\n            </div>\n          </div>\n        </Teleport>\n      );\n    };\n  },\n});\n"], "names": ["key", "defineComponent", "name", "inheritAttrs", "props", "emits", "setup", "context", "destroyOnCloseVisible", "ref", "isVisible", "styleEl", "styleTimer", "_useConfig", "useConfig", "globalConfig", "_useGlobalIcon", "useGlobalIcon", "CloseIcon", "TdCloseIcon", "renderTNodeJSX", "useTNodeJSX", "renderContent", "useContent", "COMPONENT_NAME", "usePrefixClass", "_useDrag", "useDrag", "draggedSizeValue", "enableDrag", "draggableLineStyles", "draggingStyles", "computedVisible", "computed", "visible", "isMounted", "teleportElement", "useTeleport", "attach", "confirmBtnAction", "e", "_props2$onConfirm", "onConfirm", "cancelBtnAction", "_props2$onCancel", "onCancel", "closeDrawer", "trigger", "_useAction", "useAction", "getConfirmBtn", "getCancelBtn", "drawerEle", "drawerClasses", "value", "concat", "placement", "_defineProperty", "showInAttachedElement", "showOverlay", "drawerClassName", "sizeValue", "_props2$size", "size", "defaultSize", "isNaN", "Number", "small", "medium", "large", "wrapperStyles", "transform", "width", "includes", "height", "wrapperClasses", "parentNode", "_drawerEle$value", "modeAndPlacement", "mode", "join", "footerStyle", "display", "justifyContent", "handleEscKeydown", "_props2$closeOnEscKey", "closeOnEscKeydown", "isTopInteractivePopup", "_props2$onEscKeydown", "onEscKeydown", "stopImmediatePropagation", "clearStyleEl", "clearTimeout", "setTimeout", "_styleEl$value", "_styleEl$value$remove", "<PERSON><PERSON><PERSON><PERSON>", "call", "nextTick", "_drawerEle$value2", "_drawerEle$value2$foc", "focus", "createStyleEl", "hasScrollBar", "window", "innerWidth", "document", "documentElement", "clientWidth", "scrollWidth", "getScrollbarWidth", "createElement", "dataset", "id", "Date", "innerHTML", "handlePushMode", "style", "cssText", "updatePushMode", "marginValueData", "left", "right", "top", "bottom", "setProperty", "removeProperty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmBtn", "globalConfirm", "confirm", "className", "cancelBtn", "globalCancel", "cancel", "_createVNode", "_usePopupManager", "usePopupManager", "watch", "immediate", "updateVisibleState", "destroyOnClose", "addStyleElToHead", "preventScrollThrough", "lazy", "head", "contains", "append<PERSON><PERSON><PERSON>", "isServer", "_props2$onBeforeOpen", "onBeforeOpen", "_props2$onBeforeClose", "onBeforeClose", "handleCloseBtnClick", "_props2$onCloseBtnCli", "onCloseBtnClick", "handleWrapperClick", "_props2$onOverlayClic", "_props2$closeOnOverla", "onOverlayClick", "closeOnOverlayClick", "params", "_props2$onClose", "onClose", "emit", "onUpdated", "onMounted", "addEventListener", "onBeforeUnmount", "removeEventListener", "shouldRender", "body", "headerContent", "defaultFooter", "Teleport", "_default", "_mergeProps", "zIndex", "attrs", "_objectSpread", "closeBtn", "footer", "sizeDraggable"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,IAAIA,GAAM,GAAA,CAAA,CAAA;AAEV,cAAeC,eAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,SAAA;AACNC,EAAAA,YAAc,EAAA,KAAA;AACdC,EAAAA,KAAA,EAAAA,KAAA;EACAC,KAAA,EAAO,CAAC,gBAAgB,CAAA;AACxBC,EAAAA,KAAA,WAAAA,KAAAA,CAAMF,QAAOG,OAAS,EAAA;AACd,IAAA,IAAAC,qBAAA,GAAwBC,IAAI,KAAK,CAAA,CAAA;AACjC,IAAA,IAAAC,SAAA,GAAYD,IAAI,KAAK,CAAA,CAAA;AAC3B,IAAA,IAAME,UAAUF,GAAI,EAAA,CAAA;AACpB,IAAA,IAAMG,aAAaH,GAAI,EAAA,CAAA;AACvB,IAAA,IAAAI,UAAA,GAAyBC,SAAA,CAAU,QAAQ,CAAA;MAAnCC,YAAA,GAAAF,UAAA,CAAAE,YAAA,CAAA;IACR,IAAAC,cAAA,GAAsBC,cAAc;AAAEC,QAAAA,SAAA,EAAWC,SAAAA;AAAY,OAAC,CAAA;MAAtDD,WAAU,GAAAF,cAAA,CAAVE,SAAU,CAAA;AAClB,IAAA,IAAME,iBAAiBC,WAAY,EAAA,CAAA;AACnC,IAAA,IAAMC,gBAAgBC,UAAW,EAAA,CAAA;AAC3B,IAAA,IAAAC,cAAA,GAAiBC,eAAe,QAAQ,CAAA,CAAA;AAC9C,IAAA,IAAAC,QAAA,GAA8EC,QAAQvB,MAAsB,CAAA;MAApGwB,gBAAkB,GAAAF,QAAA,CAAlBE,gBAAkB;MAAAC,UAAA,GAAAH,QAAA,CAAAG,UAAA;MAAYC,+BAAAA;MAAqBC,cAAe,GAAAL,QAAA,CAAfK,cAAe,CAAA;IAC1E,IAAMC,eAAkB,GAAAC,QAAA,CAAS,YAAA;MAAA,OAAM7B,MAAAA,CAAM8B,OAAO,CAAA;KAAA,CAAA,CAAA;AAC9C,IAAA,IAAAC,SAAA,GAAY1B,IAAI,KAAK,CAAA,CAAA;IAG3B,IAAM2B,eAAkB,GAAAC,WAAA,CAAY,YAAA;MAAA,OAAMjC,MAAAA,CAAMkC,MAAM,CAAA;KAAA,CAAA,CAAA;AAEhD,IAAA,IAAAC,gBAAA,GAAmB,SAAnBA,gBAAAA,CAAoBC,CAAkB,EAAA;AAAA,MAAA,IAAAC,iBAAA,CAAA;AAC1CrC,MAAAA,CAAAA,iBAAAA,GAAAA,MAAM,CAAAsC,SAAA,MAAA,IAAA,IAAAD,iBAAA,KAAA,KAAA,CAAA,IAANrC,iBAAAA,CAAAA,IAAAA,CAAAA,MAAM,EAAY;AAAEoC,QAAAA,CAAA,EAAAA,CAAAA;AAAE,OAAC,CAAA,CAAA;KACzB,CAAA;AACM,IAAA,IAAAG,eAAA,GAAkB,SAAlBA,eAAAA,CAAmBH,CAAkB,EAAA;AAAA,MAAA,IAAAI,gBAAA,CAAA;AACzCxC,MAAAA,CAAAA,gBAAAA,GAAAA,MAAM,CAAAyC,QAAA,MAAA,IAAA,IAAAD,gBAAA,KAAA,KAAA,CAAA,IAANxC,gBAAAA,CAAAA,IAAAA,CAAAA,MAAM,EAAW;AAAEoC,QAAAA,CAAA,EAAAA,CAAAA;AAAE,OAAC,CAAA,CAAA;AACtBM,MAAAA,WAAA,CAAY;AAAEC,QAAAA,OAAA,EAAS,QAAU;AAAAP,QAAAA,CAAA,EAAAA,CAAAA;AAAE,OAAC,CAAA,CAAA;KACtC,CAAA;IACM,IAAAQ,UAAA,GAAkCC,UAAU;AAAEV,QAAAA,gBAAA,EAAAA,gBAAA;AAAkBI,QAAAA,iBAAAA,eAAAA;AAAgB,OAAC,CAAA;MAA/EO,2BAAAA;MAAeC,YAAa,GAAAH,UAAA,CAAbG,YAAa,CAAA;AAC9B,IAAA,IAAAC,SAAA,GAAY3C,IAAwB,IAAI,CAAA,CAAA;IACxC,IAAA4C,aAAA,GAAgBpB,SAAS,YAAA;MAAA,OAAM,CACnCT,cAAe,CAAA8B,KAAA,EAAA,EAAA,CAAAC,MAAA,CACZ/B,cAAe,CAAA8B,KAAA,EAAA,IAAA,CAAA,CAAAC,MAAA,CAAUnD,MAAM,CAAAoD,SAAA,CAAAC,EAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAF,EAAAA,EAAAA,EAAAA,CAAAA,MAAA,CAE5B/B,cAAe,CAAA8B,KAAA,aAAgB5C,SAAU,CAAA4C,KAAA,CAAA,EAAA,EAAA,CAAAC,MAAA,CACzC/B,cAAe,CAAA8B,KAAA,EAAA,UAAA,CAAA,EAAkBlD,MAAM,CAAAsD,qBAAA,CAAA,EAAA,EAAA,CAAAH,MAAA,CACvC/B,cAAe,CAAA8B,KAAA,EAAA,gBAAA,CAAA,EAAwB,CAAClD,MAAM,CAAAuD,WAAA,CAEpDvD,EAAAA,MAAM,CAAAwD,eAAA,CACP,CAAA;KAAA,CAAA,CAAA;AAEK,IAAA,IAAAC,SAAA,GAAY5B,SAAS,YAAM;AAAA,MAAA,IAAA6B,YAAA,CAAA;AAC/B,MAAA,IAAIlC,gBAAiB,CAAA0B,KAAA,EAAO,OAAO1B,gBAAiB,CAAA0B,KAAA,CAAA;AACpD,MAAA,IAAMS,IAAO3D,GAAAA,CAAAA,YAAAA,GAAAA,MAAAA,CAAM2D,IAAQ,MAAA,IAAA,IAAAD,YAAA,KAAA,KAAA,CAAA,GAAAA,YAAA,GAAA/C,YAAA,CAAauC,KAAM,CAAAS,IAAA,CAAA;AAC9C,MAAA,IAAMC,cAAcC,KAAM,CAAAC,MAAA,CAAOH,IAAI,CAAC,CAAA,GAAIA,iBAAUA,IAAA,EAAA,IAAA,CAAA,CAAA;MAElD,OAAA;AACEI,QAAAA,KAAO,EAAA,OAAA;AACPC,QAAAA,MAAQ,EAAA,OAAA;AACRC,QAAAA,KAAO,EAAA,OAAA;AACT,QAAEN,IAAS,CAAA,IAAAC,WAAA,CAAA;AAEf,KAAC,CAAA,CAAA;IACK,IAAAM,aAAA,GAAgBrC,SAAS,YAAA;MAAA,OAAO;QACpCsC,SAAA,EAAW7D,SAAU,CAAA4C,KAAA,GAAQ,eAAkB,GAAA,KAAA,CAAA;AAC/CkB,QAAAA,KAAA,EAAO,CAAC,MAAA,EAAQ,OAAO,CAAA,CAAEC,SAASrE,MAAM,CAAAoD,SAAS,CAAI,GAAAK,SAAA,CAAUP,KAAQ,GAAA,EAAA;AACvEoB,QAAAA,MAAA,EAAQ,CAAC,KAAA,EAAO,QAAQ,CAAA,CAAED,SAASrE,MAAM,CAAAoD,SAAS,CAAI,GAAAK,SAAA,CAAUP,KAAQ,GAAA,EAAA;OACxE,CAAA;AAAA,KAAA,CAAA,CAAA;IAEI,IAAAqB,cAAA,GAAiB1C,SAAS,YAAA;AAAA,MAAA,OAAM,IAAAsB,MAAA,CACjC/B,cAAe,CAAA8B,KAAA,2BAAAC,MAAA,CACf/B,cAAe,CAAA8B,KAAA,EAAAC,qBAAAA,CAAAA,CAAAA,MAAA,CAA2BnD,MAAM,CAAAoD,SAAA,CACpD,CAAA,CAAA;KAAA,CAAA,CAAA;IAED,IAAMoB,UAAa,GAAA3C,QAAA,CAAsB,YAAA;AAAA,MAAA,IAAA4C,gBAAA,CAAA;MAAA,OAAAA,CAAAA,gBAAA,GAAMzB,SAAA,CAAUE,0DAAVuB,gBAAA,CAAiBD,UAAyB,CAAA;KAAA,CAAA,CAAA;IAEnF,IAAAE,gBAAA,GAAmB7C,QAAiB,CAAA,YAAA;AAAA,MAAA,OAAM,CAAC7B,MAAAA,CAAM2E,MAAM3E,MAAM,CAAAoD,SAAS,CAAE,CAAAwB,IAAA,EAAM,CAAA;KAAA,CAAA,CAAA;IAC9E,IAAAC,WAAA,GAAchD,SAAS,YAAA;MAAA,OAAO;AAClCiD,QAAAA,OAAS,EAAA,MAAA;QACTC,cAAgB/E,EAAAA,MAAAA,CAAMoD,SAAc,KAAA,OAAA,GAAU,YAAe,GAAA,UAAA;OAC7D,CAAA;AAAA,KAAA,CAAA,CAAA;AAEI,IAAA,IAAA4B,gBAAA,GAAmB,SAAnBA,gBAAAA,CAAoB5C,CAAqB,EAAA;AAAA,MAAA,IAAA6C,qBAAA,CAAA;AAE1CjF,MAAAA,IAAAA,CAAAA,CAAAA,qBAAAA,GAAAA,MAAAA,CAAMkF,iBAAqB,MAAAD,IAAAA,IAAAA,qBAAA,KAAAA,KAAAA,CAAAA,GAAAA,qBAAA,GAAAtE,YAAA,CAAauC,KAAM,CAAAgC,iBAAA,KAC/C9C,CAAE,CAAAxC,GAAA,KAAQ,QACV,IAAAU,SAAA,CAAU4C,KACV,IAAAiC,qBAAA,EACA,EAAA;AAAA,QAAA,IAAAC,oBAAA,CAAA;AACApF,QAAAA,CAAAA,oBAAAA,GAAAA,MAAM,CAAAqF,YAAA,MAAA,IAAA,IAAAD,oBAAA,KAAA,KAAA,CAAA,IAANpF,oBAAAA,CAAAA,IAAAA,CAAAA,MAAM,EAAe;AAAEoC,UAAAA,CAAA,EAAAA,CAAAA;AAAE,SAAC,CAAA,CAAA;AAC1BM,QAAAA,WAAA,CAAY;AAAEC,UAAAA,OAAA,EAAS,KAAO;AAAAP,UAAAA,CAAA,EAAAA,CAAAA;AAAE,SAAC,CAAA,CAAA;QAEjCA,CAAA,CAAEkD,wBAAyB,EAAA,CAAA;AAC7B,OAAA;KACF,CAAA;AAEA,IAAA,IAAMC,eAAe,SAAfA,eAAqB;AACzBC,MAAAA,YAAA,CAAahF,WAAW0C,KAAK,CAAA,CAAA;AAClB1C,MAAAA,UAAA,CAAA0C,KAAA,GAAQuC,WAAW,YAAM;QAAA,IAAAC,cAAA,EAAAC,qBAAA,CAAA;AAClC,QAAA,CAAAD,cAAA,GAAAnF,OAAA,CAAQ2C,KAAO,MAAAwC,IAAAA,IAAAA,cAAA,KAAAA,KAAAA,CAAAA,IAAAA,CAAAA,cAAA,GAAfA,cAAA,CAAelB,UAAA,cAAAkB,cAAA,KAAA,KAAA,CAAA,IAAA,CAAAC,qBAAA,GAAfD,cAAA,CAA2BE,WAAc,MAAA,IAAA,IAAAD,qBAAA,KAAzCA,KAAAA,CAAAA,IAAAA,qBAAA,CAAAE,IAAA,CAAAH,cAAA,EAAyCnF,OAAA,CAAQ2C,KAAK,CAAA,CAAA;QACtD3C,OAAA,CAAQ2C,KAAQ,GAAA,IAAA,CAAA;SACf,GAAG,CAAA,CAAA;AACN4C,MAAAA,QAAA,CAAS,YAAM;QAAA,IAAAC,iBAAA,EAAAC,qBAAA,CAAA;QACb,CAAAD,iBAAA,GAAA/C,SAAA,CAAUE,4EAAV6C,iBAAA,CAAiBE,KAAQ,cAAAD,qBAAA,KAAA,KAAA,CAAA,IAAzBA,qBAAA,CAAAH,IAAA,CAAAE,iBAAyB,CAAA,CAAA;AAC3B,OAAC,CAAA,CAAA;KACH,CAAA;AAEA,IAAA,IAAMG,gBAAgB,SAAhBA,gBAAsB;AAC1B,MAAA,IAAI,CAAC3F,OAAQ,CAAA2C,KAAA,EAAO,OAAA;MACpB,IAAMiD,YAAe,GAAAC,MAAA,CAAOC,UAAa,GAAAC,QAAA,CAASC,eAAgB,CAAAC,WAAA,CAAA;MAC5D,IAAAC,WAAA,GAAcN,YAAe,GAAAO,iBAAA,EAAsB,GAAA,CAAA,CAAA;MACjDnG,OAAA,CAAA2C,KAAA,GAAQoD,QAAS,CAAAK,aAAA,CAAc,OAAO,CAAA,CAAA;MACtCpG,OAAA,CAAA2C,KAAA,CAAM0D,QAAQC,EAAK,GAAA,YAAA,CAAA1D,MAAA,CAAa,CAAC,IAAI2D,IAAA,gBAAWlH,GAAO,IAAA,CAAA,CAAA,CAAA;AAC/DW,MAAAA,OAAA,CAAQ2C,MAAM6D,SAAY,GAAA5D,yIAAAA,CAAAA,MAAA,CAIpBnD,MAAM,CAAA2E,IAAA,KAAS,MAAS,GAAA,EAAA,GAAA,qBAAA,CAAAxB,MAAA,CAA2BsD,WAAA,SAAA,EAAA,qBAAA,CAAA,CAAA;KAG3D,CAAA;AAEA,IAAA,IAAMO,iBAAiB,SAAjBA,iBAAuB;AAC3B,MAAA,IAAIhH,OAAM2E,IAAS,KAAA,MAAA,EAAQ,OAAA;AAC3BmB,MAAAA,QAAA,CAAS,YAAM;AACb,QAAA,IAAI,CAACtB,UAAW,CAAAtB,KAAA,EAAO,OAAA;AACZsB,QAAAA,UAAA,CAAAtB,KAAA,CAAM+D,MAAMC,OAAU,GAAA,6DAAA,CAAA;AACnC,OAAC,CAAA,CAAA;KACH,CAAA;AAGA,IAAA,IAAMC,iBAAiB,SAAjBA,iBAAuB;MAC3B,IAAI,CAAC3C,UAAA,CAAWtB,KAASlD,IAAAA,MAAAA,CAAM2E,IAAS,KAAA,MAAA,EAAQ,OAAA;AAChD,MAAA,IAAMyC,eAAkB,GAAA;AACtBC,QAAAA,MAAM;AAAEvH,UAAAA,IAAA,EAAM,aAAe;UAAAoD,KAAA,EAAOO,UAAUP,KAAAA;SAAM;AACpDoE,QAAAA,OAAO;AAAExH,UAAAA,IAAA,EAAM;AAAgBoD,UAAAA,KAAO,EAAAC,GAAAA,CAAAA,MAAA,CAAIM,UAAUP,KAAQ,CAAA;SAAA;AAC5DqE,QAAAA,KAAK;AAAEzH,UAAAA,IAAA,EAAM,YAAc;UAAAoD,KAAA,EAAOO,UAAUP,KAAAA;SAAM;AAClDsE,QAAAA,QAAQ;AAAE1H,UAAAA,IAAA,EAAM;AAAiBoD,UAAAA,KAAO,EAAAC,GAAAA,CAAAA,MAAA,CAAIM,UAAUP,KAAQ,CAAA;AAAA,SAAA;AAChE,QAAElD,MAAM,CAAAoD,SAAA,CAAA,CAAA;MACR,IAAI9C,UAAU4C,KAAO,EAAA;AACnBsB,QAAAA,UAAA,CAAWtB,MAAM+D,KAAM,CAAAQ,WAAA,CAAYL,eAAgB,CAAAtH,IAAA,EAAMsH,gBAAgBlE,KAAK,CAAA,CAAA;AAChF,OAAO,MAAA;QACLsB,UAAA,CAAWtB,KAAM,CAAA+D,KAAA,CAAMS,cAAe,CAAAN,eAAA,CAAgBtH,IAAI,CAAA,CAAA;AAC5D,OAAA;KACF,CAAA;AAGA,IAAA,IAAM6H,mBAAmB,SAAnBA,mBAAyB;MAC7B,IAAMC,aAAa9E,aAAc,CAAA;QAC/B8E,YAAY5H,MAAM,CAAA4H,UAAA;AAClBC,QAAAA,aAAA,EAAelH,aAAauC,KAAM,CAAA4E,OAAA;AAClCC,QAAAA,SAAA,EAAA5E,EAAAA,CAAAA,MAAA,CAAc/B,cAAe,CAAA8B,KAAA,EAAA,WAAA,CAAA;AAC/B,OAAC,CAAA,CAAA;MACD,IAAM8E,YAAYjF,YAAa,CAAA;QAC7BiF,WAAWhI,MAAM,CAAAgI,SAAA;AACjBC,QAAAA,YAAA,EAActH,aAAauC,KAAM,CAAAgF,MAAA;AACjCH,QAAAA,SAAA,EAAA5E,EAAAA,CAAAA,MAAA,CAAc/B,cAAe,CAAA8B,KAAA,EAAA,UAAA,CAAA;AAC/B,OAAC,CAAA,CAAA;AAEC,MAAA,OAAAiF,WAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAYtD,WAAY,CAAA3B,KAAAA;OACrBlD,EAAAA,CAAAA,MAAAA,CAAMoD,SAAc,KAAA,OAAA,GAAUwE,UAAa,GAAA,IAAA,EAC3CI,SAAA,EACAhI,MAAAA,CAAMoD,SAAc,KAAA,OAAA,GAAUwE,UAAa,GAAA,IAAA,CAAA,CAAA,CAAA;KAGlD,CAAA;AAEA,IAAA,IAAAQ,gBAAA,GAAkCC,eAAA,CAAgB,QAAU,EAAA;AAC1DvG,QAAAA,OAAS,EAAAF,eAAAA;AACX,OAAC,CAAA;MAFOuD,qBAAA,GAAAiD,gBAAA,CAAAjD,qBAAA,CAAA;AAIRmD,IAAAA,KAAA,CAAM5D,gBAAkB,EAAAsC,cAAA,EAAgB;AAAEuB,MAAAA,SAAA,EAAW,IAAA;AAAK,KAAC,CAAA,CAAA;AAErD,IAAA,IAAAC,kBAAA,GAAqB,SAArBA,kBAAAA,CAAsBtF,KAAmB,EAAA;AAC7C,MAAA,IAAIA,KAAO,EAAA;QACTnB,SAAA,CAAUmB,KAAQ,GAAA,IAAA,CAAA;AACpB,OAAA;MAEA,IAAIlD,OAAMyI,cAAgB,EAAA;AACxB,QAAA,IAAIvF,KAAO,EAAA;UACT9C,qBAAA,CAAsB8C,KAAQ,GAAA,KAAA,CAAA;AACnBuC,UAAAA,UAAA,CAAA,YAAA;AAAA,YAAA,OAAOnF,SAAU,CAAA4C,KAAA,GAAQ,IAAK,CAAA;WAAA,CAAA,CAAA;AAC3C,SAAO,MAAA;UACL5C,SAAA,CAAU4C,KAAQ,GAAA,KAAA,CAAA;UAKlB,IAAI9C,sBAAsB8C,KAAO,EAAA;YAC/B9C,qBAAA,CAAsB8C,KAAQ,GAAA,KAAA,CAAA;AAChC,WAAA;AACAuC,UAAAA,UAAA,CAAW,YAAA;AAAA,YAAA,OAAOrF,qBAAA,CAAsB8C,KAAQ,GAAA,IAAA,CAAA;AAAA,WAAA,EAAO,GAAG,CAAA,CAAA;AAC5D,SAAA;AACA,QAAA,OAAA;AACF,OAAA;AAEI,MAAA,IAAA9C,qBAAA,CAAsB8C,SAASA,KAAO,EAAA;QACxC9C,qBAAA,CAAsB8C,KAAQ,GAAA,KAAA,CAAA;AACnBuC,QAAAA,UAAA,CAAA,YAAA;AAAA,UAAA,OAAOnF,SAAU,CAAA4C,KAAA,GAAQ,IAAK,CAAA;SAAA,CAAA,CAAA;AACzC,QAAA,OAAA;AACF,OAAA;AAEWuC,MAAAA,UAAA,CAAA,YAAA;AAAA,QAAA,OAAOnF,SAAU,CAAA4C,KAAA,GAAQA,KAAM,CAAA;OAAA,CAAA,CAAA;KAC5C,CAAA;AAEA,IAAA,IAAMwF,mBAAmB,SAAnBA,mBAAyB;MAE3B,IAAA,CAAC1I,MAAM,CAAAsD,qBAAA,IACPtD,MAAM,CAAA2I,oBAAA,IACNrI,SAAU,CAAA4C,KAAA,KACTnB,SAAU,CAAAmB,KAAA,IAAS,CAAClD,MAAAA,CAAM4I,IAC3B,CAAA,EAAA;AACI,QAAA,IAAA,CAACrI,QAAQ2C,KAAO,EAAA;AACJgD,UAAAA,aAAA,EAAA,CAAA;AAChB,SAAA;AACI,QAAA,IAAA3F,OAAA,CAAQ2C,SAAS,CAACoD,QAAA,CAASuC,KAAKC,QAAS,CAAAvI,OAAA,CAAQ2C,KAAK,CAAG,EAAA;UAClDoD,QAAA,CAAAuC,IAAA,CAAKE,WAAY,CAAAxI,OAAA,CAAQ2C,KAAK,CAAA,CAAA;AACzC,SAAA;AACF,OAAA;KACF,CAAA;AAEAoF,IAAAA,KAAA,CACE,YAAA;MAAA,OAAMtI,MAAM,CAAA8B,OAAA,CAAA;KACZ,EAAA,UAACoB,KAAU,EAAA;AACL,MAAA,IAAA8F,QAAA,EAAU,OAAA;AACd,MAAA,IAAI9F,KAAO,EAAA;AAAA,QAAA,IAAA+F,oBAAA,CAAA;AACQP,QAAAA,gBAAA,EAAA,CAAA;AACjB1I,QAAAA,CAAAA,oBAAAA,GAAAA,OAAMkJ,YAAe,MAAA,IAAA,IAAAD,oBAAA,KAAA,KAAA,CAAA,IAArBjJ,oBAAAA,CAAAA,IAAAA,CAAAA,MAAqB,CAAA,CAAA;AACvB,OAAO,MAAA;AAAA,QAAA,IAAAmJ,qBAAA,CAAA;AACQ5D,QAAAA,YAAA,EAAA,CAAA;AACbvF,QAAAA,CAAAA,qBAAAA,GAAAA,OAAMoJ,aAAgB,MAAA,IAAA,IAAAD,qBAAA,KAAA,KAAA,CAAA,IAAtBnJ,qBAAAA,CAAAA,IAAAA,CAAAA,MAAsB,CAAA,CAAA;AACxB,OAAA;MAEAwI,kBAAA,CAAmBtF,KAAK,CAAA,CAAA;AAC1B,KAAA,EACA;AAAEqF,MAAAA,WAAW,IAAA;AAAK,KACpB,CAAA,CAAA;AAEM,IAAA,IAAAc,mBAAA,GAAsB,SAAtBA,mBAAAA,CAAuBjH,CAAkB,EAAA;AAAA,MAAA,IAAAkH,qBAAA,CAAA;AAC7CtJ,MAAAA,CAAAA,qBAAAA,GAAAA,MAAM,CAAAuJ,eAAA,MAAA,IAAA,IAAAD,qBAAA,KAAA,KAAA,CAAA,IAANtJ,qBAAAA,CAAAA,IAAAA,CAAAA,MAAM,EAAkB;AAAEoC,QAAAA,CAAA,EAAAA,CAAAA;AAAE,OAAC,CAAA,CAAA;AAC7BM,MAAAA,WAAA,CAAY;AAAEC,QAAAA,OAAA,EAAS,WAAa;AAAAP,QAAAA,CAAA,EAAAA,CAAAA;AAAE,OAAC,CAAA,CAAA;KACzC,CAAA;AACM,IAAA,IAAAoH,kBAAA,GAAqB,SAArBA,kBAAAA,CAAsBpH,CAAkB,EAAA;MAAA,IAAAqH,qBAAA,EAAAC,qBAAA,CAAA;AAC5C1J,MAAAA,CAAAA,qBAAAA,GAAAA,MAAM,CAAA2J,cAAA,MAAA,IAAA,IAAAF,qBAAA,KAAA,KAAA,CAAA,IAANzJ,qBAAAA,CAAAA,IAAAA,CAAAA,MAAM,EAAiB;AAAEoC,QAAAA,CAAA,EAAAA,CAAAA;AAAE,OAAC,CAAA,CAAA;AAC5B,MAAA,IAAA,CAAAsH,qBAAA,GAAI1J,MAAM,CAAA4J,mBAAA,MAAAF,IAAAA,IAAAA,qBAAA,KAAAA,KAAAA,CAAAA,GAAAA,qBAAA,GAAuB/I,YAAa,CAAAuC,KAAA,CAAM0G,mBAAqB,EAAA;AACvElH,QAAAA,WAAA,CAAY;AAAEC,UAAAA,OAAA,EAAS,SAAW;AAAAP,UAAAA,CAAA,EAAAA,CAAAA;AAAE,SAAC,CAAA,CAAA;AACvC,OAAA;KACF,CAAA;AAEM,IAAA,IAAAM,WAAA,GAAc,SAAdA,WAAAA,CAAemH,MAA+B,EAAA;AAAA,MAAA,IAAAC,eAAA,CAAA;AAClD9J,MAAAA,CAAAA,eAAAA,GAAAA,MAAAA,CAAM+J,yCAAN/J,KAAAA,CAAAA,IAAAA,eAAAA,CAAAA,IAAAA,CAAAA,MAAAA,EAAgB6J,MAAM,CAAA,CAAA;AACd1J,MAAAA,OAAA,CAAA6J,IAAA,CAAK,kBAAkB,KAAK,CAAA,CAAA;KACtC,CAAA;IAEAC,SAAA,CAAU9C,cAAc,CAAA,CAAA;AAExB+C,IAAAA,SAAA,CAAU,YAAM;AACGxB,MAAAA,gBAAA,EAAA,CAAA;AACVtC,MAAAA,MAAA,CAAA+D,gBAAA,CAAiB,WAAWnF,gBAAgB,CAAA,CAAA;AACrD,KAAC,CAAA,CAAA;AAEDoF,IAAAA,eAAA,CAAgB,YAAM;AACP7E,MAAAA,YAAA,EAAA,CAAA;AACNa,MAAAA,MAAA,CAAAiE,mBAAA,CAAoB,WAAWrF,gBAAgB,CAAA,CAAA;AACxD,KAAC,CAAA,CAAA;AAEK,IAAA,IAAAsF,YAAA,GAAezI,SAAS,YAAM;AAC9B,MAAA,IAAA,CAACE,UAAUmB,KAAO,EAAA;QACpB,OAAO,CAAClD,MAAM,CAAA4I,IAAA,CAAA;AAChB,OAAO,MAAA;AACE,QAAA,OAAAtI,SAAA,CAAU4C,KAAS,IAAA,CAAC9C,qBAAsB,CAAA8C,KAAA,CAAA;AACnD,OAAA;AACF,KAAC,CAAA,CAAA;AAED,IAAA,OAAO,YAAM;AACX,MAAA,IAAI,CAACoH,YAAa,CAAApH,KAAA,EAAO,OAAA;AAEnB,MAAA,IAAAqH,IAAA,GAAOrJ,aAAc,CAAA,MAAA,EAAQ,SAAS,CAAA,CAAA;AACtC,MAAA,IAAAsJ,aAAA,GAAgBxJ,eAAe,QAAQ,CAAA,CAAA;AAC7C,MAAA,IAAMyJ,gBAAgB9C,gBAAiB,EAAA,CAAA;MAEvC,OAAAQ,WAAA,CAAAuC,QAAA,EAAA;QAAA,UACsB,EAAA,CAAC1K,MAAM,CAAAkC,MAAA,IAAU,CAACF,eAAA,CAAgBkB,KAAO;AAAA,QAAA,IAAA,EAAIlB,eAAgB,CAAAkB,KAAAA;AAAA,OAAA,EAAA;AAAA,QAAA,SAAA,EAAA,SAAAyH,QAAA,GAAA;UAAA,OAAAxC,CAAAA,WAAA,QAAAyC,UAAA,CAAA;AAAA,YAAA,KAAA,EAExE5H,SAAA;YAAA,OACEC,EAAAA,aAAA,CAAcC,KACrB;YAAA,OAAO,EAAA;cAAE2H,MAAA,EAAQ7K,MAAM,CAAA6K,MAAAA;aACvB;AAAA,YAAA,WAAA,EAAW7F,gBACX;YAAA,UAAU,EAAA,CAAA;WACN7E,EAAAA,OAAQ,CAAA2K,KAAA,CAAA,EAAA,CAEX9K,MAAAA,CAAMuD;+BAA8BnC,cAAA,CAAe8B,KAAe,EAAA,QAAA,CAAA;YAAA,SAASsG,EAAAA,kBAAAA;AAAoB,WAAA,EAAA,IAAA,CAAA,EAAArB,WAAA,CAAA,KAAA,EAAA;YAAA,OACpF5D,EAAAA,cAAA,CAAerB,KAAO;YAAA,OAAA6H,EAAAA,aAAA,CAAAA,aAAA,CAAY7G,EAAAA,EAAAA,aAAc,CAAAhB,KAAA,CAAA,EAAUvB,cAAA,CAAeuB,KAAM,CAAA;WACxFsH,EAAAA,CAAAA,aAAA,IAAArC,WAAA,CAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA,EAAA,CAAAhF,MAAA,CAAgC/B,cAAe,CAAA8B,KAAA,EAAA,UAAA,CAAA;AAAA,WAAA,EAAA,CAAkBsH,cAA/C,CAAA,EAClBxK,OAAMgL,QACL,IAAA7C,WAAA,CAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA,EAAA,CAAAhF,MAAA,CAAe/B,cAAe,CAAA8B,KAAA,EAAA,aAAA,CAAA;YAAA,SAA6BmG,EAAAA,mBAAAA;WACxDrI,EAAAA,CAAAA,eAAe,UAAY,EAAAmH,WAAA,CAAArH,WAAA,EAAa,IAAA,EAAA,IAAA,CAAA,EAD1C,CAAA,EAAAqH,WAAA,CAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAIS,IAAAhF,MAAA,CAAI/B,eAAe8B,KAAe,aAAA,kBAAkB,CAAA;AAAI,WAAA,EAAA,CAAAqH,IAAA,CACnEvK,CAAAA,EAAAA,MAAM,CAAAiL,MAAA,IAAA9C,WAAA,CAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA,EAAA,CAAAhF,MAAA,CACU/B,cAAe,CAAA8B,KAAA,EAAA,UAAA,CAAA;AAAA,WAAA,EAAA,CAAkBlC,cAAe,CAAA,QAAA,EAAUyJ,aAAa,CAAA,CAArF,CAAA,EAEFzK,MAAAA,CAAMkL;mBAA6BxJ,EAAAA,mBAAoB,CAAAwB,KAAA;YAAA,aAAoBzB,EAAAA,UAAAA;WAAY,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,SAAA;AAAA,OAAA,CAAA,CAAA;KAKlG,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}