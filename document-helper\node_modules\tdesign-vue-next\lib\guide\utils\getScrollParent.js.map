{"version": 3, "file": "getScrollParent.js", "sources": ["../../../../components/guide/utils/getScrollParent.ts"], "sourcesContent": ["import { elementInViewport } from '@tdesign/shared-utils';\n\nexport function getScrollParent(element: HTMLElement) {\n  let style = window.getComputedStyle(element);\n  const excludeStaticParent = style.position === 'absolute';\n  const overflowRegex = /(auto|scroll)/;\n\n  if (style.position === 'fixed') return document.body;\n\n  for (let parent = element; parent.parentElement; ) {\n    parent = parent.parentElement;\n    style = window.getComputedStyle(parent);\n    if (excludeStaticParent && style.position === 'static') {\n      continue;\n    }\n    if (overflowRegex.test(style.overflow + style.overflowY + style.overflowX)) return parent;\n  }\n\n  return document.body;\n}\n\nexport function scrollToParentVisibleArea(element: HTMLElement) {\n  const parent = getScrollParent(element);\n  if (parent === document.body) return;\n  // !todo 逻辑待验证\n  if (elementInViewport(element, parent)) return;\n  parent.scrollTop = element.offsetTop - parent.offsetTop;\n}\n"], "names": ["getScrollParent", "element", "style", "window", "getComputedStyle", "excludeStaticParent", "position", "overflowRegex", "document", "body", "parent", "parentElement", "test", "overflow", "overflowY", "overflowX", "scrollToParentVisibleArea", "elementInViewport", "scrollTop", "offsetTop"], "mappings": ";;;;;;;;;;;;AAEO,SAASA,gBAAgBC,OAAsB,EAAA;AAChD,EAAA,IAAAC,KAAA,GAAQC,MAAO,CAAAC,gBAAA,CAAiBH,OAAO,CAAA,CAAA;AACrC,EAAA,IAAAI,mBAAA,GAAsBH,MAAMI,QAAa,KAAA,UAAA,CAAA;EAC/C,IAAMC,aAAgB,GAAA,eAAA,CAAA;EAEtB,IAAIL,MAAMI,QAAa,KAAA,OAAA,EAAS,OAAOE,QAAS,CAAAC,IAAA,CAAA;EAEvC,KAAA,IAAAC,MAAA,GAAST,OAAS,EAAAS,MAAA,CAAOC,aAAiB,GAAA;IACjDD,MAAA,GAASA,MAAO,CAAAC,aAAA,CAAA;AACRT,IAAAA,KAAA,GAAAC,MAAA,CAAOC,iBAAiBM,MAAM,CAAA,CAAA;AAClC,IAAA,IAAAL,mBAAA,IAAuBH,KAAM,CAAAI,QAAA,KAAa,QAAU,EAAA;AACtD,MAAA,SAAA;AACF,KAAA;AACA,IAAA,IAAIC,cAAcK,IAAK,CAAAV,KAAA,CAAMW,WAAWX,KAAM,CAAAY,SAAA,GAAYZ,MAAMa,SAAS,CAAA,EAAU,OAAAL,MAAA,CAAA;AACrF,GAAA;EAEA,OAAOF,QAAS,CAAAC,IAAA,CAAA;AAClB,CAAA;AAEO,SAASO,0BAA0Bf,OAAsB,EAAA;AACxD,EAAA,IAAAS,MAAA,GAASV,gBAAgBC,OAAO,CAAA,CAAA;AACtC,EAAA,IAAIS,WAAWF,QAAS,CAAAC,IAAA,EAAM,OAAA;AAE1B,EAAA,IAAAQ,iBAAA,CAAkBhB,SAASS,MAAM,CAAA,EAAG,OAAA;EACjCA,MAAA,CAAAQ,SAAA,GAAYjB,OAAQ,CAAAkB,SAAA,GAAYT,MAAO,CAAAS,SAAA,CAAA;AAChD;;;;"}