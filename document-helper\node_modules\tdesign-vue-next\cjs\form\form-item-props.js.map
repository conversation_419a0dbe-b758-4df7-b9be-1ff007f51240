{"version": 3, "file": "form-item-props.js", "sources": ["../../../components/form/form-item-props.ts"], "sourcesContent": ["/* eslint-disable */\n\n/**\n * 该文件为脚本自动生成文件，请勿随意修改。如需修改请联系 PMC\n * */\n\nimport { TdFormItemProps } from '../form/type';\nimport { PropType } from 'vue';\n\nexport default {\n  /** label 原生属性 */\n  for: {\n    type: String,\n    default: '',\n  },\n  /** 表单项说明内容 */\n  help: {\n    type: [String, Function] as PropType<TdFormItemProps['help']>,\n  },\n  /** 字段标签名称 */\n  label: {\n    type: [String, Function] as PropType<TdFormItemProps['label']>,\n    default: '' as TdFormItemProps['label'],\n  },\n  /** 表单字段标签对齐方式：左对齐、右对齐、顶部对齐。默认使用 Form 的对齐方式，优先级高于 Form.labelAlign */\n  labelAlign: {\n    type: String as PropType<TdFormItemProps['labelAlign']>,\n    validator(val: TdFormItemProps['labelAlign']): boolean {\n      if (!val) return true;\n      return ['left', 'right', 'top'].includes(val);\n    },\n  },\n  /** 可以整体设置标签宽度，优先级高于 Form.labelWidth */\n  labelWidth: {\n    type: [String, Number] as PropType<TdFormItemProps['labelWidth']>,\n  },\n  /** 表单字段名称 */\n  name: {\n    type: String,\n    default: '',\n  },\n  /** 是否显示必填符号（*），优先级高于 Form.requiredMark */\n  requiredMark: {\n    type: Boolean,\n    default: undefined,\n  },\n  /** 表单字段校验规则 */\n  rules: {\n    type: Array as PropType<TdFormItemProps['rules']>,\n  },\n  /** 校验不通过时，是否显示错误提示信息，优先级高于 `Form.showErrorMessage` */\n  showErrorMessage: {\n    type: Boolean,\n    default: undefined,\n  },\n  /** 校验状态，可在需要完全自主控制校验状态时使用 */\n  status: {\n    type: String as PropType<TdFormItemProps['status']>,\n    default: '' as TdFormItemProps['status'],\n  },\n  /** 校验状态图标，值为 `true` 显示默认图标，默认图标有 成功、失败、警告 等，不同的状态图标不同。`statusIcon` 值为 `false`，不显示图标。`statusIcon` 值类型为渲染函数，则可以自定义右侧状态图标。优先级高级 Form 的 statusIcon */\n  statusIcon: {\n    type: [Boolean, Function] as PropType<TdFormItemProps['statusIcon']>,\n    default: undefined as TdFormItemProps['statusIcon'],\n  },\n  /** 是否显示校验成功的边框，默认不显示 */\n  successBorder: Boolean,\n  /** 自定义提示内容，样式跟随 `status` 变动，可在需要完全自主控制校验规则时使用 */\n  tips: {\n    type: [String, Function] as PropType<TdFormItemProps['tips']>,\n  },\n};\n"], "names": ["type", "String", "help", "Function", "label", "labelAlign", "validator", "val", "includes", "labelWidth", "Number", "name", "requiredMark", "Boolean", "rules", "Array", "showErrorMessage", "status", "statusIcon", "successBorder", "tips"], "mappings": ";;;;;;;;;;AASA,YAAe;EAEb,KAAK,EAAA;AACHA,IAAAA,IAAM,EAAAC,MAAA;IACN,SAAS,EAAA,EAAA;GACX;AAEAC,EAAAA,IAAM,EAAA;AACJF,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQE,QAAQ,CAAA;GACzB;AAEAC,EAAAA,KAAO,EAAA;AACLJ,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQE,QAAQ,CAAA;IACvB,SAAS,EAAA,EAAA;GACX;AAEAE,EAAAA,UAAY,EAAA;AACVL,IAAAA,IAAM,EAAAC,MAAA;AACNK,IAAAA,WAAAA,SAAAA,UAAUC,GAA6C,EAAA;AACrD,MAAA,IAAI,CAACA,GAAA,EAAY,OAAA,IAAA,CAAA;MACjB,OAAO,CAAC,MAAQ,EAAA,OAAA,EAAS,KAAK,CAAA,CAAEC,SAASD,GAAG,CAAA,CAAA;AAC9C,KAAA;GACF;AAEAE,EAAAA,UAAY,EAAA;AACVT,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQS,MAAM,CAAA;GACvB;AAEAC,EAAAA,IAAM,EAAA;AACJX,IAAAA,IAAM,EAAAC,MAAA;IACN,SAAS,EAAA,EAAA;GACX;AAEAW,EAAAA,YAAc,EAAA;AACZZ,IAAAA,IAAM,EAAAa,OAAA;AACN,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAC,EAAAA,KAAO,EAAA;AACLd,IAAAA,IAAM,EAAAe,KAAAA;GACR;AAEAC,EAAAA,gBAAkB,EAAA;AAChBhB,IAAAA,IAAM,EAAAa,OAAA;AACN,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAI,EAAAA,MAAQ,EAAA;AACNjB,IAAAA,IAAM,EAAAC,MAAA;IACN,SAAS,EAAA,EAAA;GACX;AAEAiB,EAAAA,UAAY,EAAA;AACVlB,IAAAA,IAAA,EAAM,CAACa,OAAA,EAASV,QAAQ,CAAA;AACxB,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAgB,EAAAA,aAAe,EAAAN,OAAA;AAEfO,EAAAA,IAAM,EAAA;AACJpB,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQE,QAAQ,CAAA;AACzB,GAAA;AACF,CAAA;;;;"}