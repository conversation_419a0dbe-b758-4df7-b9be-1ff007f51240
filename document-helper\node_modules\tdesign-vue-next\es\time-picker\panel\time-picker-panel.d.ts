declare const _default: import("vue").DefineComponent<{
    isFooterDisplay: BooleanConstructor;
    handleConfirmClick: FunctionConstructor;
    onChange: FunctionConstructor;
    disabled: {
        default: boolean;
        type: BooleanConstructor;
        validator(v: boolean): boolean;
    };
    isFocus: {
        default: boolean;
        type: BooleanConstructor;
        validator(v: boolean): boolean;
    };
    value: {
        type: StringConstructor;
        default: string;
    };
    format: {
        type: StringConstructor;
        default: string;
    };
    steps: {
        default: number[];
        type: import("vue").PropType<Array<string | number>>;
    };
    isShowPanel: {
        default: boolean;
        type: BooleanConstructor;
        validator(v: boolean): boolean;
    };
    activeIndex: {
        type: NumberConstructor;
    };
    presets: {
        type: import("vue").PropType<import("..").TdTimePickerProps["presets"] | import("..").TdTimeRangePickerProps["presets"]>;
    };
    hideDisabledTime: {
        type: BooleanConstructor;
        default: boolean;
    };
    disableTime: {
        type: FunctionConstructor;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    isFooterDisplay: BooleanConstructor;
    handleConfirmClick: FunctionConstructor;
    onChange: FunctionConstructor;
    disabled: {
        default: boolean;
        type: BooleanConstructor;
        validator(v: boolean): boolean;
    };
    isFocus: {
        default: boolean;
        type: BooleanConstructor;
        validator(v: boolean): boolean;
    };
    value: {
        type: StringConstructor;
        default: string;
    };
    format: {
        type: StringConstructor;
        default: string;
    };
    steps: {
        default: number[];
        type: import("vue").PropType<Array<string | number>>;
    };
    isShowPanel: {
        default: boolean;
        type: BooleanConstructor;
        validator(v: boolean): boolean;
    };
    activeIndex: {
        type: NumberConstructor;
    };
    presets: {
        type: import("vue").PropType<import("..").TdTimePickerProps["presets"] | import("..").TdTimeRangePickerProps["presets"]>;
    };
    hideDisabledTime: {
        type: BooleanConstructor;
        default: boolean;
    };
    disableTime: {
        type: FunctionConstructor;
    };
}>>, {
    value: string;
    format: string;
    disabled: boolean;
    steps: (string | number)[];
    isFocus: boolean;
    isShowPanel: boolean;
    hideDisabledTime: boolean;
    isFooterDisplay: boolean;
}, {}>;
export default _default;
