{"version": 3, "file": "effect.js", "sources": ["../../../../components/cascader/utils/effect.ts"], "sourcesContent": ["import { isArray, isNumber, cloneDeep, isFunction } from 'lodash-es';\n\nimport type { TreeNode, CascaderContextType, TdCascaderProps, TreeNodeValue, TreeNodeModel } from '../types';\nimport { getFullPathLabel, getTreeValue, isEmptyValues } from './helper';\n\n/**\n * 点击item的副作用\n * @param propsTrigger\n * @param trigger\n * @param node\n * @param cascaderContext\n */\nexport function expendClickEffect(\n  propsTrigger: TdCascaderProps['trigger'],\n  trigger: TdCascaderProps['trigger'],\n  node: TreeNode,\n  cascaderContext: CascaderContextType,\n) {\n  const { checkStrictly, multiple, treeStore, setVisible, setValue, setTreeNodes, setExpend, value, max, valueType } =\n    cascaderContext;\n\n  const isDisabled = node.disabled || (multiple && (value as TreeNodeValue[]).length >= max && max !== 0);\n\n  if (isDisabled) return;\n  // 点击展开节点，设置展开状态\n  if (propsTrigger === trigger) {\n    const expanded = node.setExpanded(true);\n    treeStore.refreshNodes();\n    treeStore.replaceExpanded(expanded);\n    const nodes = treeStore.getNodes().filter((node: TreeNode) => node.visible);\n    setTreeNodes(nodes);\n\n    // 多选条件下手动维护expend\n    if (multiple) {\n      setExpend(expanded);\n    }\n  }\n\n  if (!multiple && (node.isLeaf() || checkStrictly) && trigger === 'click') {\n    treeStore.resetChecked();\n    const checked = node.setChecked(!node.checked);\n    const [value] = checked;\n\n    // 非受控状态下更新状态\n    setValue(valueType === 'single' ? value : node.getPath().map((item) => item.value), 'check', node.getModel());\n\n    // 当 trigger 为 hover 时 ，点击节点一定是关闭 panel 的操作\n    if (!checkStrictly || propsTrigger === 'hover') {\n      setVisible(false, {});\n    }\n  }\n}\n\n/**\n * 多选状态下选中状态数据变化的副作用\n * @param node\n * @param cascaderContext\n * @returns\n */\nexport function valueChangeEffect(node: TreeNode, cascaderContext: CascaderContextType) {\n  const { disabled, max, inputVal, multiple, setVisible, setValue, treeNodes, treeStore, valueType } = cascaderContext;\n\n  if (!node || disabled || node.disabled) {\n    return;\n  }\n  const checked = node.setChecked(!node.isChecked());\n\n  if (isNumber(max) && max < 0) {\n    console.warn('TDesign Warn:', 'max should > 0');\n  }\n\n  if (checked.length > max && isNumber(max) && max > 0) {\n    return;\n  }\n\n  if (checked.length === 0) {\n    const expanded = treeStore.getExpanded();\n    setTimeout(() => {\n      treeStore.replaceExpanded(expanded);\n      treeStore.refreshNodes();\n    }, 0);\n  }\n\n  if (!multiple) {\n    setVisible(false, {});\n  }\n\n  const isSelectAll = treeNodes.every((item) => checked.indexOf(item.value) > -1);\n\n  if (inputVal && isSelectAll) {\n    setVisible(false, {});\n  }\n\n  // 处理不同数据类型\n  const resValue =\n    valueType === 'single'\n      ? checked\n      : checked.map((val) =>\n          treeStore\n            .getNode(val)\n            .getPath()\n            .map((item) => item.value),\n        );\n\n  setValue(resValue, node.checked ? 'uncheck' : 'check', node.getModel());\n}\n\n/**\n * closeIcon点击副作用\n * @param cascaderContext\n */\nexport function closeIconClickEffect(cascaderContext: CascaderContextType) {\n  const { setVisible, multiple, setValue } = cascaderContext;\n\n  setVisible(false, {});\n\n  setValue(multiple ? [] : '', 'clear');\n}\n\n/**\n * tag 关闭按钮点击副作用\n * @param cascaderContext\n */\nexport function handleRemoveTagEffect(\n  cascaderContext: CascaderContextType,\n  index: number,\n  onRemove: TdCascaderProps['onRemove'],\n) {\n  const { disabled, setValue, value, valueType, treeStore } = cascaderContext;\n  if (disabled) return;\n\n  // index equal to undefined means to click clear button\n  if (index !== undefined) {\n    const newValue = cloneDeep(value) as [];\n    const res = newValue.splice(index, 1);\n    const node = treeStore.getNodes(res[0])[0];\n\n    const checked = node.setChecked(!node.isChecked());\n    // 处理不同数据类型\n    const resValue =\n      valueType === 'single'\n        ? checked\n        : checked.map((val) =>\n            treeStore\n              .getNode(val)\n              .getPath()\n              .map((item) => item.value),\n          );\n    setValue(resValue, 'uncheck', node.getModel());\n    if (isFunction(onRemove)) {\n      onRemove({ value: checked, node: node as any });\n    }\n  } else {\n    if (isFunction(onRemove)) {\n      onRemove({ value, node: undefined });\n    }\n  }\n}\n\n/**\n * input和treeStore变化的副作用\n * @param inputVal\n * @param treeStore\n * @param setTreeNodes\n * @returns\n */\nexport const treeNodesEffect = (\n  inputVal: CascaderContextType['inputVal'],\n  treeStore: CascaderContextType['treeStore'],\n  setTreeNodes: CascaderContextType['setTreeNodes'],\n  filter: CascaderContextType['filter'],\n) => {\n  if (!treeStore) return;\n  let nodes = [];\n  if (inputVal) {\n    const filterMethods = (node: TreeNode) => {\n      if (!node.isLeaf()) return;\n      if (isFunction(filter)) {\n        return filter(`${inputVal}`, node as TreeNodeModel & TreeNode);\n      }\n      const fullPathLabel = getFullPathLabel(node, '');\n      return fullPathLabel.indexOf(`${inputVal}`) > -1;\n    };\n\n    nodes = treeStore.nodes.filter(filterMethods);\n  } else {\n    nodes = treeStore.getNodes().filter((node: TreeNode) => node.visible);\n  }\n  setTreeNodes(nodes);\n};\n\n/**\n * 初始化展开阶段与展开状态副作用\n * @param treeStore\n * @param treeValue\n * @param expend\n */\nexport const treeStoreExpendEffect = (\n  treeStore: CascaderContextType['treeStore'],\n  value: CascaderContextType['value'],\n  expend: TreeNodeValue[],\n) => {\n  const treeValue = getTreeValue(value);\n\n  if (!treeStore) return;\n  // init expanded, 无expend状态时设置\n  if (isArray(treeValue) && expend.length === 0) {\n    const expandedMap = new Map();\n    const [val] = treeValue;\n    if (!isEmptyValues(val)) {\n      expandedMap.set(val, true);\n      const node = treeStore.getNode(val);\n      if (!node) {\n        treeStore.refreshNodes();\n        return;\n      }\n      node.getParents().forEach((tn: TreeNode) => {\n        expandedMap.set(tn.value, true);\n      });\n      const expandedArr = Array.from(expandedMap.keys());\n      treeStore.replaceExpanded(expandedArr);\n    }\n  }\n  // 本地维护 expend，更加可控，不需要依赖于 tree 的状态\n  if (treeStore.getExpanded() && expend.length) {\n    treeStore.replaceExpanded(expend);\n  }\n  treeStore.refreshNodes();\n};\n"], "names": ["expendClickEffect", "propsTrigger", "trigger", "node", "cascaderContext", "checkStrictly", "multiple", "treeStore", "setVisible", "setValue", "setTreeNodes", "setExpend", "value", "max", "valueType", "isDisabled", "disabled", "length", "expanded", "setExpanded", "refreshNodes", "replaceExpanded", "nodes", "getNodes", "filter", "visible", "<PERSON><PERSON><PERSON><PERSON>", "resetChecked", "checked", "setChecked", "_checked", "_slicedToArray", "<PERSON><PERSON><PERSON>", "map", "item", "getModel", "valueChangeEffect", "inputVal", "treeNodes", "isChecked", "isNumber", "console", "warn", "getExpanded", "setTimeout", "isSelectAll", "every", "indexOf", "resValue", "val", "getNode", "closeIconClickEffect", "handleRemoveTagEffect", "index", "onRemove", "newValue", "cloneDeep", "res", "splice", "isFunction", "treeNodesEffect", "filterMethods", "concat", "fullPathLabel", "getFullPathLabel", "treeStoreExpendEffect", "expend", "treeValue", "getTreeValue", "isArray", "expandedMap", "Map", "_treeValue", "isEmptyValues", "set", "getParents", "for<PERSON>ach", "tn", "expandedArr", "Array", "from", "keys"], "mappings": ";;;;;;;;;;AAYO,SAASA,iBACdA,CAAAC,YAAA,EACAC,OACA,EAAAC,IAAA,EACAC,eACA,EAAA;AACM,EAAA,IAAEC,aAAe,GACrBD,eAAA,CADMC,aAAe;IAAAC,QAAA,GACrBF,eAAA,CADqBE,QAAA;IAAUC,SAAW,GAC1CH,eAAA,CAD+BG,SAAW;IAAAC,UAAA,GAC1CJ,eAAA,CAD0CI,UAAA;IAAYC,QAAU,GAChEL,eAAA,CADsDK,QAAU;IAAAC,YAAA,GAChEN,eAAA,CADgEM,YAAA;IAAcC,SAAW,GACzFP,eAAA,CAD8EO,SAAW;IAAAC,KAAA,GACzFR,eAAA,CADyFQ,KAAA;IAAOC,GAAK,GACrGT,eAAA,CADgGS,GAAK;IAAAC,SAAA,GACrGV,eAAA,CADqGU,SAAA,CAAA;AAGvG,EAAA,IAAMC,aAAaZ,IAAK,CAAAa,QAAA,IAAaV,YAAaM,KAA0B,CAAAK,MAAA,IAAUJ,OAAOA,GAAQ,KAAA,CAAA,CAAA;AAEjG,EAAA,IAAAE,UAAA,EAAY,OAAA;EAEhB,IAAId,iBAAiBC,OAAS,EAAA;AACtB,IAAA,IAAAgB,QAAA,GAAWf,IAAK,CAAAgB,WAAA,CAAY,IAAI,CAAA,CAAA;IACtCZ,SAAA,CAAUa,YAAa,EAAA,CAAA;AACvBb,IAAAA,SAAA,CAAUc,gBAAgBH,QAAQ,CAAA,CAAA;IAC5B,IAAAI,KAAA,GAAQf,UAAUgB,QAAS,EAAA,CAAEC,OAAO,UAACrB,KAAAA,EAAAA;MAAAA,OAAmBA,MAAKsB,OAAO,CAAA;KAAA,CAAA,CAAA;IAC1Ef,YAAA,CAAaY,KAAK,CAAA,CAAA;AAGlB,IAAA,IAAIhB,QAAU,EAAA;MACZK,SAAA,CAAUO,QAAQ,CAAA,CAAA;AACpB,KAAA;AACF,GAAA;AAEA,EAAA,IAAI,CAACZ,QAAa,KAAAH,IAAA,CAAKuB,QAAY,IAAArB,aAAA,CAAA,IAAkBH,YAAY,OAAS,EAAA;IACxEK,SAAA,CAAUoB,YAAa,EAAA,CAAA;IACvB,IAAMC,OAAU,GAAAzB,IAAA,CAAK0B,UAAW,CAAA,CAAC1B,KAAKyB,OAAO,CAAA,CAAA;AACvC,IAAA,IAAAE,QAAA,GAAAC,cAAA,CAAUH,OAAA,EAAA,CAAA,CAAA;AAAThB,MAAAA,MAAK,GAAAkB,QAAA,CAAA,CAAA,CAAA,CAAA;AAGZrB,IAAAA,QAAA,CAASK,SAAc,KAAA,QAAA,GAAWF,MAAQ,GAAAT,IAAA,CAAK6B,SAAU,CAAAC,GAAA,CAAI,UAACC,IAAA,EAAA;MAAA,OAASA,KAAKtB,KAAK,CAAA;KAAA,CAAA,EAAG,OAAS,EAAAT,IAAA,CAAKgC,UAAU,CAAA,CAAA;AAGxG,IAAA,IAAA,CAAC9B,aAAiB,IAAAJ,YAAA,KAAiB,OAAS,EAAA;AACnCO,MAAAA,UAAA,CAAA,KAAA,EAAO,EAAE,CAAA,CAAA;AACtB,KAAA;AACF,GAAA;AACF,CAAA;AAQgB,SAAA4B,iBAAAA,CAAkBjC,MAAgBC,eAAsC,EAAA;AAChF,EAAA,IAAEY,QAAU,GAAmFZ,eAAA,CAA7FY,QAAU;IAAAH,GAAA,GAAmFT,eAAA,CAAnFS,GAAA;IAAKwB,QAAU,GAAoEjC,eAAA,CAA9EiC,QAAU;IAAA/B,QAAA,GAAoEF,eAAA,CAApEE,QAAA;IAAUE,aAA0DJ,eAAA,CAA1DI;IAAYC,QAAU,GAAoCL,eAAA,CAA9CK,QAAU;IAAA6B,SAAA,GAAoClC,eAAA,CAApCkC,SAAA;IAAW/B,SAAW,GAAcH,eAAA,CAAzBG,SAAW;IAAAO,SAAA,GAAcV,eAAA,CAAdU,SAAA,CAAA;EAEvF,IAAI,CAACX,IAAA,IAAQa,QAAY,IAAAb,IAAA,CAAKa,QAAU,EAAA;AACtC,IAAA,OAAA;AACF,GAAA;AACA,EAAA,IAAMY,UAAUzB,IAAK,CAAA0B,UAAA,CAAW,CAAC1B,IAAA,CAAKoC,WAAW,CAAA,CAAA;EAEjD,IAAIC,QAAS,CAAA3B,GAAG,CAAK,IAAAA,GAAA,GAAM,CAAG,EAAA;AACpB4B,IAAAA,OAAA,CAAAC,IAAA,CAAK,iBAAiB,gBAAgB,CAAA,CAAA;AAChD,GAAA;AAEA,EAAA,IAAId,QAAQX,MAAS,GAAAJ,GAAA,IAAO2B,SAAS3B,GAAG,CAAA,IAAKA,MAAM,CAAG,EAAA;AACpD,IAAA,OAAA;AACF,GAAA;AAEI,EAAA,IAAAe,OAAA,CAAQX,WAAW,CAAG,EAAA;AAClB,IAAA,IAAAC,QAAA,GAAWX,UAAUoC,WAAY,EAAA,CAAA;AACvCC,IAAAA,UAAA,CAAW,YAAM;AACfrC,MAAAA,SAAA,CAAUc,gBAAgBH,QAAQ,CAAA,CAAA;MAClCX,SAAA,CAAUa,YAAa,EAAA,CAAA;OACtB,CAAC,CAAA,CAAA;AACN,GAAA;EAEA,IAAI,CAACd,QAAU,EAAA;AACFE,IAAAA,UAAA,CAAA,KAAA,EAAO,EAAE,CAAA,CAAA;AACtB,GAAA;AAEM,EAAA,IAAAqC,WAAA,GAAcP,SAAU,CAAAQ,KAAA,CAAM,UAACZ,IAAA,EAAA;IAAA,OAASN,QAAQmB,OAAQ,CAAAb,IAAA,CAAKtB,KAAK,CAAA,GAAI,CAAE,CAAA,CAAA;GAAA,CAAA,CAAA;EAE9E,IAAIyB,YAAYQ,WAAa,EAAA;AAChBrC,IAAAA,UAAA,CAAA,KAAA,EAAO,EAAE,CAAA,CAAA;AACtB,GAAA;AAGA,EAAA,IAAMwC,QACJ,GAAAlC,SAAA,KAAc,QACV,GAAAc,OAAA,GACAA,OAAQ,CAAAK,GAAA,CAAI,UAACgB,GAAA,EAAA;AAAA,IAAA,OACX1C,SACG,CAAA2C,OAAA,CAAQD,GAAG,CAAA,CACXjB,OAAQ,EAAA,CACRC,GAAI,CAAA,UAACC,IAAS,EAAA;MAAA,OAAAA,IAAA,CAAKtB,KAAK,CAAA;KAAA,CAAA,CAAA;AAAA,GAC7B,CAAA,CAAA;AAENH,EAAAA,QAAA,CAASuC,UAAU7C,IAAK,CAAAyB,OAAA,GAAU,YAAY,OAAS,EAAAzB,IAAA,CAAKgC,UAAU,CAAA,CAAA;AACxE,CAAA;AAMO,SAASgB,qBAAqB/C,eAAsC,EAAA;AACzE,EAAA,IAAQI,UAAA,GAAmCJ,eAAA,CAAnCI,UAAA;IAAYF,QAAU,GAAaF,eAAA,CAAvBE,QAAU;IAAAG,QAAA,GAAaL,eAAA,CAAbK,QAAA,CAAA;AAEnBD,EAAAA,UAAA,CAAA,KAAA,EAAO,EAAE,CAAA,CAAA;EAEpBC,QAAA,CAASH,QAAW,GAAA,EAAK,GAAA,EAAA,EAAI,OAAO,CAAA,CAAA;AACtC,CAAA;AAMgB,SAAA8C,qBAAAA,CACdhD,eACA,EAAAiD,KAAA,EACAC,QACA,EAAA;AACA,EAAA,IAAQtC,QAAU,GAA0CZ,eAAA,CAApDY,QAAU;IAAAP,QAAA,GAA0CL,eAAA,CAA1CK,QAAA;IAAUG,KAAO,GAAyBR,eAAA,CAAhCQ,KAAO;IAAAE,SAAA,GAAyBV,eAAA,CAAzBU,SAAA;IAAWP,YAAcH,eAAA,CAAdG;AAC1C,EAAA,IAAAS,QAAA,EAAU,OAAA;AAGd,EAAA,IAAIqC,UAAU,KAAW,CAAA,EAAA;AACjB,IAAA,IAAAE,QAAA,GAAWC,UAAU5C,KAAK,CAAA,CAAA;IAChC,IAAM6C,GAAM,GAAAF,QAAA,CAASG,MAAO,CAAAL,KAAA,EAAO,CAAC,CAAA,CAAA;AACpC,IAAA,IAAMlD,IAAO,GAAAI,SAAA,CAAUgB,QAAS,CAAAkC,GAAA,CAAI,EAAE,CAAE,CAAA,CAAA,CAAA,CAAA;AAExC,IAAA,IAAM7B,UAAUzB,IAAK,CAAA0B,UAAA,CAAW,CAAC1B,IAAA,CAAKoC,WAAW,CAAA,CAAA;AAEjD,IAAA,IAAMS,QACJ,GAAAlC,SAAA,KAAc,QACV,GAAAc,OAAA,GACAA,OAAQ,CAAAK,GAAA,CAAI,UAACgB,GAAA,EAAA;AAAA,MAAA,OACX1C,SACG,CAAA2C,OAAA,CAAQD,GAAG,CAAA,CACXjB,OAAQ,EAAA,CACRC,GAAI,CAAA,UAACC,IAAS,EAAA;QAAA,OAAAA,IAAA,CAAKtB,KAAK,CAAA;OAAA,CAAA,CAAA;AAAA,KAC7B,CAAA,CAAA;IACNH,QAAA,CAASuC,QAAU,EAAA,SAAA,EAAW7C,IAAK,CAAAgC,QAAA,EAAU,CAAA,CAAA;AACzC,IAAA,IAAAwB,UAAA,CAAWL,QAAQ,CAAG,EAAA;AACxBA,MAAAA,QAAA,CAAS;AAAE1C,QAAAA,KAAA,EAAOgB,OAAS;AAAAzB,QAAAA,IAAA,EAAAA,IAAAA;AAAkB,OAAC,CAAA,CAAA;AAChD,KAAA;AACF,GAAO,MAAA;AACD,IAAA,IAAAwD,UAAA,CAAWL,QAAQ,CAAG,EAAA;AACxBA,MAAAA,QAAA,CAAS;AAAE1C,QAAAA,KAAA,EAAAA,KAAA;AAAOT,QAAAA,IAAM,EAAA,KAAA,CAAA;AAAU,OAAC,CAAA,CAAA;AACrC,KAAA;AACF,GAAA;AACF,CAAA;AASayD,IAAAA,eAAkB,GAAA,SAAlBA,eAAkBA,CAC7BvB,QACA,EAAA9B,SAAA,EACAG,cACAc,MACG,EAAA;EACH,IAAI,CAACjB,SAAA,EAAW,OAAA;EAChB,IAAIe,QAAQ,EAAC,CAAA;AACb,EAAA,IAAIe,QAAU,EAAA;AACN,IAAA,IAAAwB,aAAA,GAAgB,SAAhBA,aAAAA,CAAiB1D,IAAmB,EAAA;AACpC,MAAA,IAAA,CAACA,KAAKuB,MAAO,EAAA,EAAG,OAAA;AAChB,MAAA,IAAAiC,UAAA,CAAWnC,MAAM,CAAG,EAAA;AACf,QAAA,OAAAA,MAAA,CAAAsC,EAAAA,CAAAA,MAAA,CAAUzB,QAAA,CAAA,EAAYlC,IAAgC,CAAA,CAAA;AAC/D,OAAA;AACM,MAAA,IAAA4D,aAAA,GAAgBC,gBAAiB,CAAA7D,IAAA,EAAM,EAAE,CAAA,CAAA;MAC/C,OAAO4D,aAAc,CAAAhB,OAAA,CAAAe,EAAAA,CAAAA,MAAA,CAAWzB,QAAA,CAAU,CAAI,GAAA,CAAA,CAAA,CAAA;KAChD,CAAA;IAEQf,KAAA,GAAAf,SAAA,CAAUe,KAAM,CAAAE,MAAA,CAAOqC,aAAa,CAAA,CAAA;AAC9C,GAAO,MAAA;IACLvC,KAAA,GAAQf,UAAUgB,QAAS,EAAA,CAAEC,OAAO,UAACrB,IAAA,EAAA;MAAA,OAAmBA,KAAKsB,OAAO,CAAA;KAAA,CAAA,CAAA;AACtE,GAAA;EACAf,YAAA,CAAaY,KAAK,CAAA,CAAA;AACpB,EAAA;AAQO,IAAM2C,qBAAwB,GAAA,SAAxBA,qBAAwBA,CACnC1D,SACA,EAAAK,KAAA,EACAsD,MACG,EAAA;AACG,EAAA,IAAAC,SAAA,GAAYC,aAAaxD,KAAK,CAAA,CAAA;EAEpC,IAAI,CAACL,SAAA,EAAW,OAAA;EAEhB,IAAI8D,OAAQ,CAAAF,SAAS,CAAK,IAAAD,MAAA,CAAOjD,WAAW,CAAG,EAAA;AACvC,IAAA,IAAAqD,WAAA,sBAAkBC,GAAI,EAAA,CAAA;AACtB,IAAA,IAAAC,UAAA,GAAAzC,cAAA,CAAQoC,SAAA,EAAA,CAAA,CAAA;AAAPlB,MAAAA,GAAG,GAAAuB,UAAA,CAAA,CAAA,CAAA,CAAA;AACN,IAAA,IAAA,CAACC,aAAc,CAAAxB,GAAG,CAAG,EAAA;AACXqB,MAAAA,WAAA,CAAAI,GAAA,CAAIzB,KAAK,IAAI,CAAA,CAAA;AACnB,MAAA,IAAA9C,IAAA,GAAOI,SAAU,CAAA2C,OAAA,CAAQD,GAAG,CAAA,CAAA;MAClC,IAAI,CAAC9C,IAAM,EAAA;QACTI,SAAA,CAAUa,YAAa,EAAA,CAAA;AACvB,QAAA,OAAA;AACF,OAAA;MACAjB,IAAA,CAAKwE,UAAW,EAAA,CAAEC,OAAQ,CAAA,UAACC,EAAiB,EAAA;QAC9BP,WAAA,CAAAI,GAAA,CAAIG,EAAG,CAAAjE,KAAA,EAAO,IAAI,CAAA,CAAA;AAChC,OAAC,CAAA,CAAA;MACD,IAAMkE,WAAc,GAAAC,KAAA,CAAMC,IAAK,CAAAV,WAAA,CAAYW,MAAM,CAAA,CAAA;AACjD1E,MAAAA,SAAA,CAAUc,gBAAgByD,WAAW,CAAA,CAAA;AACvC,KAAA;AACF,GAAA;EAEA,IAAIvE,SAAU,CAAAoC,WAAA,EAAiB,IAAAuB,MAAA,CAAOjD,MAAQ,EAAA;AAC5CV,IAAAA,SAAA,CAAUc,gBAAgB6C,MAAM,CAAA,CAAA;AAClC,GAAA;EACA3D,SAAA,CAAUa,YAAa,EAAA,CAAA;AACzB;;;;"}