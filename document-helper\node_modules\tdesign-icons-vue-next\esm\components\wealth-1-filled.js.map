{"version": 3, "file": "wealth-1-filled.js", "sources": ["../../src/components/wealth-1-filled.tsx"], "sourcesContent": ["import { computed, PropType, defineComponent } from 'vue';\nimport renderFn from '../utils/render-fn';\nimport {\n  TdIconSVGProps, SVGJson,\n} from '../utils/types';\nimport useSizeProps from '../utils/use-size-props';\n\nimport '../style/css';\n\nconst element: SVGJson = {\"tag\":\"svg\",\"attrs\":{\"fill\":\"none\",\"viewBox\":\"0 0 24 24\",\"width\":\"1em\",\"height\":\"1em\"},\"children\":[{\"tag\":\"path\",\"attrs\":{\"fill\":\"currentColor\",\"d\":\"M16.5461 8.97827C14.8989 9.63717 12.7604 10 10.5 10 8.23963 10 6.10115 9.63717 4.45392 8.97827 3.94923 8.7764 3.45286 8.53173 3 8.23525V11.5C3 13.1569 6.35786 14.5 10.5 14.5 11.6041 14.5 12.6525 14.4046 13.5964 14.2332 14.6394 13.0355 16.1139 12.2236 17.7778 12.0397 17.813 12.0358 17.8484 12.0322 17.8838 12.0288 17.9602 11.8572 18 11.6804 18 11.5V8.23525C17.5471 8.53173 17.0508 8.7764 16.5461 8.97827zM10.5 16.5C11.1255 16.5 11.7416 16.4722 12.3406 16.4181 12.1197 17.0717 12 17.7719 12 18.5 12 19.3465 12.1618 20.1552 12.4562 20.8969 11.8326 20.9641 11.1768 21 10.5 21 6.35786 21 3 19.6569 3 18V14.7352C3.45286 15.0317 3.94923 15.2764 4.45392 15.4783 6.10115 16.1372 8.23963 16.5 10.5 16.5zM10.5 8C6.35786 8 3 6.65685 3 5 3 3.34315 6.35786 2 10.5 2 13.6066 2 16.2721 2.75552 17.4106 3.83226 17.7901 4.19118 18 4.58579 18 5 18 6.65685 14.6421 8 10.5 8zM18.5 23C16.8151 23 15.3464 22.074 14.5752 20.7031 14.2089 20.0519 14 19.3003 14 18.5 14 16.1837 15.75 14.2762 18 14.0275 18.1642 14.0093 18.331 14 18.5 14 20.9853 14 23 16.0147 23 18.5 23 20.9853 20.9853 23 18.5 23z\"}}]};\n\nexport default defineComponent({\n  name: 'Wealth1FilledIcon',\n  props: {\n    size: {\n      type: String,\n    },\n    onClick: {\n      type: Function as PropType<TdIconSVGProps['onClick']>,\n    },\n  },\n  setup(props, { attrs }) {\n    const propsSize = computed(() => props.size);\n\n    const { className, style } = useSizeProps(propsSize);\n\n    const finalCls = computed(() => ['t-icon', 't-icon-wealth-1-filled', className.value]);\n    const finalStyle = computed(() => ({ ...style.value, ...(attrs.style as Styles) }));\n    const finalProps = computed(() => ({\n      class: finalCls.value,\n      style: finalStyle.value,\n      onClick: (e:MouseEvent) => props.onClick?.({ e }),\n    }));\n    return () => renderFn(element, finalProps.value);\n  },\n\n});\n"], "names": ["element", "defineComponent", "name", "props", "size", "type", "String", "onClick", "Function", "setup", "attrs", "propsSize", "computed", "className", "style", "useSizeProps", "finalCls", "value", "finalStyle", "_objectSpread", "finalProps", "class", "e", "_props$onClick", "call", "renderFn"], "mappings": ";;;;;;;;;;AASA,IAAMA,UAAmB;EAAC,OAAM;EAAM,SAAQ;IAAC,QAAO;IAAO,WAAU;IAAY,SAAQ;IAAM,UAAS;;EAAO,YAAW,CAAC;IAAC,OAAM;IAAO,SAAQ;MAAC,QAAO;MAAe,KAAI;;;;AAE9K,oBAAeC,gBAAgB;EAC7BC,MAAM;EACNC,OAAO;IACLC,MAAM;MACJC,MAAMC;;IAERC,SAAS;MACPF,MAAMG;;;EAGVC,MAAMN,aAAkB;IAAA,IAAX;MAAEO;;QACPC,YAAYC,SAAS,MAAMT,MAAMC;QAEjC;MAAES;MAAWC;QAAUC,aAAaJ;QAEpCK,WAAWJ,SAAS,MAAM,CAAC,UAAU,0BAA0BC,UAAUI;QACzEC,aAAaN,SAAS,MAAAO,aAAA,CAAAA,aAAA,KAAYL,MAAMG,QAAWP,MAAMI;QACzDM,aAAaR,SAAS;MAC1BS,OAAOL,SAASC;MAChBH,OAAOI,WAAWD;MAClBV,SAAUe;;iCAAiBnB,MAAMI,0DAANgB,cAAA,CAAAC,IAAA,CAAArB,OAAgB;UAAEmB;;;;WAExC,MAAMG,SAASzB,SAASoB,WAAWH;;AAAA;;;;"}