{"version": 3, "file": "FailSvg.js", "sources": ["../../../../components/empty/components/FailSvg.tsx"], "sourcesContent": ["import { defineComponent } from 'vue';\n\nexport default defineComponent({\n  name: 'FailSvg',\n  setup() {\n    return () => (\n      <svg width=\"1em\" height=\"1em\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n        <path\n          fill-rule=\"evenodd\"\n          clip-rule=\"evenodd\"\n          d=\"M24 6C14.0589 6 6 14.0589 6 24C6 33.9411 14.0589 42 24 42C33.9411 42 42 33.9411 42 24C42 14.0589 33.9411 6 24 6ZM2 24C2 11.8497 11.8497 2 24 2C36.1503 2 46 11.8497 46 24C46 36.1503 36.1503 46 24 46C11.8497 46 2 36.1503 2 24ZM26 13V28H22V13H26ZM22 31H26.0078V35.0078H22V31Z\"\n          fill=\"#D54941\"\n        />\n      </svg>\n    );\n  },\n});\n"], "names": ["defineComponent", "name", "setup", "_createVNode"], "mappings": ";;;;;;;;AAEA,cAAeA,eAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,SAAA;EACNC,KAAQ,EAAA,SAARA,KAAQA,GAAA;IACC,OAAA,YAAA;AAAA,MAAA,OAAAC,WAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAA,KAAA;AAAA,QAAA,QAAA,EAAA,KAAA;AAAA,QAAA,SAAA,EAAA,WAAA;AAAA,QAAA,MAAA,EAAA,MAAA;AAAA,QAAA,OAAA,EAAA,4BAAA;AAAA,OAAA,EAAA,CAAAA,WAAA,CAAA,MAAA,EAAA;AAAA,QAAA,WAAA,EAAA,SAAA;AAAA,QAAA,WAAA,EAAA,SAAA;AAAA,QAAA,GAAA,EAAA,kRAAA;AAAA,QAAA,MAAA,EAAA,SAAA;AAAA,OAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;KACJ,CAAA;AASL,GAAA;AACF,CAAC,CAAA;;;;"}