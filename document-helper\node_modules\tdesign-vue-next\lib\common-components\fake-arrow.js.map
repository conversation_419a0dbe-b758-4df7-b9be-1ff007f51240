{"version": 3, "file": "fake-arrow.js", "sources": ["../../../components/common-components/fake-arrow.tsx"], "sourcesContent": ["import { defineComponent, PropType, computed, CSSProperties } from 'vue';\nimport { usePrefixClass } from '@tdesign/shared-hooks';\n\n// 统一使用的翻转箭头组件\nexport default defineComponent({\n  name: 'TFakeArrow',\n  props: {\n    // 是否active状态 active状态下箭头向上翻转\n    isActive: {\n      type: Boolean as PropType<boolean>,\n    },\n    overlayClassName: {\n      type: [String, Object, Array],\n    },\n    overlayStyle: {\n      type: Object || (String as PropType<string | CSSProperties>),\n    },\n  },\n\n  setup(props) {\n    const COMPONENT_NAME = usePrefixClass('fake-arrow');\n    const classes = computed(() => [\n      COMPONENT_NAME.value,\n      {\n        [`${COMPONENT_NAME.value}--active`]: props.isActive,\n      },\n      props.overlayClassName,\n    ]);\n\n    return () => (\n      <svg\n        class={classes.value}\n        width=\"16\"\n        height=\"16\"\n        viewBox=\"0 0 16 16\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        style={props.overlayStyle}\n      >\n        <path d=\"M3.75 5.7998L7.99274 10.0425L12.2361 5.79921\" stroke=\"black\" stroke-opacity=\"0.9\" stroke-width=\"1.3\" />\n      </svg>\n    );\n  },\n});\n"], "names": ["defineComponent", "name", "props", "isActive", "type", "Boolean", "overlayClassName", "String", "Object", "Array", "overlayStyle", "setup", "COMPONENT_NAME", "usePrefixClass", "classes", "computed", "value", "_defineProperty", "concat", "_createVNode"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAIA,gBAAeA,eAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,YAAA;AACNC,EAAAA,KAAO,EAAA;AAELC,IAAAA,QAAU,EAAA;AACRC,MAAAA,IAAM,EAAAC,OAAAA;KACR;AACAC,IAAAA,gBAAkB,EAAA;AAChBF,MAAAA,IAAM,EAAA,CAACG,MAAQ,EAAAC,MAAA,EAAQC,KAAK,CAAA;KAC9B;AACAC,IAAAA,YAAc,EAAA;MACZN,MAAMI,MAAW,IAAAD,MAAAA;AACnB,KAAA;GACF;AAEAI,EAAAA,OAAAA,SAAAA,MAAMT,KAAO,EAAA;AACL,IAAA,IAAAU,cAAA,GAAiBC,eAAe,YAAY,CAAA,CAAA;IAC5C,IAAAC,OAAA,GAAUC,SAAS,YAAA;MAAA,OAAM,CAC7BH,cAAe,CAAAI,KAAA,EAAAC,eAAA,CAAA,EAAA,EAAA,EAAA,CAAAC,MAAA,CAETN,cAAe,CAAAI,KAAA,EAAA,UAAA,CAAA,EAAkBd,KAAM,CAAAC,QAAA,GAE7CD,KAAM,CAAAI,gBAAA,CACP,CAAA;KAAA,CAAA,CAAA;IAED,OAAO,YAAA;AAAA,MAAA,OAAAa,WAAA,CAAA,KAAA,EAAA;QAAA,OAEIL,EAAAA,OAAQ,CAAAE,KAAA;AAAA,QAAA,OAAA,EAAA,IAAA;AAAA,QAAA,QAAA,EAAA,IAAA;AAAA,QAAA,SAAA,EAAA,WAAA;AAAA,QAAA,MAAA,EAAA,MAAA;AAAA,QAAA,OAAA,EAAA,4BAAA;AAAA,QAAA,OAAA,EAMRd,KAAM,CAAAQ,YAAAA;AAAA,OAAA,EAAA,CAAAS,WAAA,CAAA,MAAA,EAAA;AAAA,QAAA,GAAA,EAAA,8CAAA;AAAA,QAAA,QAAA,EAAA,OAAA;AAAA,QAAA,gBAAA,EAAA,KAAA;AAAA,QAAA,cAAA,EAAA,KAAA;AAAA,OAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;KAPd,CAAA;AAYL,GAAA;AACF,CAAC,CAAA;;;;"}