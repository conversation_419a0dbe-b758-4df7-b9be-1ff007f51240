import { PropType } from 'vue';
import { EmptyType, SearchOption, TransferValue, TdTransferProps, TransferListType, TransferItemOption } from '../types';
import { CheckboxProps } from '../../checkbox';
declare const _default: import("vue").DefineComponent<{
    checkboxProps: {
        type: PropType<CheckboxProps>;
        default: () => {};
    };
    dataSource: {
        type: PropType<Array<TransferItemOption>>;
        default(): Array<TransferItemOption>;
    };
    listType: {
        type: PropType<TransferListType>;
        default: string;
    };
    title: {
        type: (FunctionConstructor | StringConstructor)[];
    };
    checkedValue: {
        type: PropType<Array<TransferValue>>;
        default(): Array<TransferValue>;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    search: {
        type: PropType<SearchOption>;
        default: boolean;
    };
    transferItem: PropType<TdTransferProps["transferItem"]>;
    empty: {
        type: PropType<EmptyType>;
    };
    pagination: (ObjectConstructor | BooleanConstructor)[];
    footer: (FunctionConstructor | StringConstructor)[];
    checkAll: BooleanConstructor;
    isTreeMode: {
        type: PropType<boolean>;
        default: boolean;
    };
    onCheckedChange: PropType<(event: Array<TransferValue>) => void>;
    onPageChange: FunctionConstructor;
    onScroll: FunctionConstructor;
    onSearch: FunctionConstructor;
    onDataChange: PropType<(data: Array<TransferValue>, movedValue: Array<TransferValue>) => void>;
    draggable: BooleanConstructor;
    currentValue: {
        type: PropType<Array<TransferValue>>;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    checkboxProps: {
        type: PropType<CheckboxProps>;
        default: () => {};
    };
    dataSource: {
        type: PropType<Array<TransferItemOption>>;
        default(): Array<TransferItemOption>;
    };
    listType: {
        type: PropType<TransferListType>;
        default: string;
    };
    title: {
        type: (FunctionConstructor | StringConstructor)[];
    };
    checkedValue: {
        type: PropType<Array<TransferValue>>;
        default(): Array<TransferValue>;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    search: {
        type: PropType<SearchOption>;
        default: boolean;
    };
    transferItem: PropType<TdTransferProps["transferItem"]>;
    empty: {
        type: PropType<EmptyType>;
    };
    pagination: (ObjectConstructor | BooleanConstructor)[];
    footer: (FunctionConstructor | StringConstructor)[];
    checkAll: BooleanConstructor;
    isTreeMode: {
        type: PropType<boolean>;
        default: boolean;
    };
    onCheckedChange: PropType<(event: Array<TransferValue>) => void>;
    onPageChange: FunctionConstructor;
    onScroll: FunctionConstructor;
    onSearch: FunctionConstructor;
    onDataChange: PropType<(data: Array<TransferValue>, movedValue: Array<TransferValue>) => void>;
    draggable: BooleanConstructor;
    currentValue: {
        type: PropType<Array<TransferValue>>;
    };
}>>, {
    search: boolean;
    disabled: boolean;
    draggable: boolean;
    checkAll: boolean;
    isTreeMode: boolean;
    checkboxProps: {};
    dataSource: TransferItemOption[];
    listType: TransferListType;
    checkedValue: TransferValue[];
}, {}>;
export default _default;
