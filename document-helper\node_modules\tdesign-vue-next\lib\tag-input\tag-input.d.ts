import { TdTagInputProps } from './type';
declare const _default: import("vue").DefineComponent<{
    autoWidth: BooleanConstructor;
    borderless: BooleanConstructor;
    clearable: BooleanConstructor;
    collapsedItems: {
        type: import("vue").PropType<TdTagInputProps["collapsedItems"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    dragSort: BooleanConstructor;
    excessTagsDisplayType: {
        type: import("vue").PropType<TdTagInputProps["excessTagsDisplayType"]>;
        default: TdTagInputProps["excessTagsDisplayType"];
        validator(val: TdTagInputProps["excessTagsDisplayType"]): boolean;
    };
    inputProps: {
        type: import("vue").PropType<TdTagInputProps["inputProps"]>;
    };
    inputValue: {
        type: import("vue").PropType<TdTagInputProps["inputValue"]>;
        default: TdTagInputProps["inputValue"];
    };
    defaultInputValue: {
        type: import("vue").PropType<TdTagInputProps["defaultInputValue"]>;
        default: TdTagInputProps["defaultInputValue"];
    };
    label: {
        type: import("vue").PropType<TdTagInputProps["label"]>;
    };
    max: {
        type: NumberConstructor;
    };
    minCollapsedNum: {
        type: NumberConstructor;
        default: number;
    };
    placeholder: {
        type: StringConstructor;
        default: any;
    };
    prefixIcon: {
        type: import("vue").PropType<TdTagInputProps["prefixIcon"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    size: {
        type: import("vue").PropType<TdTagInputProps["size"]>;
        default: TdTagInputProps["size"];
        validator(val: TdTagInputProps["size"]): boolean;
    };
    status: {
        type: import("vue").PropType<TdTagInputProps["status"]>;
        validator(val: TdTagInputProps["status"]): boolean;
    };
    suffix: {
        type: import("vue").PropType<TdTagInputProps["suffix"]>;
    };
    suffixIcon: {
        type: import("vue").PropType<TdTagInputProps["suffixIcon"]>;
    };
    tag: {
        type: import("vue").PropType<TdTagInputProps["tag"]>;
    };
    tagProps: {
        type: import("vue").PropType<TdTagInputProps["tagProps"]>;
    };
    tips: {
        type: import("vue").PropType<TdTagInputProps["tips"]>;
    };
    value: {
        type: import("vue").PropType<TdTagInputProps["value"]>;
        default: TdTagInputProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<TdTagInputProps["value"]>;
        default: TdTagInputProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<TdTagInputProps["defaultValue"]>;
        default: () => TdTagInputProps["defaultValue"];
    };
    valueDisplay: {
        type: import("vue").PropType<TdTagInputProps["valueDisplay"]>;
    };
    onBlur: import("vue").PropType<TdTagInputProps["onBlur"]>;
    onChange: import("vue").PropType<TdTagInputProps["onChange"]>;
    onClear: import("vue").PropType<TdTagInputProps["onClear"]>;
    onClick: import("vue").PropType<TdTagInputProps["onClick"]>;
    onDragSort: import("vue").PropType<TdTagInputProps["onDragSort"]>;
    onEnter: import("vue").PropType<TdTagInputProps["onEnter"]>;
    onFocus: import("vue").PropType<TdTagInputProps["onFocus"]>;
    onInputChange: import("vue").PropType<TdTagInputProps["onInputChange"]>;
    onMouseenter: import("vue").PropType<TdTagInputProps["onMouseenter"]>;
    onMouseleave: import("vue").PropType<TdTagInputProps["onMouseleave"]>;
    onPaste: import("vue").PropType<TdTagInputProps["onPaste"]>;
    onRemove: import("vue").PropType<TdTagInputProps["onRemove"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    autoWidth: BooleanConstructor;
    borderless: BooleanConstructor;
    clearable: BooleanConstructor;
    collapsedItems: {
        type: import("vue").PropType<TdTagInputProps["collapsedItems"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    dragSort: BooleanConstructor;
    excessTagsDisplayType: {
        type: import("vue").PropType<TdTagInputProps["excessTagsDisplayType"]>;
        default: TdTagInputProps["excessTagsDisplayType"];
        validator(val: TdTagInputProps["excessTagsDisplayType"]): boolean;
    };
    inputProps: {
        type: import("vue").PropType<TdTagInputProps["inputProps"]>;
    };
    inputValue: {
        type: import("vue").PropType<TdTagInputProps["inputValue"]>;
        default: TdTagInputProps["inputValue"];
    };
    defaultInputValue: {
        type: import("vue").PropType<TdTagInputProps["defaultInputValue"]>;
        default: TdTagInputProps["defaultInputValue"];
    };
    label: {
        type: import("vue").PropType<TdTagInputProps["label"]>;
    };
    max: {
        type: NumberConstructor;
    };
    minCollapsedNum: {
        type: NumberConstructor;
        default: number;
    };
    placeholder: {
        type: StringConstructor;
        default: any;
    };
    prefixIcon: {
        type: import("vue").PropType<TdTagInputProps["prefixIcon"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    size: {
        type: import("vue").PropType<TdTagInputProps["size"]>;
        default: TdTagInputProps["size"];
        validator(val: TdTagInputProps["size"]): boolean;
    };
    status: {
        type: import("vue").PropType<TdTagInputProps["status"]>;
        validator(val: TdTagInputProps["status"]): boolean;
    };
    suffix: {
        type: import("vue").PropType<TdTagInputProps["suffix"]>;
    };
    suffixIcon: {
        type: import("vue").PropType<TdTagInputProps["suffixIcon"]>;
    };
    tag: {
        type: import("vue").PropType<TdTagInputProps["tag"]>;
    };
    tagProps: {
        type: import("vue").PropType<TdTagInputProps["tagProps"]>;
    };
    tips: {
        type: import("vue").PropType<TdTagInputProps["tips"]>;
    };
    value: {
        type: import("vue").PropType<TdTagInputProps["value"]>;
        default: TdTagInputProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<TdTagInputProps["value"]>;
        default: TdTagInputProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<TdTagInputProps["defaultValue"]>;
        default: () => TdTagInputProps["defaultValue"];
    };
    valueDisplay: {
        type: import("vue").PropType<TdTagInputProps["valueDisplay"]>;
    };
    onBlur: import("vue").PropType<TdTagInputProps["onBlur"]>;
    onChange: import("vue").PropType<TdTagInputProps["onChange"]>;
    onClear: import("vue").PropType<TdTagInputProps["onClear"]>;
    onClick: import("vue").PropType<TdTagInputProps["onClick"]>;
    onDragSort: import("vue").PropType<TdTagInputProps["onDragSort"]>;
    onEnter: import("vue").PropType<TdTagInputProps["onEnter"]>;
    onFocus: import("vue").PropType<TdTagInputProps["onFocus"]>;
    onInputChange: import("vue").PropType<TdTagInputProps["onInputChange"]>;
    onMouseenter: import("vue").PropType<TdTagInputProps["onMouseenter"]>;
    onMouseleave: import("vue").PropType<TdTagInputProps["onMouseleave"]>;
    onPaste: import("vue").PropType<TdTagInputProps["onPaste"]>;
    onRemove: import("vue").PropType<TdTagInputProps["onRemove"]>;
}>>, {
    value: import("./type").TagInputValue;
    disabled: boolean;
    size: import("..").SizeEnum;
    defaultValue: import("./type").TagInputValue;
    placeholder: string;
    modelValue: import("./type").TagInputValue;
    readonly: boolean;
    autoWidth: boolean;
    borderless: boolean;
    clearable: boolean;
    inputValue: string;
    excessTagsDisplayType: "scroll" | "break-line";
    defaultInputValue: string;
    dragSort: boolean;
    minCollapsedNum: number;
}, {}>;
export default _default;
