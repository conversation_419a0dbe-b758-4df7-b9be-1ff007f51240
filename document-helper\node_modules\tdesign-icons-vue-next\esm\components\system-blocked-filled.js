import { _ as _defineProperty } from '../_chunks/dep-931ef437.js';
import { defineComponent, computed } from 'vue';
import renderFn from '../utils/render-fn.js';
import useSizeProps from '../utils/use-size-props.js';
import '../style/css.js';
import '../utils/use-common-classname.js';
import '../utils/config-context.js';

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var element = {
  "tag": "svg",
  "attrs": {
    "fill": "none",
    "viewBox": "0 0 24 24",
    "width": "1em",
    "height": "1em"
  },
  "children": [{
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M23 2H1V18H11.5176C11.5059 17.8348 11.5 17.6681 11.5 17.5 11.5 16.2242 11.8413 15.0281 12.4376 13.998H3V3.99805H21V10.9596C21.734 11.2404 22.4087 11.6411 23 12.1379V2zM3 20H11.9596C12.2404 20.734 12.6411 21.4087 13.1379 22H3V20z"
    }
  }, {
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M18.5 12C15.4624 12 13 14.4624 13 17.5C13 19.0184 13.6167 20.3948 14.6108 21.389C15.605 22.3832 16.9814 23 18.5 23C21.5376 23 24 20.5376 24 17.5C24 15.9816 23.3833 14.6052 22.3892 13.611C21.395 12.6168 20.0186 12 18.5 12ZM15 17.5C15 15.567 16.567 14 18.5 14C19.1032 14 19.6701 14.1521 20.1654 14.4206L15.4205 19.1652C15.152 18.67 15 18.1031 15 17.5ZM16.8346 20.5794L21.5795 15.8348C21.848 16.33 22 16.8969 22 17.5C22 19.433 20.433 21 18.5 21C17.8968 21 17.3299 20.8479 16.8346 20.5794Z"
    }
  }]
};
var systemBlockedFilled = defineComponent({
  name: "SystemBlockedFilledIcon",
  props: {
    size: {
      type: String
    },
    onClick: {
      type: Function
    }
  },
  setup(props, _ref) {
    var {
      attrs
    } = _ref;
    var propsSize = computed(() => props.size);
    var {
      className,
      style
    } = useSizeProps(propsSize);
    var finalCls = computed(() => ["t-icon", "t-icon-system-blocked-filled", className.value]);
    var finalStyle = computed(() => _objectSpread(_objectSpread({}, style.value), attrs.style));
    var finalProps = computed(() => ({
      class: finalCls.value,
      style: finalStyle.value,
      onClick: e => {
        var _props$onClick;
        return (_props$onClick = props.onClick) === null || _props$onClick === void 0 ? void 0 : _props$onClick.call(props, {
          e
        });
      }
    }));
    return () => renderFn(element, finalProps.value);
  }
});

export default systemBlockedFilled;
//# sourceMappingURL=system-blocked-filled.js.map
