'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var defineProperty = require('../_chunks/dep-304d0f8c.js');
var vue = require('vue');
var utils_renderFn = require('../utils/render-fn.js');
var utils_useSizeProps = require('../utils/use-size-props.js');
require('../utils/use-common-classname.js');
require('../utils/config-context.js');

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { defineProperty._defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var element = {
  "tag": "svg",
  "attrs": {
    "fill": "none",
    "viewBox": "0 0 24 24",
    "width": "1em",
    "height": "1em"
  },
  "children": [{
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M3.27924 2H20.7208L21.6921 4.91402C21.8731 5.45691 22.083 6.24588 21.8555 7.06828C21.6913 7.66173 21.3946 8.19867 21 8.6458V20H22V22H2V20H3V8.64575C2.37852 7.94132 2 7.01438 2 6V5.83772L3.27924 2ZM5 9.87377V20H8V13H16V20H19V9.87377C18.6802 9.95615 18.3451 10 18 10C16.8053 10 15.7329 9.47624 15 8.64582C14.2671 9.47624 13.1947 10 12 10C10.8053 10 9.73295 9.47624 9 8.64582C8.26706 9.47624 7.19469 10 6 10C5.65489 10 5.31975 9.95615 5 9.87377ZM10 6C10 7.10457 10.8954 8 12 8C13.1046 8 14 7.10457 14 6V4H10V6ZM8 4H4.72076L4.00526 6.14649C4.04369 6.67948 4.29033 7.15392 4.66691 7.49097C5.02142 7.80826 5.48712 8 6 8C7.10457 8 8 7.10457 8 6V4ZM16 4V6C16 7.10457 16.8954 8 18 8C18.5129 8 18.9786 7.80826 19.3331 7.49097C19.6157 7.23801 19.8249 6.90744 19.9279 6.53505C19.987 6.32154 19.9537 6.02326 19.7947 5.54648L19.2792 4H16ZM14 20V15H10V20H14Z"
    }
  }]
};
var shop5 = vue.defineComponent({
  name: "Shop5Icon",
  props: {
    size: {
      type: String
    },
    onClick: {
      type: Function
    }
  },
  setup(props, _ref) {
    var {
      attrs
    } = _ref;
    var propsSize = vue.computed(() => props.size);
    var {
      className,
      style
    } = utils_useSizeProps['default'](propsSize);
    var finalCls = vue.computed(() => ["t-icon", "t-icon-shop-5", className.value]);
    var finalStyle = vue.computed(() => _objectSpread(_objectSpread({}, style.value), attrs.style));
    var finalProps = vue.computed(() => ({
      class: finalCls.value,
      style: finalStyle.value,
      onClick: e => {
        var _props$onClick;
        return (_props$onClick = props.onClick) === null || _props$onClick === void 0 ? void 0 : _props$onClick.call(props, {
          e
        });
      }
    }));
    return () => utils_renderFn['default'](element, finalProps.value);
  }
});

exports.default = shop5;
//# sourceMappingURL=shop-5.js.map
