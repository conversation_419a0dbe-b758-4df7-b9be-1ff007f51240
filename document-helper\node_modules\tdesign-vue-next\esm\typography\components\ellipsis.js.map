{"version": 3, "file": "ellipsis.js", "sources": ["../../../../components/typography/components/ellipsis.tsx"], "sourcesContent": ["import { defineComponent, computed, ref } from 'vue';\nimport { usePrefixClass, useConfig } from '@tdesign/shared-hooks';\nimport props from '../paragraph-props';\nimport TTooltip from '../../tooltip/index';\n\nimport type { TypographyEllipsis } from '../type';\n\nexport default defineComponent({\n  name: 'TEllipsis',\n  components: { TTooltip },\n  props,\n  setup(props, { slots }) {\n    const COMPONENT_NAME = usePrefixClass('typography');\n    const { globalConfig } = useConfig('typography');\n\n    const content = computed(() => {\n      return props.content || slots?.default();\n    });\n\n    const ellipsisState = computed((): TypographyEllipsis => {\n      const ellipsis = props.ellipsis;\n      return {\n        row: 1,\n        expandable: false,\n        ...(typeof ellipsis === 'object' ? ellipsis : null),\n      };\n    });\n\n    const ellipsisStyles = computed((): any => {\n      const ellipsis = ellipsisState.value;\n      const def = {\n        overflow: props.ellipsis ? 'hidden' : 'visible',\n        textOverflow: props.ellipsis ? 'ellipsis' : 'initial',\n        whiteSpace: props.ellipsis ? 'normal' : 'nowrap',\n        display: '-webkit-box',\n        WebkitLineClamp: ellipsis.row,\n        WebkitBoxOrient: 'vertical',\n      };\n\n      if (isExpand.value) {\n        def.overflow = 'visible';\n        def.whiteSpace = 'normal';\n        def.display = 'initial';\n      }\n      return def;\n    });\n    const isExpand = ref(false);\n\n    const onExpand = () => {\n      isExpand.value = true;\n      if (typeof props.ellipsis === 'object') props.ellipsis.onExpand?.(true);\n    };\n\n    const onCollapse = () => {\n      isExpand.value = false;\n      if (typeof props.ellipsis === 'object') props.ellipsis.onExpand?.(false);\n    };\n\n    const renderEllipsisExpand = () => {\n      const { suffix } = ellipsisState.value;\n\n      const moreNode = (\n        <span\n          class={`${COMPONENT_NAME.value}-ellipsis-symbol`}\n          onClick={onExpand}\n          style=\"text-decoration:none;white-space:nowrap;flex: 1;\"\n        >\n          {suffix || globalConfig.value.expandText}\n        </span>\n      );\n\n      const { tooltipProps, expandable, collapsible } = ellipsisState.value;\n      if (!isExpand.value && expandable) {\n        return tooltipProps && tooltipProps.content ? (\n          <TTooltip {...tooltipProps} content={tooltipProps.content}>\n            {moreNode}\n          </TTooltip>\n        ) : (\n          moreNode\n        );\n      }\n      if (expandable && isExpand.value && collapsible) {\n        return (\n          <span\n            class={`${COMPONENT_NAME.value}-ellipsis-symbol`}\n            onClick={onCollapse}\n            style=\"text-decoration:none;white-space:nowrap;flex: 1;\"\n          >\n            {globalConfig.value.collapseText}\n          </span>\n        );\n      }\n    };\n\n    return () => {\n      const { tooltipProps } = ellipsisState.value;\n      return (\n        <div\n          style={{\n            display: 'flex',\n            alignItems: 'flex-end',\n          }}\n        >\n          {tooltipProps && <TTooltip content={tooltipProps.content} placement=\"top-right\"></TTooltip>}\n          <p style={props.ellipsis ? ellipsisStyles.value : {}}>{content.value}</p>\n          {renderEllipsisExpand()}\n        </div>\n      );\n    };\n  },\n});\n"], "names": ["_isSlot", "s", "Object", "prototype", "toString", "call", "_isVNode", "defineComponent", "name", "components", "TTooltip", "props", "setup", "slots", "_ref", "COMPONENT_NAME", "usePrefixClass", "_useConfig", "useConfig", "globalConfig", "content", "computed", "ellipsisState", "ellipsis", "_objectSpread", "row", "expandable", "_typeof", "ellipsisStyles", "value", "def", "overflow", "textOverflow", "whiteSpace", "display", "WebkitLineClamp", "WebkitBoxOrient", "isExpand", "ref", "onExpand", "_props2$ellipsis$onEx", "_props2$ellipsis", "onCollapse", "_props2$ellipsis$onEx2", "_props2$ellipsis2", "renderEllipsisExpand", "suffix", "moreNode", "_createVNode", "concat", "expandText", "_ellipsisState$value", "tooltipProps", "collapsible", "_mergeProps", "_default", "collapseText", "alignItems"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGqB,SAAAA,QAAAC,CAAA,EAAA;AAAA,EAAA,OAAA,OAAAA,CAAA,KAAA,UAAA,IAAAC,MAAA,CAAAC,SAAA,CAAAC,QAAA,CAAAC,IAAA,CAAAJ,CAAA,CAAAK,KAAAA,iBAAAA,IAAAA,CAAAA,OAAA,CAAAL,CAAA,CAAA,CAAA;AAAA,CAAA;AAIrB,eAAeM,eAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,WAAA;AACNC,EAAAA,UAAA,EAAY;AAAEC,IAAAA,QAAS,EAATA,OAAAA;GAAS;AACvBC,EAAAA,KAAA,EAAAA,KAAA;AACAC,EAAAA,KAAMD,WAANC,KAAMD,CAAAA,MAAAA,EAAAA,IAAAA,EAAkB;AAAA,IAAA,IAATE,KAAA,GAAAC,IAAA,CAAAD,KAAA,CAAA;AACP,IAAA,IAAAE,cAAA,GAAiBC,eAAe,YAAY,CAAA,CAAA;AAClD,IAAA,IAAAC,UAAA,GAAyBC,SAAA,CAAU,YAAY,CAAA;MAAvCC,YAAA,GAAAF,UAAA,CAAAE,YAAA,CAAA;AAEF,IAAA,IAAAC,OAAA,GAAUC,SAAS,YAAM;MACtBV,OAAAA,MAAAA,CAAMS,OAAW,KAAAP,KAAA,KAAAA,IAAAA,IAAAA,KAAA,KAAAA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAA,CAAe,SAAA,CAAA,EAAA,CAAA,CAAA;AACzC,KAAC,CAAA,CAAA;AAEK,IAAA,IAAAS,aAAA,GAAgBD,SAAS,YAA0B;AACvD,MAAA,IAAME,WAAWZ,MAAM,CAAAY,QAAA,CAAA;AAChB,MAAA,OAAAC,aAAA,CAAA;AACLC,QAAAA,GAAK,EAAA,CAAA;AACLC,QAAAA,UAAY,EAAA,KAAA;OACRC,EAAAA,OAAA,CAAOJ,QAAa,CAAA,KAAA,QAAA,GAAWA,QAAW,GAAA,IAAA,CAAA,CAAA;AAElD,KAAC,CAAA,CAAA;AAEK,IAAA,IAAAK,cAAA,GAAiBP,SAAS,YAAW;AACzC,MAAA,IAAME,WAAWD,aAAc,CAAAO,KAAA,CAAA;AAC/B,MAAA,IAAMC,GAAM,GAAA;AACVC,QAAAA,QAAA,EAAUpB,MAAM,CAAAY,QAAA,GAAW,QAAW,GAAA,SAAA;AACtCS,QAAAA,YAAA,EAAcrB,MAAM,CAAAY,QAAA,GAAW,UAAa,GAAA,SAAA;AAC5CU,QAAAA,UAAA,EAAYtB,MAAM,CAAAY,QAAA,GAAW,QAAW,GAAA,QAAA;AACxCW,QAAAA,OAAS,EAAA,aAAA;QACTC,iBAAiBZ,QAAS,CAAAE,GAAA;AAC1BW,QAAAA,eAAiB,EAAA,UAAA;OACnB,CAAA;MAEA,IAAIC,SAASR,KAAO,EAAA;QAClBC,GAAA,CAAIC,QAAW,GAAA,SAAA,CAAA;QACfD,GAAA,CAAIG,UAAa,GAAA,QAAA,CAAA;QACjBH,GAAA,CAAII,OAAU,GAAA,SAAA,CAAA;AAChB,OAAA;AACO,MAAA,OAAAJ,GAAA,CAAA;AACT,KAAC,CAAA,CAAA;AACK,IAAA,IAAAO,QAAA,GAAWC,IAAI,KAAK,CAAA,CAAA;AAE1B,IAAA,IAAMC,WAAW,SAAXA,WAAiB;MAAA,IAAAC,qBAAA,EAAAC,gBAAA,CAAA;MACrBJ,QAAA,CAASR,KAAQ,GAAA,IAAA,CAAA;AACb,MAAA,IAAAF,OAAA,CAAOhB,OAAMY,QAAa,CAAA,KAAA,QAAA,EAAUZ,CAAAA,qBAAAA,GAAAA,CAAAA,gBAAAA,GAAAA,MAAAA,CAAMY,QAAS,EAAAgB,QAAA,MAAAC,IAAAA,IAAAA,qBAAA,KAAf7B,KAAAA,CAAAA,IAAAA,qBAAAA,CAAAA,IAAAA,CAAAA,gBAAAA,EAA0B,IAAI,CAAA,CAAA;KACxE,CAAA;AAEA,IAAA,IAAM+B,aAAa,SAAbA,aAAmB;MAAA,IAAAC,sBAAA,EAAAC,iBAAA,CAAA;MACvBP,QAAA,CAASR,KAAQ,GAAA,KAAA,CAAA;AACb,MAAA,IAAAF,OAAA,CAAOhB,OAAMY,QAAa,CAAA,KAAA,QAAA,EAAUZ,CAAAA,sBAAAA,GAAAA,CAAAA,iBAAAA,GAAAA,MAAAA,CAAMY,QAAS,EAAAgB,QAAA,MAAAI,IAAAA,IAAAA,sBAAA,KAAfhC,KAAAA,CAAAA,IAAAA,sBAAAA,CAAAA,IAAAA,CAAAA,iBAAAA,EAA0B,KAAK,CAAA,CAAA;KACzE,CAAA;AAEA,IAAA,IAAMkC,uBAAuB,SAAvBA,uBAA6B;AAC3B,MAAA,IAAEC,MAAO,GAAIxB,aAAc,CAAAO,KAAA,CAAzBiB,MAAO,CAAA;MAEf,IAAMC,QACJ,GAAAC,WAAA,CAAA,MAAA,EAAA;AAAA,QAAA,OAAA,EAAA,EAAA,CAAAC,MAAA,CACYlC,cAAe,CAAAc,KAAA,EAAA,kBAAA,CAAA;AAAA,QAAA,SAAA,EAChBU,QAAA;AAAA,QAAA,OAAA,EAAA,kDAAA;AAAA,OAAA,EAAA,CAGRO,MAAA,IAAU3B,YAAa,CAAAU,KAAA,CAAMqB,WAL/B,CAAA,CAAA;AASH,MAAA,IAAAC,oBAAA,GAAkD7B,aAAc,CAAAO,KAAA;QAAxDuB,YAAA,GAAAD,oBAAA,CAAAC,YAAA;QAAc1B,UAAY,GAAAyB,oBAAA,CAAZzB,UAAY;QAAA2B,WAAA,GAAAF,oBAAA,CAAAE,WAAA,CAAA;AAC9B,MAAA,IAAA,CAAChB,QAAS,CAAAR,KAAA,IAASH,UAAY,EAAA;AAC1B,QAAA,OAAA0B,YAAA,IAAgBA,YAAa,CAAAhC,OAAA,GAAA4B,WAAA,CAAAtC,OAAA,EAAA4C,UAAA,CACpBF,YAAc,EAAA;AAAA,UAAA,SAAA,EAASA,YAAa,CAAAhC,OAAAA;AAAA,SAAA,CAAA,EAAApB,OAAA,CAC/C+C,QACH,CAAA,GADGA,QACH,GAAA;AAAA,UAAA,SAAA,EAAA,SAAAQ,QAAA,GAAA;AAAA,YAAA,OAAA,CADGR,QACH,CAAA,CAAA;AAAA,WAAA;AAAA,SAAA,CAAA,GAEAA,QAAA,CAAA;AAEJ,OAAA;AACI,MAAA,IAAArB,UAAA,IAAcW,QAAS,CAAAR,KAAA,IAASwB,WAAa,EAAA;AAC/C,QAAA,OAAAL,WAAA,CAAA,MAAA,EAAA;AAAA,UAAA,OAAA,EAAA,EAAA,CAAAC,MAAA,CAEclC,cAAe,CAAAc,KAAA,EAAA,kBAAA,CAAA;AAAA,UAAA,SAAA,EAChBa,UAAA;AAAA,UAAA,OAAA,EAAA,kDAAA;AAAA,SAAA,EAAA,CAGRvB,YAAa,CAAAU,KAAA,CAAM2B;AAG1B,OAAA;KACF,CAAA;AAEA,IAAA,OAAO,YAAM;AACL,MAAA,IAAEJ,YAAa,GAAI9B,aAAc,CAAAO,KAAA,CAA/BuB,YAAa,CAAA;AAEnB,MAAA,OAAAJ,WAAA,CAAA,KAAA,EAAA;QAAA,OACS,EAAA;AACLd,UAAAA,OAAS,EAAA,MAAA;AACTuB,UAAAA,UAAY,EAAA,UAAA;AACd,SAAA;AAAA,OAAA,EAAA,CAECL;iBAAmCA,EAAAA,YAAa,CAAAhC,OAAA;AAAA,QAAA,WAAA,EAAA,WAAA;AAAA,OAAA,EAAA,IAAA,CAA+B,EAAA4B,WAAA,CAAA,GAAA,EAAA;QAAA,OACtErC,EAAAA,MAAM,CAAAY,QAAA,GAAWK,cAAe,CAAAC,KAAA,GAAQ,EAAC;AAAA,OAAA,EAAA,CAAIT,OAAQ,CAAAS,KAAA,CAC9DgB,CAAAA,EAAAA,oBAAqB,EAAA,CAAA,CAAA,CAAA;KAG5B,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}