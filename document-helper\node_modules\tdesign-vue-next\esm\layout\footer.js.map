{"version": 3, "file": "footer.js", "sources": ["../../../components/layout/footer.tsx"], "sourcesContent": ["import { defineComponent } from 'vue';\nimport props from './footer-props';\n\nimport { useTNodeJSX, usePrefixClass } from '@tdesign/shared-hooks';\n\nexport default defineComponent({\n  name: 'TFooter',\n  props,\n  setup(props) {\n    const COMPONENT_NAME = usePrefixClass('layout__footer');\n    const renderTNodeJSX = useTNodeJSX();\n\n    return () => (\n      <footer class={COMPONENT_NAME.value} style={props.height ? { height: props.height } : {}}>\n        {renderTNodeJSX('default')}\n      </footer>\n    );\n  },\n});\n"], "names": ["defineComponent", "name", "props", "setup", "COMPONENT_NAME", "usePrefixClass", "renderTNodeJSX", "useTNodeJSX", "_createVNode", "value", "height"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,cAAeA,eAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,SAAA;AACNC,EAAAA,KAAA,EAAAA,KAAA;AACAC,EAAAA,OAAAA,SAAAA,MAAMD,MAAO,EAAA;AACL,IAAA,IAAAE,cAAA,GAAiBC,eAAe,gBAAgB,CAAA,CAAA;AACtD,IAAA,IAAMC,iBAAiBC,WAAY,EAAA,CAAA;IAEnC,OAAO,YAAA;AAAA,MAAA,OAAAC,WAAA,CAAA,QAAA,EAAA;QAAA,OACUJ,EAAAA,cAAe,CAAAK,KAAA;QAAA,OAAcP,EAAAA,MAAM,CAAAQ,MAAA,GAAS;UAAEA,MAAQR,EAAAA,MAAAA,CAAMQ,MAAAA;AAAO,SAAI,GAAA,EAAC;OACpFJ,EAAAA,CAAAA,cAAe,CAAA,SAAS;KAD1B,CAAA;AAIL,GAAA;AACF,CAAC,CAAA;;;;"}