import { PropType } from 'vue';
import type { TdDatePickerProps } from '../../type';
declare const _default: import("vue").DefineComponent<{
    mode: {
        type: PropType<TdDatePickerProps["mode"]>;
        default: string;
    };
    value: (ArrayConstructor | StringConstructor | NumberConstructor | DateConstructor)[];
    format: StringConstructor;
    firstDayOfWeek: NumberConstructor;
    multiple: BooleanConstructor;
    data: ArrayConstructor;
    time: StringConstructor;
    onCellClick: FunctionConstructor;
    onCellMouseEnter: FunctionConstructor;
    onCellMouseLeave: FunctionConstructor;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    mode: {
        type: PropType<TdDatePickerProps["mode"]>;
        default: string;
    };
    value: (ArrayConstructor | StringConstructor | NumberConstructor | DateConstructor)[];
    format: StringConstructor;
    firstDayOfWeek: NumberConstructor;
    multiple: BooleanConstructor;
    data: ArrayConstructor;
    time: StringConstructor;
    onCellClick: FunctionConstructor;
    onCellMouseEnter: FunctionConstructor;
    onCellMouseLeave: FunctionConstructor;
}>>, {
    mode: "date" | "month" | "year" | "quarter" | "week";
    multiple: boolean;
}, {}>;
export default _default;
