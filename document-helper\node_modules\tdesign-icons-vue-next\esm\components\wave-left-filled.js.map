{"version": 3, "file": "wave-left-filled.js", "sources": ["../../src/components/wave-left-filled.tsx"], "sourcesContent": ["import { computed, PropType, defineComponent } from 'vue';\nimport renderFn from '../utils/render-fn';\nimport {\n  TdIconSVGProps, SVGJson,\n} from '../utils/types';\nimport useSizeProps from '../utils/use-size-props';\n\nimport '../style/css';\n\nconst element: SVGJson = {\"tag\":\"svg\",\"attrs\":{\"fill\":\"none\",\"viewBox\":\"0 0 24 24\",\"width\":\"1em\",\"height\":\"1em\"},\"children\":[{\"tag\":\"path\",\"attrs\":{\"fill\":\"currentColor\",\"d\":\"M7.07996 3.01688L6.25658 3.58437C5.25048 4.27779 4.37742 5.15081 3.68397 6.15688L3.11645 6.98024 1.46973 5.8452 2.03725 5.02184C2.86875 3.81549 3.91522 2.76906 5.1216 1.9376L5.94498 1.37012 7.07996 3.01688zM10.2448 4.46556C10.7819 3.92867 11.6529 3.92995 12.1885 4.4684L17.4423 9.75065 16.9979 8.65493C16.4384 7.27525 18.0035 6.00498 19.2385 6.83649 19.4269 6.96332 19.5842 7.13103 19.6987 7.32712L22.0783 11.4019C23.7259 14.2233 23.2639 17.8009 20.9536 20.1111L20.1093 20.9554C17.3017 23.7631 12.7495 23.7631 9.94188 20.9554L4.90742 15.9198C4.37073 15.383 4.37056 14.5128 4.90705 13.9758 5.44395 13.4384 6.31495 13.4382 6.85213 13.9753L8.45572 15.5789 8.98679 15.0478 5.29669 11.3577C4.75982 10.8209 4.75981 9.95045 5.29666 9.41357 5.83353 8.87668 6.70399 8.87667 7.24088 9.41355L10.931 13.1037 11.4613 12.5733 6.40446 7.51647C5.86764 6.97965 5.86747 6.10934 6.40408 5.57231 6.94099 5.03498 7.81188 5.0348 8.349 5.57192L13.4059 10.6288 13.9361 10.0986 10.2446 6.4071C9.70839 5.87093 9.7085 5.0016 10.2448 4.46556z\"}}]};\n\nexport default defineComponent({\n  name: 'WaveLeftFilledIcon',\n  props: {\n    size: {\n      type: String,\n    },\n    onClick: {\n      type: Function as PropType<TdIconSVGProps['onClick']>,\n    },\n  },\n  setup(props, { attrs }) {\n    const propsSize = computed(() => props.size);\n\n    const { className, style } = useSizeProps(propsSize);\n\n    const finalCls = computed(() => ['t-icon', 't-icon-wave-left-filled', className.value]);\n    const finalStyle = computed(() => ({ ...style.value, ...(attrs.style as Styles) }));\n    const finalProps = computed(() => ({\n      class: finalCls.value,\n      style: finalStyle.value,\n      onClick: (e:MouseEvent) => props.onClick?.({ e }),\n    }));\n    return () => renderFn(element, finalProps.value);\n  },\n\n});\n"], "names": ["element", "defineComponent", "name", "props", "size", "type", "String", "onClick", "Function", "setup", "attrs", "propsSize", "computed", "className", "style", "useSizeProps", "finalCls", "value", "finalStyle", "_objectSpread", "finalProps", "class", "e", "_props$onClick", "call", "renderFn"], "mappings": ";;;;;;;;;;AASA,IAAMA,UAAmB;EAAC,OAAM;EAAM,SAAQ;IAAC,QAAO;IAAO,WAAU;IAAY,SAAQ;IAAM,UAAS;;EAAO,YAAW,CAAC;IAAC,OAAM;IAAO,SAAQ;MAAC,QAAO;MAAe,KAAI;;;;AAE9K,qBAAeC,gBAAgB;EAC7BC,MAAM;EACNC,OAAO;IACLC,MAAM;MACJC,MAAMC;;IAERC,SAAS;MACPF,MAAMG;;;EAGVC,MAAMN,aAAkB;IAAA,IAAX;MAAEO;;QACPC,YAAYC,SAAS,MAAMT,MAAMC;QAEjC;MAAES;MAAWC;QAAUC,aAAaJ;QAEpCK,WAAWJ,SAAS,MAAM,CAAC,UAAU,2BAA2BC,UAAUI;QAC1EC,aAAaN,SAAS,MAAAO,aAAA,CAAAA,aAAA,KAAYL,MAAMG,QAAWP,MAAMI;QACzDM,aAAaR,SAAS;MAC1BS,OAAOL,SAASC;MAChBH,OAAOI,WAAWD;MAClBV,SAAUe;;iCAAiBnB,MAAMI,0DAANgB,cAAA,CAAAC,IAAA,CAAArB,OAAgB;UAAEmB;;;;WAExC,MAAMG,SAASzB,SAASoB,WAAWH;;AAAA;;;;"}