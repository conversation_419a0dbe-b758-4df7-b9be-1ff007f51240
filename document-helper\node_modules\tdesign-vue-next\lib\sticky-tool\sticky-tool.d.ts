import type { TdStickyItemProps } from './type';
declare const _default: import("vue").DefineComponent<{
    list: {
        type: import("vue").PropType<import("./type").TdStickyToolProps["list"]>;
        default: () => import("./type").TdStickyToolProps["list"];
    };
    offset: {
        type: import("vue").PropType<import("./type").TdStickyToolProps["offset"]>;
    };
    placement: {
        type: import("vue").PropType<import("./type").TdStickyToolProps["placement"]>;
        default: import("./type").TdStickyToolProps["placement"];
        validator(val: import("./type").TdStickyToolProps["placement"]): boolean;
    };
    popupProps: {
        type: import("vue").PropType<import("./type").TdStickyToolProps["popupProps"]>;
    };
    shape: {
        type: import("vue").PropType<import("./type").TdStickyToolProps["shape"]>;
        default: import("./type").TdStickyToolProps["shape"];
        validator(val: import("./type").TdStickyToolProps["shape"]): boolean;
    };
    type: {
        type: import("vue").PropType<import("./type").TdStickyToolProps["type"]>;
        default: import("./type").TdStickyToolProps["type"];
        validator(val: import("./type").TdStickyToolProps["type"]): boolean;
    };
    width: {
        type: import("vue").PropType<import("./type").TdStickyToolProps["width"]>;
    };
    onClick: import("vue").PropType<import("./type").TdStickyToolProps["onClick"]>;
    onHover: import("vue").PropType<import("./type").TdStickyToolProps["onHover"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    list: {
        type: import("vue").PropType<import("./type").TdStickyToolProps["list"]>;
        default: () => import("./type").TdStickyToolProps["list"];
    };
    offset: {
        type: import("vue").PropType<import("./type").TdStickyToolProps["offset"]>;
    };
    placement: {
        type: import("vue").PropType<import("./type").TdStickyToolProps["placement"]>;
        default: import("./type").TdStickyToolProps["placement"];
        validator(val: import("./type").TdStickyToolProps["placement"]): boolean;
    };
    popupProps: {
        type: import("vue").PropType<import("./type").TdStickyToolProps["popupProps"]>;
    };
    shape: {
        type: import("vue").PropType<import("./type").TdStickyToolProps["shape"]>;
        default: import("./type").TdStickyToolProps["shape"];
        validator(val: import("./type").TdStickyToolProps["shape"]): boolean;
    };
    type: {
        type: import("vue").PropType<import("./type").TdStickyToolProps["type"]>;
        default: import("./type").TdStickyToolProps["type"];
        validator(val: import("./type").TdStickyToolProps["type"]): boolean;
    };
    width: {
        type: import("vue").PropType<import("./type").TdStickyToolProps["width"]>;
    };
    onClick: import("vue").PropType<import("./type").TdStickyToolProps["onClick"]>;
    onHover: import("vue").PropType<import("./type").TdStickyToolProps["onHover"]>;
}>>, {
    type: "normal" | "compact";
    list: TdStickyItemProps[];
    shape: "round" | "square";
    placement: "left-top" | "left-bottom" | "right-top" | "right-bottom" | "right-center" | "left-center";
}, {}>;
export default _default;
