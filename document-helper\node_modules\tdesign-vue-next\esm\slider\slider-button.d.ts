import { PropType } from 'vue';
import { TdSliderProps } from './type';
declare const _default: import("vue").DefineComponent<{
    value: {
        type: NumberConstructor[];
        default: number;
    };
    vertical: {
        type: BooleanConstructor;
        default: boolean;
    };
    tooltipProps: {
        type: (ObjectConstructor | BooleanConstructor)[];
        default: boolean;
    };
    label: {
        type: PropType<TdSliderProps["label"]>;
    };
    range: {
        type: BooleanConstructor;
        default: boolean;
    };
    position: {
        type: StringConstructor;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("input" | "mouseup")[], "input" | "mouseup", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: NumberConstructor[];
        default: number;
    };
    vertical: {
        type: BooleanConstructor;
        default: boolean;
    };
    tooltipProps: {
        type: (ObjectConstructor | BooleanConstructor)[];
        default: boolean;
    };
    label: {
        type: PropType<TdSliderProps["label"]>;
    };
    range: {
        type: BooleanConstructor;
        default: boolean;
    };
    position: {
        type: StringConstructor;
    };
}>> & {
    onInput?: (...args: any[]) => any;
    onMouseup?: (...args: any[]) => any;
}, {
    value: number;
    vertical: boolean;
    range: boolean;
    tooltipProps: boolean | Record<string, any>;
}, {}>;
export default _default;
