import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<{
    leftDisabled: {
        type: PropType<boolean>;
        required: true;
    };
    rightDisabled: {
        type: PropType<boolean>;
        required: true;
    };
    operation: {
        type: PropType<import("../type").TdTransferProps["operation"]>;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("moveToRight" | "moveToLeft")[], "moveToRight" | "moveToLeft", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    leftDisabled: {
        type: PropType<boolean>;
        required: true;
    };
    rightDisabled: {
        type: PropType<boolean>;
        required: true;
    };
    operation: {
        type: PropType<import("../type").TdTransferProps["operation"]>;
    };
}>> & {
    onMoveToRight?: (...args: any[]) => any;
    onMoveToLeft?: (...args: any[]) => any;
}, {}, {}>;
export default _default;
