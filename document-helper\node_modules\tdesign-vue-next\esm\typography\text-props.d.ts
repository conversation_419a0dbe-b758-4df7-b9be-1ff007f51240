import { TdTextProps } from '../typography/type';
import { PropType } from 'vue';
declare const _default: {
    code: BooleanConstructor;
    content: {
        type: PropType<TdTextProps["content"]>;
    };
    copyable: {
        type: PropType<TdTextProps["copyable"]>;
        default: TdTextProps["copyable"];
    };
    default: {
        type: PropType<TdTextProps["default"]>;
    };
    delete: BooleanConstructor;
    disabled: BooleanConstructor;
    ellipsis: {
        type: PropType<TdTextProps["ellipsis"]>;
        default: TdTextProps["ellipsis"];
    };
    italic: BooleanConstructor;
    keyboard: BooleanConstructor;
    mark: {
        type: PropType<TdTextProps["mark"]>;
        default: TdTextProps["mark"];
    };
    strong: BooleanConstructor;
    theme: {
        type: PropType<TdTextProps["theme"]>;
        validator(val: TdTextProps["theme"]): boolean;
    };
    underline: BooleanConstructor;
};
export default _default;
