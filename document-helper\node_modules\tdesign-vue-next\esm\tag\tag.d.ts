declare const _default: import("vue").DefineComponent<{
    closable: BooleanConstructor;
    color: {
        type: StringConstructor;
        default: string;
    };
    content: {
        type: import("vue").PropType<import("./type").TdTagProps["content"]>;
    };
    default: {
        type: import("vue").PropType<import("./type").TdTagProps["default"]>;
    };
    disabled: BooleanConstructor;
    icon: {
        type: import("vue").PropType<import("./type").TdTagProps["icon"]>;
        default: any;
    };
    maxWidth: {
        type: import("vue").PropType<import("./type").TdTagProps["maxWidth"]>;
    };
    shape: {
        type: import("vue").PropType<import("./type").TdTagProps["shape"]>;
        default: import("./type").TdTagProps["shape"];
        validator(val: import("./type").TdTagProps["shape"]): boolean;
    };
    size: {
        type: import("vue").PropType<import("./type").TdTagProps["size"]>;
        default: import("./type").TdTagProps["size"];
        validator(val: import("./type").TdTagProps["size"]): boolean;
    };
    theme: {
        type: import("vue").PropType<import("./type").TdTagProps["theme"]>;
        default: import("./type").TdTagProps["theme"];
        validator(val: import("./type").TdTagProps["theme"]): boolean;
    };
    title: {
        type: StringConstructor;
        default: string;
    };
    variant: {
        type: import("vue").PropType<import("./type").TdTagProps["variant"]>;
        default: import("./type").TdTagProps["variant"];
        validator(val: import("./type").TdTagProps["variant"]): boolean;
    };
    onClick: import("vue").PropType<import("./type").TdTagProps["onClick"]>;
    onClose: import("vue").PropType<import("./type").TdTagProps["onClose"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    closable: BooleanConstructor;
    color: {
        type: StringConstructor;
        default: string;
    };
    content: {
        type: import("vue").PropType<import("./type").TdTagProps["content"]>;
    };
    default: {
        type: import("vue").PropType<import("./type").TdTagProps["default"]>;
    };
    disabled: BooleanConstructor;
    icon: {
        type: import("vue").PropType<import("./type").TdTagProps["icon"]>;
        default: any;
    };
    maxWidth: {
        type: import("vue").PropType<import("./type").TdTagProps["maxWidth"]>;
    };
    shape: {
        type: import("vue").PropType<import("./type").TdTagProps["shape"]>;
        default: import("./type").TdTagProps["shape"];
        validator(val: import("./type").TdTagProps["shape"]): boolean;
    };
    size: {
        type: import("vue").PropType<import("./type").TdTagProps["size"]>;
        default: import("./type").TdTagProps["size"];
        validator(val: import("./type").TdTagProps["size"]): boolean;
    };
    theme: {
        type: import("vue").PropType<import("./type").TdTagProps["theme"]>;
        default: import("./type").TdTagProps["theme"];
        validator(val: import("./type").TdTagProps["theme"]): boolean;
    };
    title: {
        type: StringConstructor;
        default: string;
    };
    variant: {
        type: import("vue").PropType<import("./type").TdTagProps["variant"]>;
        default: import("./type").TdTagProps["variant"];
        validator(val: import("./type").TdTagProps["variant"]): boolean;
    };
    onClick: import("vue").PropType<import("./type").TdTagProps["onClick"]>;
    onClose: import("vue").PropType<import("./type").TdTagProps["onClose"]>;
}>>, {
    disabled: boolean;
    color: string;
    size: import("..").SizeEnum;
    title: string;
    theme: "default" | "primary" | "success" | "warning" | "danger";
    icon: (h: typeof import("vue").h) => import("..").TNodeReturnValue;
    variant: "outline" | "dark" | "light" | "light-outline";
    shape: "mark" | "round" | "square";
    closable: boolean;
}, {}>;
export default _default;
