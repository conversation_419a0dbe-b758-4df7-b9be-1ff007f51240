# 文档编写辅助工具

一个基于 Vue 3 + TDesign 的现代化文档编写辅助工具，提供直观的目录管理和内容编辑功能。

## 功能特性

### 🗂️ 智能目录管理
- **可视化目录树**：左侧面板显示文档结构，支持多级嵌套
- **内容预览**：目录项下方显示章节内容摘要，一目了然
- **双重编辑模式**：
  - 快速编辑：双击标题直接修改
  - 详细编辑：完整的标题和描述编辑
- **智能操作**：悬停显示操作按钮，支持增删改查
- **目录生成**：一键生成文档大纲（待集成AI功能）

### ✏️ 强大的内容编辑
- **多种编辑模式**：
  - 编辑模式：专注写作的纯文本编辑器
  - 预览模式：实时查看Markdown渲染效果
  - 分屏模式：同时编辑和预览
- **Markdown支持**：支持标准Markdown语法
- **实时保存**：内容自动保存，防止数据丢失

### 🎨 现代化设计
- **TDesign设计语言**：采用腾讯TDesign组件库
- **响应式布局**：适配不同屏幕尺寸
- **主题定制**：支持浅色主题，界面简洁美观

## 快速开始

### 环境要求
- Node.js 16.0 或更高版本
- npm 或 yarn 包管理器

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

应用将在 `http://localhost:5173/` 启动

### 构建生产版本
```bash
npm run build
```

## 使用指南

### 1. 目录管理
- **添加根目录**：点击左侧底部的"添加根目录"按钮
- **添加子目录**：选中目录项后，点击"+"按钮添加子项
- **编辑目录**：点击目录项旁的编辑按钮修改标题和描述
- **删除目录**：点击删除按钮移除目录项

### 2. 内容编辑
- **选择章节**：点击左侧目录树中的任意项目
- **切换模式**：使用右上角的按钮组切换编辑/预览/分屏模式
- **编写内容**：在编辑器中使用Markdown语法编写内容

### 3. 文档操作
- **新建文档**：点击顶部菜单的"新建文档"
- **保存文档**：点击"保存文档"（当前为演示功能）
- **导出文档**：点击"导出文档"（当前为演示功能）

## 技术栈

- **前端框架**：Vue 3 (Composition API)
- **构建工具**：Vite
- **UI组件库**：TDesign Vue Next
- **图标库**：TDesign Icons Vue Next
- **样式**：CSS3 + TDesign Design Tokens

## 项目结构

```
document-helper/
├── public/                 # 静态资源
├── src/
│   ├── components/        # Vue组件
│   │   └── DocumentHelper.vue  # 主要组件
│   ├── App.vue           # 根组件
│   ├── main.js           # 应用入口
│   └── style.css         # 全局样式
├── package.json          # 项目配置
└── vite.config.js        # Vite配置
```

## 待实现功能

- [ ] AI智能生成目录大纲
- [ ] 文档导出（PDF、Word、HTML）
- [ ] 文档模板系统
- [ ] 多文档管理
- [ ] 协作编辑功能
- [ ] 版本历史管理
- [ ] 插件系统
- [ ] 暗色主题支持

## 开发说明

### 添加新功能
1. 在 `src/components/` 目录下创建新组件
2. 在 `DocumentHelper.vue` 中引入和使用
3. 更新相关样式和功能

### 自定义主题
修改 `src/style.css` 中的CSS变量来自定义主题色彩

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！
