import { _ as _defineProperty } from '../_chunks/dep-931ef437.js';
import { defineComponent, computed } from 'vue';
import renderFn from '../utils/render-fn.js';
import useSizeProps from '../utils/use-size-props.js';
import '../style/css.js';
import '../utils/use-common-classname.js';
import '../utils/config-context.js';

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var element = {
  "tag": "svg",
  "attrs": {
    "fill": "none",
    "viewBox": "0 0 24 24",
    "width": "1em",
    "height": "1em"
  },
  "children": [{
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M12.9997 7.81265C14.0242 7.41269 14.75 6.4161 14.75 5.25C14.75 3.73122 13.5188 2.5 12 2.5C10.4812 2.5 9.25 3.73122 9.25 5.25C9.25 6.41588 9.97552 7.4123 10.9997 7.81241V9.17624L7.1507 11.4262V15.8836L5.87017 16.5277C5.41577 16.1959 4.85576 16 4.25 16C2.73122 16 1.5 17.2312 1.5 18.75C1.5 20.2688 2.73122 21.5 4.25 21.5C5.76878 21.5 7 20.2688 7 18.75C7 18.5699 6.98269 18.3939 6.94965 18.2235L8.11488 17.6374L11.9997 19.9083L15.8849 17.6372L17.0505 18.2226C17.0174 18.3933 17 18.5696 17 18.75C17 20.2688 18.2312 21.5 19.75 21.5C21.2688 21.5 22.5 20.2688 22.5 18.75C22.5 17.2312 21.2688 16 19.75 16C19.1446 16 18.5849 16.1956 18.1307 16.5271L16.8487 15.8832V11.4262L12.9997 9.17624V7.81265Z"
    }
  }]
};
var systemComponentsFilled = defineComponent({
  name: "SystemComponentsFilledIcon",
  props: {
    size: {
      type: String
    },
    onClick: {
      type: Function
    }
  },
  setup(props, _ref) {
    var {
      attrs
    } = _ref;
    var propsSize = computed(() => props.size);
    var {
      className,
      style
    } = useSizeProps(propsSize);
    var finalCls = computed(() => ["t-icon", "t-icon-system-components-filled", className.value]);
    var finalStyle = computed(() => _objectSpread(_objectSpread({}, style.value), attrs.style));
    var finalProps = computed(() => ({
      class: finalCls.value,
      style: finalStyle.value,
      onClick: e => {
        var _props$onClick;
        return (_props$onClick = props.onClick) === null || _props$onClick === void 0 ? void 0 : _props$onClick.call(props, {
          e
        });
      }
    }));
    return () => renderFn(element, finalProps.value);
  }
});

export default systemComponentsFilled;
//# sourceMappingURL=system-components-filled.js.map
