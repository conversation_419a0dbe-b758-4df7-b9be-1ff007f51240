declare const _default: import("vue").DefineComponent<{
    index: {
        type: NumberConstructor;
    };
    currentIndex: {
        type: NumberConstructor;
    };
    isSwitching: {
        type: BooleanConstructor;
        default: boolean;
    };
    getWrapAttribute: {
        type: FunctionConstructor;
    };
    swiperItemLength: {
        type: NumberConstructor;
        default: number;
    };
    animation: {
        type: import("vue").PropType<import("./type").TdSwiperProps["animation"]>;
        default: import("./type").TdSwiperProps["animation"];
        validator(val: import("./type").TdSwiperProps["animation"]): boolean;
    };
    autoplay: {
        type: BooleanConstructor;
        default: boolean;
    };
    current: {
        type: NumberConstructor;
        default: number;
    };
    defaultCurrent: {
        type: NumberConstructor;
        default: number;
    };
    direction: {
        type: import("vue").PropType<import("./type").TdSwiperProps["direction"]>;
        default: import("./type").TdSwiperProps["direction"];
        validator(val: import("./type").TdSwiperProps["direction"]): boolean;
    };
    duration: {
        type: NumberConstructor;
        default: number;
    };
    height: {
        type: NumberConstructor;
    };
    interval: {
        type: NumberConstructor;
        default: number;
    };
    loop: {
        type: BooleanConstructor;
        default: boolean;
    };
    navigation: {
        type: import("vue").PropType<import("./type").TdSwiperProps["navigation"]>;
    };
    stopOnHover: {
        type: BooleanConstructor;
        default: boolean;
    };
    theme: {
        type: import("vue").PropType<import("./type").TdSwiperProps["theme"]>;
        default: import("./type").TdSwiperProps["theme"];
        validator(val: import("./type").TdSwiperProps["theme"]): boolean;
    };
    trigger: {
        type: import("vue").PropType<import("./type").TdSwiperProps["trigger"]>;
        default: import("./type").TdSwiperProps["trigger"];
        validator(val: import("./type").TdSwiperProps["trigger"]): boolean;
    };
    type: {
        type: import("vue").PropType<import("./type").TdSwiperProps["type"]>;
        default: import("./type").TdSwiperProps["type"];
        validator(val: import("./type").TdSwiperProps["type"]): boolean;
    };
    cardScale: {
        type: NumberConstructor;
        default: number;
    };
    onChange: import("vue").PropType<import("./type").TdSwiperProps["onChange"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    index: {
        type: NumberConstructor;
    };
    currentIndex: {
        type: NumberConstructor;
    };
    isSwitching: {
        type: BooleanConstructor;
        default: boolean;
    };
    getWrapAttribute: {
        type: FunctionConstructor;
    };
    swiperItemLength: {
        type: NumberConstructor;
        default: number;
    };
    animation: {
        type: import("vue").PropType<import("./type").TdSwiperProps["animation"]>;
        default: import("./type").TdSwiperProps["animation"];
        validator(val: import("./type").TdSwiperProps["animation"]): boolean;
    };
    autoplay: {
        type: BooleanConstructor;
        default: boolean;
    };
    current: {
        type: NumberConstructor;
        default: number;
    };
    defaultCurrent: {
        type: NumberConstructor;
        default: number;
    };
    direction: {
        type: import("vue").PropType<import("./type").TdSwiperProps["direction"]>;
        default: import("./type").TdSwiperProps["direction"];
        validator(val: import("./type").TdSwiperProps["direction"]): boolean;
    };
    duration: {
        type: NumberConstructor;
        default: number;
    };
    height: {
        type: NumberConstructor;
    };
    interval: {
        type: NumberConstructor;
        default: number;
    };
    loop: {
        type: BooleanConstructor;
        default: boolean;
    };
    navigation: {
        type: import("vue").PropType<import("./type").TdSwiperProps["navigation"]>;
    };
    stopOnHover: {
        type: BooleanConstructor;
        default: boolean;
    };
    theme: {
        type: import("vue").PropType<import("./type").TdSwiperProps["theme"]>;
        default: import("./type").TdSwiperProps["theme"];
        validator(val: import("./type").TdSwiperProps["theme"]): boolean;
    };
    trigger: {
        type: import("vue").PropType<import("./type").TdSwiperProps["trigger"]>;
        default: import("./type").TdSwiperProps["trigger"];
        validator(val: import("./type").TdSwiperProps["trigger"]): boolean;
    };
    type: {
        type: import("vue").PropType<import("./type").TdSwiperProps["type"]>;
        default: import("./type").TdSwiperProps["type"];
        validator(val: import("./type").TdSwiperProps["type"]): boolean;
    };
    cardScale: {
        type: NumberConstructor;
        default: number;
    };
    onChange: import("vue").PropType<import("./type").TdSwiperProps["onChange"]>;
}>>, {
    type: "default" | "card";
    direction: "vertical" | "horizontal";
    duration: number;
    theme: "dark" | "light";
    animation: "fade" | "slide";
    current: number;
    trigger: "click" | "hover";
    defaultCurrent: number;
    autoplay: boolean;
    isSwitching: boolean;
    swiperItemLength: number;
    interval: number;
    loop: boolean;
    stopOnHover: boolean;
    cardScale: number;
}, {}>;
export default _default;
