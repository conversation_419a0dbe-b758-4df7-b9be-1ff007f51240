{"version": 3, "file": "enhanced-table-props.mjs", "sources": ["../../../components/table/enhanced-table-props.ts"], "sourcesContent": ["/* eslint-disable */\n\n/**\n * 该文件为脚本自动生成文件，请勿随意修改。如需修改请联系 PMC\n * */\n\nimport { TdEnhancedTableProps } from '../table/type';\nimport { PropType } from 'vue';\n\nexport default {\n  /** 树形结构中，拖拽排序前控制，返回值为 `true` 则继续排序；返回值为 `false` 则中止排序还原数据 */\n  beforeDragSort: {\n    type: Function as PropType<TdEnhancedTableProps['beforeDragSort']>,\n  },\n  /** 展开的树形节点。非必须。在需要自由控制展开的树形节点时使用。其他场景无需设置，表格组件有内置展开逻辑 */\n  expandedTreeNodes: {\n    type: Array as PropType<TdEnhancedTableProps['expandedTreeNodes']>,\n    default: undefined as TdEnhancedTableProps['expandedTreeNodes'],\n  },\n  /** 展开的树形节点。非必须。在需要自由控制展开的树形节点时使用。其他场景无需设置，表格组件有内置展开逻辑，非受控属性 */\n  defaultExpandedTreeNodes: {\n    type: Array as PropType<TdEnhancedTableProps['defaultExpandedTreeNodes']>,\n    default: (): TdEnhancedTableProps['defaultExpandedTreeNodes'] => [],\n  },\n  /** 树形结构相关配置。具体属性文档查看 `TableTreeConfig` 相关描述 */\n  tree: {\n    type: Object as PropType<TdEnhancedTableProps['tree']>,\n  },\n  /** 自定义树形结构展开图标，支持全局配置 `GlobalConfigProvider` */\n  treeExpandAndFoldIcon: {\n    type: Function as PropType<TdEnhancedTableProps['treeExpandAndFoldIcon']>,\n  },\n  /** 异常拖拽排序时触发，如：树形结构中，非同层级之间的交换。`context.code` 指交换异常错误码，固定值；`context.reason` 指交换异常的原因 */\n  onAbnormalDragSort: Function as PropType<TdEnhancedTableProps['onAbnormalDragSort']>,\n  /** 树形结构，展开的树节点发生变化时触发，泛型 T 指表格数据类型 */\n  onExpandedTreeNodesChange: Function as PropType<TdEnhancedTableProps['onExpandedTreeNodesChange']>,\n  /** 已废弃。树形结构，用户操作引起节点展开或收起时触发。请更为使用 `onExpandedTreeNodesChange` */\n  onTreeExpandChange: Function as PropType<TdEnhancedTableProps['onTreeExpandChange']>,\n};\n"], "names": ["beforeDragSort", "type", "Function", "expandedTreeNodes", "Array", "defaultExpandedTreeNodes", "default", "tree", "Object", "treeExpandAndFoldIcon", "onAbnormalDragSort", "onExpandedTreeNodesChange", "onTreeExpandChange"], "mappings": ";;;;;;AASA,yBAAe;AAEbA,EAAAA,cAAgB,EAAA;AACdC,IAAAA,IAAM,EAAAC,QAAAA;GACR;AAEAC,EAAAA,iBAAmB,EAAA;AACjBF,IAAAA,IAAM,EAAAG,KAAA;AACN,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAC,EAAAA,wBAA0B,EAAA;AACxBJ,IAAAA,IAAM,EAAAG,KAAA;IACN,SAAS,EAAA,SAATE,QAAAA,GAAA;AAAA,MAAA,OAAiE,EAAC,CAAA;AAAA,KAAA;GACpE;AAEAC,EAAAA,IAAM,EAAA;AACJN,IAAAA,IAAM,EAAAO,MAAAA;GACR;AAEAC,EAAAA,qBAAuB,EAAA;AACrBR,IAAAA,IAAM,EAAAC,QAAAA;GACR;AAEAQ,EAAAA,kBAAoB,EAAAR,QAAA;AAEpBS,EAAAA,yBAA2B,EAAAT,QAAA;AAE3BU,EAAAA,kBAAoB,EAAAV,QAAAA;AACtB,CAAA;;;;"}