{"version": 3, "file": "divider.js", "sources": ["../../../components/divider/divider.tsx"], "sourcesContent": ["import { defineComponent } from 'vue';\nimport props from './props';\nimport { useContent, usePrefixClass } from '@tdesign/shared-hooks';\n\nexport default defineComponent({\n  name: 'TDivider',\n  props,\n  setup(props) {\n    const COMPONENT_NAME = usePrefixClass('divider');\n    const renderContent = useContent();\n    return () => {\n      const { layout, dashed, align } = props;\n      const children = renderContent('default', 'content');\n\n      const dividerClassNames = [\n        `${COMPONENT_NAME.value}`,\n        [`${COMPONENT_NAME.value}--${layout}`],\n        {\n          [`${COMPONENT_NAME.value}--dashed`]: !!dashed,\n          [`${COMPONENT_NAME.value}--with-text`]: !!children,\n          [`${COMPONENT_NAME.value}--with-text-${align}`]: !!children,\n        },\n      ];\n\n      return (\n        <div class={dividerClassNames}>\n          {children && <span class={`${COMPONENT_NAME.value}__inner-text`}>{children}</span>}\n        </div>\n      );\n    };\n  },\n});\n"], "names": ["defineComponent", "name", "props", "setup", "COMPONENT_NAME", "usePrefixClass", "renderContent", "useContent", "layout", "dashed", "align", "children", "dividerClassNames", "concat", "value", "_defineProperty", "_createVNode"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,eAAeA,eAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,UAAA;AACNC,EAAAA,KAAA,EAAAA,KAAA;AACAC,EAAAA,OAAAA,SAAAA,MAAMD,MAAO,EAAA;AACL,IAAA,IAAAE,cAAA,GAAiBC,eAAe,SAAS,CAAA,CAAA;AAC/C,IAAA,IAAMC,gBAAgBC,UAAW,EAAA,CAAA;AACjC,IAAA,OAAO,YAAM;AACX,MAAA,IAAQC,MAAA,GAA0BN,MAAAA,CAA1BM,MAAA;QAAQC,MAAQ,GAAUP,MAAAA,CAAlBO,MAAQ;QAAAC,KAAA,GAAUR,MAAAA,CAAVQ,KAAA,CAAA;AAClB,MAAA,IAAAC,QAAA,GAAWL,aAAc,CAAA,SAAA,EAAW,SAAS,CAAA,CAAA;AAEnD,MAAA,IAAMM,iBAAoB,GAAA,CAAAC,EAAAA,CAAAA,MAAA,CACrBT,cAAe,CAAAU,KAAA,CAClB,EAAA,CAAA,EAAA,CAAAD,MAAA,CAAIT,cAAe,CAAAU,KAAA,EAAA,IAAA,CAAA,CAAAD,MAAA,CAAUL,MAAQ,CAAA,CAAA,EAAAO,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAF,EAAAA,EAAAA,EAAAA,CAAAA,MAAA,CAE/BT,cAAe,CAAAU,KAAA,EAAkB,UAAA,CAAA,EAAA,CAAC,CAACL,MAAA,CAAAI,EAAAA,EAAAA,CAAAA,MAAA,CACnCT,cAAe,CAAAU,KAAA,EAAqB,aAAA,CAAA,EAAA,CAAC,CAACH,QAAA,CAAAE,EAAAA,EAAAA,CAAAA,MAAA,CACtCT,cAAA,CAAeU,KAAoB,EAAAD,cAAAA,CAAAA,CAAAA,MAAA,CAAAH,KAAA,CAAU,EAAA,CAAC,CAACC,QAAA,CAEvD,CAAA,CAAA;AAEA,MAAA,OAAAK,WAAA,CAAA,KAAA,EAAA;QAAA,OACcJ,EAAAA,iBAAAA;OACTD,EAAAA,CAAAA;2BAA4BP,cAAe,CAAAU,KAAA,EAAA,cAAA,CAAA;AAAA,OAAA,EAAA,CAAsBH,QAAS;KAGjF,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}