import { TdSubmenuProps } from '../menu/type';
import { PropType } from 'vue';
declare const _default: {
    content: {
        type: PropType<TdSubmenuProps["content"]>;
    };
    default: {
        type: PropType<TdSubmenuProps["default"]>;
    };
    disabled: BooleanConstructor;
    icon: {
        type: PropType<TdSubmenuProps["icon"]>;
    };
    popupProps: {
        type: PropType<TdSubmenuProps["popupProps"]>;
    };
    title: {
        type: PropType<TdSubmenuProps["title"]>;
    };
    value: {
        type: PropType<TdSubmenuProps["value"]>;
    };
};
export default _default;
