{"version": 3, "file": "dialog.mjs", "sources": ["../../../components/dialog/dialog.tsx"], "sourcesContent": ["import {\n  computed,\n  defineComponent,\n  nextTick,\n  onBeforeUnmount,\n  onMounted,\n  ref,\n  Transition,\n  watch,\n  Teleport,\n  ComponentPublicInstance,\n} from 'vue';\nimport { DialogCloseContext } from './type';\nimport props from './props';\nimport { useConfig, useTeleport, usePrefixClass, usePopupManager, useDestroyOnClose } from '@tdesign/shared-hooks';\nimport { useSameTarget } from './hooks';\n\nimport { getScrollbarWidth } from '@tdesign/common-js/utils/getScrollbarWidth';\n\nimport { getCSSValue } from './utils';\nimport TDialogCard from './dialog-card';\n\nlet mousePosition: { x: number; y: number } | null;\nconst getClickPosition = (e: MouseEvent) => {\n  mousePosition = {\n    x: e.clientX,\n    y: e.clientY,\n  };\n  setTimeout(() => {\n    mousePosition = null;\n  }, 100);\n};\n\nif (typeof window !== 'undefined' && window.document && window.document.documentElement) {\n  document.documentElement.addEventListener('click', getClickPosition, true);\n}\n\nlet key = 1;\n\nexport default defineComponent({\n  name: 'TDialog',\n  inheritAttrs: false,\n  props,\n  emits: ['update:visible'],\n  setup(props, context) {\n    const COMPONENT_NAME = usePrefixClass('dialog');\n    const classPrefix = usePrefixClass();\n    const dialogCardRef = ref<ComponentPublicInstance<{ resetPosition: () => void }>>(null);\n    const { globalConfig } = useConfig('dialog');\n    const confirmBtnAction = (context: { e: MouseEvent }) => {\n      props.onConfirm?.(context);\n    };\n    const cancelBtnAction = (context: { e: MouseEvent }) => {\n      props.onCancel?.(context);\n      emitCloseEvent({ e: context.e, trigger: 'cancel' });\n    };\n    // teleport容器\n    const teleportElement = useTeleport(() => props.attach);\n    useDestroyOnClose();\n    const timer = ref();\n    const styleEl = ref();\n    // 是否模态形式的对话框\n    const isModal = computed(() => props.mode === 'modal');\n    // 是否非模态对话框\n    const isModeLess = computed(() => props.mode === 'modeless');\n    // 是否全屏对话框\n    const isFullScreen = computed(() => props.mode === 'full-screen');\n    const computedVisible = computed(() => props.visible);\n    const maskClass = computed(() => [\n      `${COMPONENT_NAME.value}__mask`,\n      !props.showOverlay && `${classPrefix.value}-is-hidden`,\n    ]);\n    const positionClass = computed(() => {\n      if (isFullScreen.value) return [`${COMPONENT_NAME.value}__position_fullscreen`];\n      if (isModal.value || isModeLess.value) {\n        return [\n          `${COMPONENT_NAME.value}__position`,\n          !!props.top && `${COMPONENT_NAME.value}--top`,\n          `${props.placement && !props.top ? `${COMPONENT_NAME.value}--${props.placement}` : ''}`,\n        ];\n      }\n      return [];\n    });\n    const wrapClass = computed(() =>\n      isFullScreen.value || isModal.value || isModeLess.value ? [`${COMPONENT_NAME.value}__wrap`] : null,\n    );\n    const positionStyle = computed(() => {\n      if (isFullScreen.value) return {}; // 全屏模式，top属性不生效\n\n      // 此处获取定位方式 top 优先级较高 存在时 默认使用top定位\n      const { top } = props;\n      let topStyle = {};\n      if (top !== undefined) {\n        const topValue = getCSSValue(top);\n        topStyle = { paddingTop: topValue };\n      }\n      return topStyle;\n    });\n\n    const { isTopInteractivePopup } = usePopupManager('dialog', {\n      visible: computedVisible,\n    });\n    /**是否已经第一次渲染，懒加载判断 */\n    const isMounted = ref(false);\n\n    watch(\n      () => props.visible,\n      (value) => {\n        if (value) {\n          isMounted.value = true;\n          if ((isModal.value && !props.showInAttachedElement) || isFullScreen.value) {\n            if (props.preventScrollThrough) {\n              document.body.appendChild(styleEl.value);\n            }\n\n            nextTick(() => {\n              if (mousePosition && dialogCardRef.value?.$el) {\n                const el = dialogCardRef.value.$el as HTMLElement;\n                el.style.transformOrigin = `${mousePosition.x - el.offsetLeft}px ${mousePosition.y - el.offsetTop}px`;\n              }\n            });\n          }\n          // 清除鼠标焦点 避免entry事件多次触发（按钮弹出弹窗 不移除焦点 立即按Entry按键 会造成弹窗关闭再弹出）\n          (document.activeElement as HTMLElement)?.blur();\n        } else {\n          clearStyleFunc();\n        }\n        addKeyboardEvent(value);\n      },\n    );\n\n    function destroySelf() {\n      styleEl.value.parentNode?.removeChild?.(styleEl.value);\n    }\n\n    function clearStyleFunc() {\n      clearTimeout(timer.value);\n      timer.value = setTimeout(() => {\n        destroySelf();\n      }, 150);\n    }\n\n    const addKeyboardEvent = (status: boolean) => {\n      if (status) {\n        document.addEventListener('keydown', keyboardEvent);\n        props.confirmOnEnter && document.addEventListener('keydown', keyboardEnterEvent);\n      } else {\n        document.removeEventListener('keydown', keyboardEvent);\n        props.confirmOnEnter && document.removeEventListener('keydown', keyboardEnterEvent);\n      }\n    };\n    // 回车触发确认事件\n    const keyboardEnterEvent = (e: KeyboardEvent) => {\n      const eventSrc = e.target as HTMLElement;\n      if (eventSrc.tagName.toLowerCase() === 'input') return; // 若是input触发 则不执行\n      const { code } = e;\n      if ((code === 'Enter' || code === 'NumpadEnter') && isTopInteractivePopup()) {\n        props.onConfirm?.({ e });\n      }\n    };\n    const keyboardEvent = (e: KeyboardEvent) => {\n      if (e.code === 'Escape' && isTopInteractivePopup()) {\n        props.onEscKeydown?.({ e });\n        // 根据closeOnEscKeydown判断按下ESC时是否触发close事件\n        if (props.closeOnEscKeydown ?? globalConfig.value.closeOnEscKeydown) {\n          emitCloseEvent({ e, trigger: 'esc' });\n          // 阻止事件冒泡\n          e.stopImmediatePropagation();\n        }\n      }\n    };\n    const overlayAction = (e: MouseEvent) => {\n      if (props.showOverlay && (props.closeOnOverlayClick ?? globalConfig.value.closeOnOverlayClick)) {\n        props.onOverlayClick?.({ e });\n        emitCloseEvent({ e, trigger: 'overlay' });\n      }\n    };\n    const { onClick, onMousedown, onMouseup } = useSameTarget(overlayAction);\n    const closeBtnAction = (context: { e: MouseEvent }) => {\n      props.onCloseBtnClick?.(context);\n      emitCloseEvent({\n        trigger: 'close-btn',\n        e: context.e,\n      });\n    };\n\n    // 打开弹窗动画开始时事件\n    const beforeEnter = () => {\n      props.onBeforeOpen?.();\n    };\n\n    // 打开弹窗动画结束时事件\n    const afterEnter = () => {\n      props.onOpened?.();\n    };\n\n    // 关闭弹窗动画开始时事件\n    const beforeLeave = () => {\n      props.onBeforeClose?.();\n    };\n\n    // 关闭弹窗动画结束时事件\n    const afterLeave = () => {\n      dialogCardRef.value?.resetPosition?.();\n      props.onClosed?.();\n    };\n\n    const emitCloseEvent = (ctx: DialogCloseContext) => {\n      props.onClose?.(ctx);\n      // 默认关闭弹窗\n      context.emit('update:visible', false);\n    };\n\n    // Vue在引入阶段对事件的处理还做了哪些初始化操作。Vue在实例上用一个_events属性存贮管理事件的派发和更新，\n    // 暴露出$on, $once, $off, $emit方法给外部管理事件和派发执行事件\n    // 所以通过判断_events某个事件下监听函数数组是否超过一个，可以判断出组件是否监听了当前事件\n    // const hasEventOn = (name: string) => {\n    //   // _events 因没有被暴露在vue实例接口中，只能把这个规则注释掉\n    //   // eslint-disable-next-line dot-notation\n    //   // @ts-ignore\n    //   const eventFuncs = this['_events']?.[name];\n    //   return !!eventFuncs?.length;\n    // };\n\n    const renderDialog = () => {\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      const { theme, onConfirm, onCancel, onCloseBtnClick, ...otherProps } = props;\n      return (\n        /** 非模态形态下draggable为true才允许拖拽 */\n        <div class={wrapClass.value}>\n          <div\n            class={positionClass.value}\n            style={positionStyle.value}\n            onClick={onClick}\n            onMousedown={onMousedown}\n            onMouseup={onMouseup}\n          >\n            <TDialogCard\n              ref={dialogCardRef}\n              theme={theme}\n              {...otherProps}\n              v-slots={context.slots}\n              onConfirm={confirmBtnAction}\n              onCancel={cancelBtnAction}\n              onCloseBtnClick={closeBtnAction}\n            />\n          </div>\n        </div>\n      );\n    };\n\n    onMounted(() => {\n      const hasScrollBar = document.documentElement.scrollHeight > document.documentElement.clientHeight;\n      const scrollWidth = hasScrollBar ? getScrollbarWidth() : 0;\n      styleEl.value = document.createElement('style');\n      styleEl.value.dataset.id = `td_dialog_${+new Date()}_${(key += 1)}`;\n      styleEl.value.innerHTML = `\n        html body {\n          overflow-y: hidden;\n          width: calc(100% - ${scrollWidth}px);\n        }\n      `;\n    });\n\n    onBeforeUnmount(() => {\n      addKeyboardEvent(false);\n      destroySelf();\n    });\n\n    const shouldRender = computed(() => {\n      const { destroyOnClose, visible, lazy } = props;\n      if (!isMounted.value) {\n        return !lazy;\n      } else {\n        return visible || !destroyOnClose;\n      }\n    });\n\n    return () => {\n      const maskView = (isModal.value || isFullScreen.value) && <div key=\"mask\" class={maskClass.value}></div>;\n      const dialogView = renderDialog();\n      const view = [maskView, dialogView];\n      const ctxStyle = { zIndex: props.zIndex };\n      // dialog__ctx--fixed 绝对定位\n      // dialog__ctx--absolute 挂载在attach元素上 相对定位\n      // __ctx--modeless modeless 点击穿透\n      const ctxClass = [\n        `${COMPONENT_NAME.value}__ctx`,\n        {\n          [`${COMPONENT_NAME.value}__ctx--fixed`]: isModal.value || isFullScreen.value,\n          [`${COMPONENT_NAME.value}__ctx--absolute`]: isModal.value && props.showInAttachedElement,\n          [`${COMPONENT_NAME.value}__ctx--modeless`]: isModeLess.value,\n        },\n      ];\n\n      return (\n        <Teleport disabled={!props.attach || !teleportElement.value} to={teleportElement.value}>\n          <Transition\n            duration={300}\n            name={`${COMPONENT_NAME.value}-zoom__vue`}\n            onBeforeEnter={beforeEnter}\n            onAfterEnter={afterEnter}\n            onBeforeLeave={beforeLeave}\n            onAfterLeave={afterLeave}\n          >\n            {shouldRender.value && (\n              <div v-show={props.visible} class={ctxClass} style={ctxStyle} {...context.attrs}>\n                {view}\n              </div>\n            )}\n          </Transition>\n        </Teleport>\n      );\n    };\n  },\n});\n"], "names": ["mousePosition", "getClickPosition", "e", "x", "clientX", "y", "clientY", "setTimeout", "window", "document", "documentElement", "addEventListener", "key", "defineComponent", "name", "inheritAttrs", "props", "emits", "setup", "context", "COMPONENT_NAME", "usePrefixClass", "classPrefix", "dialogCardRef", "ref", "_useConfig", "useConfig", "globalConfig", "confirmBtnAction", "_props2$onConfirm", "onConfirm", "cancelBtnAction", "_props2$onCancel", "onCancel", "emitCloseEvent", "trigger", "teleportElement", "useTeleport", "attach", "useDestroyOnClose", "timer", "styleEl", "isModal", "computed", "mode", "isModeLess", "isFullScreen", "computedVisible", "visible", "maskClass", "concat", "value", "showOverlay", "positionClass", "top", "placement", "wrapClass", "positionStyle", "topStyle", "topValue", "getCSSValue", "paddingTop", "_usePopupManager", "usePopupManager", "isTopInteractivePopup", "isMounted", "watch", "_document$activeEleme", "showInAttachedElement", "preventScrollThrough", "body", "append<PERSON><PERSON><PERSON>", "nextTick", "_dialogCardRef$value", "$el", "el", "style", "transform<PERSON><PERSON>in", "offsetLeft", "offsetTop", "activeElement", "blur", "clearStyleFunc", "addKeyboardEvent", "destroySelf", "_styleEl$value$parent", "_styleEl$value$parent2", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "call", "clearTimeout", "status", "keyboardEvent", "confirmOnEnter", "keyboardEnterEvent", "removeEventListener", "eventSrc", "target", "tagName", "toLowerCase", "code", "_props2$onConfirm2", "_props2$onEscKeydown", "_props2$closeOnEscKey", "onEscKeydown", "closeOnEscKeydown", "stopImmediatePropagation", "overlayAction", "_props2$closeOnOverla", "closeOnOverlayClick", "_props2$onOverlayClic", "onOverlayClick", "_useSameTarget", "useSameTarget", "onClick", "onMousedown", "onMouseup", "closeBtnAction", "_props2$onCloseBtnCli", "onCloseBtnClick", "beforeEnter", "_props2$onBeforeOpen", "onBeforeOpen", "afterEnter", "_props2$onOpened", "onOpened", "beforeLeave", "_props2$onBeforeClose", "onBeforeClose", "afterLeave", "_dialogCardRef$value2", "_dialogCardRef$value3", "_props2$onClosed", "resetPosition", "onClosed", "ctx", "_props2$onClose", "onClose", "emit", "renderDialog", "theme", "otherProps", "_createVNode", "TDialogCard", "_mergeProps", "slots", "onMounted", "hasScrollBar", "scrollHeight", "clientHeight", "scrollWidth", "getScrollbarWidth", "createElement", "dataset", "id", "Date", "innerHTML", "onBeforeUnmount", "shouldRender", "destroyOnClose", "lazy", "<PERSON><PERSON><PERSON><PERSON>", "dialogView", "view", "ctxStyle", "zIndex", "ctxClass", "_defineProperty", "Teleport", "_default", "Transition", "attrs"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBA,IAAIA,aAAA,CAAA;AACJ,IAAMC,gBAAA,GAAmB,SAAnBA,gBAAAA,CAAoBC,CAAkB,EAAA;AAC1BF,EAAAA,aAAA,GAAA;IACdG,GAAGD,CAAE,CAAAE,OAAA;IACLC,GAAGH,CAAE,CAAAI,OAAAA;GACP,CAAA;AACAC,EAAAA,UAAA,CAAW,YAAM;AACCP,IAAAA,aAAA,GAAA,IAAA,CAAA;KACf,GAAG,CAAA,CAAA;AACR,CAAA,CAAA;AAEA,IAAI,OAAOQ,MAAW,KAAA,WAAA,IAAeA,OAAOC,QAAY,IAAAD,MAAA,CAAOC,SAASC,eAAiB,EAAA;EACvFD,QAAA,CAASC,eAAgB,CAAAC,gBAAA,CAAiB,OAAS,EAAAV,gBAAA,EAAkB,IAAI,CAAA,CAAA;AAC3E,CAAA;AAEA,IAAIW,GAAM,GAAA,CAAA,CAAA;AAEV,cAAeC,eAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,SAAA;AACNC,EAAAA,YAAc,EAAA,KAAA;AACdC,EAAAA,KAAA,EAAAA,KAAA;EACAC,KAAA,EAAO,CAAC,gBAAgB,CAAA;AACxBC,EAAAA,KAAA,WAAAA,KAAAA,CAAMF,QAAOG,OAAS,EAAA;AACd,IAAA,IAAAC,cAAA,GAAiBC,eAAe,QAAQ,CAAA,CAAA;AAC9C,IAAA,IAAMC,cAAcD,cAAe,EAAA,CAAA;AAC7B,IAAA,IAAAE,aAAA,GAAgBC,IAA4D,IAAI,CAAA,CAAA;AACtF,IAAA,IAAAC,UAAA,GAAyBC,SAAA,CAAU,QAAQ,CAAA;MAAnCC,YAAA,GAAAF,UAAA,CAAAE,YAAA,CAAA;AACF,IAAA,IAAAC,gBAAA,GAAmB,SAAnBA,gBAAAA,CAAoBT,QAA+B,EAAA;AAAA,MAAA,IAAAU,iBAAA,CAAA;AACvDb,MAAAA,CAAAA,iBAAAA,GAAAA,MAAAA,CAAMc,6CAANd,KAAAA,CAAAA,IAAAA,iBAAAA,CAAAA,IAAAA,CAAAA,MAAAA,EAAkBG,QAAO,CAAA,CAAA;KAC3B,CAAA;AACM,IAAA,IAAAY,eAAA,GAAkB,SAAlBA,eAAAA,CAAmBZ,QAA+B,EAAA;AAAA,MAAA,IAAAa,gBAAA,CAAA;AACtDhB,MAAAA,CAAAA,gBAAAA,GAAAA,MAAAA,CAAMiB,2CAANjB,KAAAA,CAAAA,IAAAA,gBAAAA,CAAAA,IAAAA,CAAAA,MAAAA,EAAiBG,QAAO,CAAA,CAAA;AACxBe,MAAAA,cAAA,CAAe;QAAEhC,CAAGiB,EAAAA,QAAAA,CAAQjB,CAAG;AAAAiC,QAAAA,OAAA,EAAS,QAAA;AAAS,OAAC,CAAA,CAAA;KACpD,CAAA;IAEA,IAAMC,eAAkB,GAAAC,WAAA,CAAY,YAAA;MAAA,OAAMrB,MAAAA,CAAMsB,MAAM,CAAA;KAAA,CAAA,CAAA;AACpCC,IAAAA,iBAAA,EAAA,CAAA;AAClB,IAAA,IAAMC,QAAQhB,GAAI,EAAA,CAAA;AAClB,IAAA,IAAMiB,UAAUjB,GAAI,EAAA,CAAA;IAEpB,IAAMkB,OAAU,GAAAC,QAAA,CAAS,YAAA;AAAA,MAAA,OAAM3B,MAAAA,CAAM4B,SAAS,OAAO,CAAA;KAAA,CAAA,CAAA;IAErD,IAAMC,UAAa,GAAAF,QAAA,CAAS,YAAA;AAAA,MAAA,OAAM3B,MAAAA,CAAM4B,SAAS,UAAU,CAAA;KAAA,CAAA,CAAA;IAE3D,IAAME,YAAe,GAAAH,QAAA,CAAS,YAAA;AAAA,MAAA,OAAM3B,MAAAA,CAAM4B,SAAS,aAAa,CAAA;KAAA,CAAA,CAAA;IAChE,IAAMG,eAAkB,GAAAJ,QAAA,CAAS,YAAA;MAAA,OAAM3B,MAAAA,CAAMgC,OAAO,CAAA;KAAA,CAAA,CAAA;IAC9C,IAAAC,SAAA,GAAYN,SAAS,YAAA;AAAA,MAAA,OAAM,IAAAO,MAAA,CAC5B9B,cAAe,CAAA+B,KAAA,aAClB,CAACnC,MAAAA,CAAMoC,WAAe,OAAAF,MAAA,CAAG5B,WAAY,CAAA6B,KAAA,eAAA,CACtC,CAAA;KAAA,CAAA,CAAA;AACK,IAAA,IAAAE,aAAA,GAAgBV,SAAS,YAAM;MACnC,IAAIG,YAAa,CAAAK,KAAA,EAAc,OAAA,CAAAD,EAAAA,CAAAA,MAAA,CAAI9B,cAAA,CAAe+B,KAA4B,EAAA,uBAAA,CAAA,CAAA,CAAA;AAC1E,MAAA,IAAAT,OAAA,CAAQS,KAAS,IAAAN,UAAA,CAAWM,KAAO,EAAA;QAC9B,OAAA,CAAA,EAAA,CAAAD,MAAA,CACF9B,cAAe,CAAA+B,KAAA,EAAA,YAAA,CAAA,EAClB,CAAC,CAACnC,MAAM,CAAAsC,GAAA,IAAA,EAAA,CAAAJ,MAAA,CAAU9B,cAAe,CAAA+B,KAAA,EAAA,OAAA,CAAA,KAAAD,MAAA,CAC9BlC,MAAM,CAAAuC,SAAA,IAAa,CAACvC,MAAAA,CAAMsC,gBAASlC,cAAA,CAAe+B,KAAUnC,EAAAA,IAAAA,CAAAA,CAAAA,MAAAA,CAAAA,MAAAA,CAAMuC,SAAc,CAAA,GAAA,EAAA,CACrF,CAAA,CAAA;AACF,OAAA;AACA,MAAA,OAAO,EAAC,CAAA;AACV,KAAC,CAAA,CAAA;IACD,IAAMC,SAAY,GAAAb,QAAA,CAAS,YAAA;MAAA,OACzBG,YAAa,CAAAK,KAAA,IAAST,OAAQ,CAAAS,KAAA,IAASN,UAAW,CAAAM,KAAA,GAAQ,CAAA,EAAA,CAAAD,MAAA,CAAI9B,cAAe,CAAA+B,KAAA,EAAA,QAAA,CAAA,CAAiB,GAAA,IAAA,CAAA;AAAA,KAChG,CAAA,CAAA;AACM,IAAA,IAAAM,aAAA,GAAgBd,SAAS,YAAM;AACnC,MAAA,IAAIG,YAAa,CAAAK,KAAA,EAAO,OAAO,EAAC,CAAA;AAG1B,MAAA,IAAEG,MAAQtC,MAAAA,CAARsC;MACR,IAAII,WAAW,EAAC,CAAA;AAChB,MAAA,IAAIJ,QAAQ,KAAW,CAAA,EAAA;AACf,QAAA,IAAAK,QAAA,GAAWC,YAAYN,GAAG,CAAA,CAAA;AACrBI,QAAAA,QAAA,GAAA;AAAEG,UAAAA,YAAYF,QAAAA;SAAS,CAAA;AACpC,OAAA;AACO,MAAA,OAAAD,QAAA,CAAA;AACT,KAAC,CAAA,CAAA;AAED,IAAA,IAAAI,gBAAA,GAAkCC,eAAA,CAAgB,QAAU,EAAA;AAC1Df,QAAAA,OAAS,EAAAD,eAAAA;AACX,OAAC,CAAA;MAFOiB,qBAAA,GAAAF,gBAAA,CAAAE,qBAAA,CAAA;AAIF,IAAA,IAAAC,SAAA,GAAYzC,IAAI,KAAK,CAAA,CAAA;AAE3B0C,IAAAA,KAAA,CACE,YAAA;MAAA,OAAMlD,MAAM,CAAAgC,OAAA,CAAA;KACZ,EAAA,UAACG,KAAU,EAAA;AACT,MAAA,IAAIA,KAAO,EAAA;AAAA,QAAA,IAAAgB,qBAAA,CAAA;QACTF,SAAA,CAAUd,KAAQ,GAAA,IAAA,CAAA;AAClB,QAAA,IAAKT,QAAQS,KAAS,IAAA,CAACnC,MAAM,CAAAoD,qBAAA,IAA0BtB,aAAaK,KAAO,EAAA;UACzE,IAAInC,OAAMqD,oBAAsB,EAAA;YACrB5D,QAAA,CAAA6D,IAAA,CAAKC,WAAY,CAAA9B,OAAA,CAAQU,KAAK,CAAA,CAAA;AACzC,WAAA;AAEAqB,UAAAA,QAAA,CAAS,YAAM;AAAA,YAAA,IAAAC,oBAAA,CAAA;AACT,YAAA,IAAAzE,aAAA,IAAA,CAAAyE,oBAAA,GAAiBlD,aAAc,CAAA4B,KAAA,MAAA,IAAA,IAAAsB,oBAAA,KAAA,KAAA,CAAA,IAAdA,oBAAA,CAAqBC,GAAK,EAAA;AACvC,cAAA,IAAAC,EAAA,GAAKpD,cAAc4B,KAAM,CAAAuB,GAAA,CAAA;cAC5BC,EAAA,CAAAC,KAAA,CAAMC,4BAAqB7E,aAAA,CAAcG,IAAIwE,EAAG,CAAAG,UAAA,EAAA,KAAA,CAAA,CAAA5B,MAAA,CAAgBlD,aAAc,CAAAK,CAAA,GAAIsE,EAAG,CAAAI,SAAA,EAAA,IAAA,CAAA,CAAA;AAC1F,aAAA;AACF,WAAC,CAAA,CAAA;AACH,SAAA;AAEC,QAAA,CAAAZ,qBAAA,GAAA1D,QAAA,CAASuE,+DAATb,qBAAA,CAAwCc,IAAK,EAAA,CAAA;AAChD,OAAO,MAAA;AACUC,QAAAA,cAAA,EAAA,CAAA;AACjB,OAAA;MACAC,gBAAA,CAAiBhC,KAAK,CAAA,CAAA;AACxB,KACF,CAAA,CAAA;IAEA,SAASiC,WAAcA,GAAA;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;MACrB,CAAAD,qBAAA,GAAA5C,OAAA,CAAQU,KAAM,CAAAoC,UAAA,MAAAF,IAAAA,IAAAA,qBAAA,KAAAC,KAAAA,CAAAA,IAAAA,CAAAA,sBAAA,GAAdD,qBAAA,CAA0BG,WAAc,MAAAF,IAAAA,IAAAA,sBAAA,KAAxCA,KAAAA,CAAAA,IAAAA,sBAAA,CAAAG,IAAA,CAAAJ,qBAAA,EAAwC5C,OAAA,CAAQU,KAAK,CAAA,CAAA;AACvD,KAAA;IAEA,SAAS+B,cAAiBA,GAAA;AACxBQ,MAAAA,YAAA,CAAalD,MAAMW,KAAK,CAAA,CAAA;AAClBX,MAAAA,KAAA,CAAAW,KAAA,GAAQ5C,WAAW,YAAM;AACjB6E,QAAAA,WAAA,EAAA,CAAA;SACX,GAAG,CAAA,CAAA;AACR,KAAA;AAEM,IAAA,IAAAD,gBAAA,GAAmB,SAAnBA,gBAAAA,CAAoBQ,MAAoB,EAAA;AAC5C,MAAA,IAAIA,MAAQ,EAAA;AACDlF,QAAAA,QAAA,CAAAE,gBAAA,CAAiB,WAAWiF,aAAa,CAAA,CAAA;QAClD5E,MAAM,CAAA6E,cAAA,IAAkBpF,QAAS,CAAAE,gBAAA,CAAiB,WAAWmF,kBAAkB,CAAA,CAAA;AACjF,OAAO,MAAA;AACIrF,QAAAA,QAAA,CAAAsF,mBAAA,CAAoB,WAAWH,aAAa,CAAA,CAAA;QACrD5E,MAAM,CAAA6E,cAAA,IAAkBpF,QAAS,CAAAsF,mBAAA,CAAoB,WAAWD,kBAAkB,CAAA,CAAA;AACpF,OAAA;KACF,CAAA;AAEM,IAAA,IAAAA,kBAAA,GAAqB,SAArBA,kBAAAA,CAAsB5F,CAAqB,EAAA;AAC/C,MAAA,IAAM8F,WAAW9F,CAAE,CAAA+F,MAAA,CAAA;MACf,IAAAD,QAAA,CAASE,OAAQ,CAAAC,WAAA,EAAkB,KAAA,OAAA,EAAS,OAAA;AAC1C,MAAA,IAAEC,OAASlG,CAAA,CAATkG;AACR,MAAA,IAAA,CAAKA,IAAS,KAAA,OAAA,IAAWA,IAAS,KAAA,aAAA,KAAkBpC,uBAAyB,EAAA;AAAA,QAAA,IAAAqC,kBAAA,CAAA;AAC3ErF,QAAAA,CAAAA,kBAAAA,GAAAA,MAAM,CAAAc,SAAA,MAAA,IAAA,IAAAuE,kBAAA,KAAA,KAAA,CAAA,IAANrF,kBAAAA,CAAAA,IAAAA,CAAAA,MAAM,EAAY;AAAEd,UAAAA,CAAA,EAAAA,CAAAA;AAAE,SAAC,CAAA,CAAA;AACzB,OAAA;KACF,CAAA;AACM,IAAA,IAAA0F,aAAA,GAAgB,SAAhBA,aAAAA,CAAiB1F,CAAqB,EAAA;MAC1C,IAAIA,CAAE,CAAAkG,IAAA,KAAS,QAAY,IAAApC,qBAAA,EAAyB,EAAA;QAAA,IAAAsC,oBAAA,EAAAC,qBAAA,CAAA;AAClDvF,QAAAA,CAAAA,oBAAAA,GAAAA,MAAM,CAAAwF,YAAA,MAAA,IAAA,IAAAF,oBAAA,KAAA,KAAA,CAAA,IAANtF,oBAAAA,CAAAA,IAAAA,CAAAA,MAAM,EAAe;AAAEd,UAAAA,CAAA,EAAAA,CAAAA;AAAE,SAAC,CAAA,CAAA;AAE1B,QAAA,IAAA,CAAAqG,qBAAA,GAAIvF,MAAM,CAAAyF,iBAAA,MAAAF,IAAAA,IAAAA,qBAAA,KAAAA,KAAAA,CAAAA,GAAAA,qBAAA,GAAqB5E,YAAa,CAAAwB,KAAA,CAAMsD,iBAAmB,EAAA;AACnEvE,UAAAA,cAAA,CAAe;AAAEhC,YAAAA,CAAA,EAAAA,CAAA;AAAGiC,YAAAA,OAAS,EAAA,KAAA;AAAM,WAAC,CAAA,CAAA;UAEpCjC,CAAA,CAAEwG,wBAAyB,EAAA,CAAA;AAC7B,SAAA;AACF,OAAA;KACF,CAAA;AACM,IAAA,IAAAC,aAAA,GAAgB,SAAhBA,aAAAA,CAAiBzG,CAAkB,EAAA;AAAA,MAAA,IAAA0G,qBAAA,CAAA;MACvC,IAAI5F,OAAMoC,WAAgBpC,KAAAA,CAAAA,qBAAAA,GAAAA,MAAAA,CAAM6F,mBAAuB,MAAA,IAAA,IAAAD,qBAAA,KAAAA,KAAAA,CAAAA,GAAAA,qBAAA,GAAAjF,YAAA,CAAawB,MAAM0D,mBAAsB,CAAA,EAAA;AAAA,QAAA,IAAAC,qBAAA,CAAA;AAC9F9F,QAAAA,CAAAA,qBAAAA,GAAAA,MAAM,CAAA+F,cAAA,MAAA,IAAA,IAAAD,qBAAA,KAAA,KAAA,CAAA,IAAN9F,qBAAAA,CAAAA,IAAAA,CAAAA,MAAM,EAAiB;AAAEd,UAAAA,CAAA,EAAAA,CAAAA;AAAE,SAAC,CAAA,CAAA;AAC5BgC,QAAAA,cAAA,CAAe;AAAEhC,UAAAA,CAAA,EAAAA,CAAA;AAAGiC,UAAAA,OAAS,EAAA,SAAA;AAAU,SAAC,CAAA,CAAA;AAC1C,OAAA;KACF,CAAA;AACA,IAAA,IAAA6E,cAAA,GAA4CC,cAAcN,aAAa,CAAA;MAA/DO,OAAS,GAAAF,cAAA,CAATE,OAAS;MAAAC,WAAA,GAAAH,cAAA,CAAAG,WAAA;MAAaC,SAAU,GAAAJ,cAAA,CAAVI,SAAU,CAAA;AAClC,IAAA,IAAAC,cAAA,GAAiB,SAAjBA,cAAAA,CAAkBlG,QAA+B,EAAA;AAAA,MAAA,IAAAmG,qBAAA,CAAA;AACrDtG,MAAAA,CAAAA,qBAAAA,GAAAA,MAAAA,CAAMuG,uDAANvG,KAAAA,CAAAA,IAAAA,qBAAAA,CAAAA,IAAAA,CAAAA,MAAAA,EAAwBG,QAAO,CAAA,CAAA;AAChBe,MAAAA,cAAA,CAAA;AACbC,QAAAA,OAAS,EAAA,WAAA;QACTjC,GAAGiB,QAAQ,CAAAjB,CAAAA;AACb,OAAC,CAAA,CAAA;KACH,CAAA;AAGA,IAAA,IAAMsH,cAAc,SAAdA,cAAoB;AAAA,MAAA,IAAAC,oBAAA,CAAA;AACxBzG,MAAAA,CAAAA,oBAAAA,GAAAA,OAAM0G,YAAe,MAAA,IAAA,IAAAD,oBAAA,KAAA,KAAA,CAAA,IAArBzG,oBAAAA,CAAAA,IAAAA,CAAAA,MAAqB,CAAA,CAAA;KACvB,CAAA;AAGA,IAAA,IAAM2G,aAAa,SAAbA,aAAmB;AAAA,MAAA,IAAAC,gBAAA,CAAA;AACvB5G,MAAAA,CAAAA,gBAAAA,GAAAA,OAAM6G,QAAW,MAAA,IAAA,IAAAD,gBAAA,KAAA,KAAA,CAAA,IAAjB5G,gBAAAA,CAAAA,IAAAA,CAAAA,MAAiB,CAAA,CAAA;KACnB,CAAA;AAGA,IAAA,IAAM8G,cAAc,SAAdA,cAAoB;AAAA,MAAA,IAAAC,qBAAA,CAAA;AACxB/G,MAAAA,CAAAA,qBAAAA,GAAAA,OAAMgH,aAAgB,MAAA,IAAA,IAAAD,qBAAA,KAAA,KAAA,CAAA,IAAtB/G,qBAAAA,CAAAA,IAAAA,CAAAA,MAAsB,CAAA,CAAA;KACxB,CAAA;AAGA,IAAA,IAAMiH,aAAa,SAAbA,aAAmB;AAAA,MAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,gBAAA,CAAA;MACvB,CAAAF,qBAAA,GAAA3G,aAAA,CAAc4B,gFAAd+E,qBAAA,CAAqBG,aAAgB,cAAAF,qBAAA,KAAA,KAAA,CAAA,IAArCA,qBAAA,CAAA1C,IAAA,CAAAyC,qBAAqC,CAAA,CAAA;AACrClH,MAAAA,CAAAA,gBAAAA,GAAAA,OAAMsH,QAAW,MAAA,IAAA,IAAAF,gBAAA,KAAA,KAAA,CAAA,IAAjBpH,gBAAAA,CAAAA,IAAAA,CAAAA,MAAiB,CAAA,CAAA;KACnB,CAAA;AAEM,IAAA,IAAAkB,cAAA,GAAiB,SAAjBA,cAAAA,CAAkBqG,GAA4B,EAAA;AAAA,MAAA,IAAAC,eAAA,CAAA;AAClDxH,MAAAA,CAAAA,eAAAA,GAAAA,MAAAA,CAAMyH,yCAANzH,KAAAA,CAAAA,IAAAA,eAAAA,CAAAA,IAAAA,CAAAA,MAAAA,EAAgBuH,GAAG,CAAA,CAAA;AAEXpH,MAAAA,OAAA,CAAAuH,IAAA,CAAK,kBAAkB,KAAK,CAAA,CAAA;KACtC,CAAA;AAaA,IAAA,IAAMC,eAAe,SAAfA,eAAqB;AAEzB,MAAA,IAAQC,KAAO,GAAwD5H,MAAAA,CAA/D4H,KAAO;QAAA9G,SAAA,GAAwDd,MAAAA,CAAxDc,SAAA;QAAWG,QAAU,GAAmCjB,MAAAA,CAA7CiB,QAAU;QAAAsF,eAAA,GAAmCvG,MAAAA,CAAnCuG,eAAA;AAAoBsB,QAAAA,sCAAe7H,MAAAA,EAAAA,SAAAA,CAAAA,CAAAA;AACvE,MAAA,OAAA8H,WAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAEctF,SAAU,CAAAL,KAAAA;AAAA,OAAA,EAAA,CAAA2F,WAAA,CAAA,KAAA,EAAA;QAAA,OAEXzF,EAAAA,aAAc,CAAAF,KAAA;QAAA,OACdM,EAAAA,aAAc,CAAAN,KAAA;AAAA,QAAA,SAAA,EACZ+D,OACT;AAAA,QAAA,aAAA,EAAaC;mBACFC,EAAAA,SAAAA;AAAA,OAAA,EAAA,CAAA0B,WAAA,CAAAC,WAAA,EAAAC,UAAA,CAAA;AAAA,QAAA,KAAA,EAGJzH,aACL;QAAA,OAAOqH,EAAAA,KAAAA;AACH,OAAA,EAAAC,UAAA,EAAA;AAAA,QAAA,WAAA,EAEOjH,gBAAA;AAAA,QAAA,UAAA,EACDG,eACV;QAAA,iBAAiBsF,EAAAA,cAAAA;OAHRlG,CAAAA,EAAAA,OAAQ,CAAA8H,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;KAQ3B,CAAA;AAEAC,IAAAA,SAAA,CAAU,YAAM;AACd,MAAA,IAAMC,YAAe,GAAA1I,QAAA,CAASC,eAAgB,CAAA0I,YAAA,GAAe3I,SAASC,eAAgB,CAAA2I,YAAA,CAAA;MAChF,IAAAC,WAAA,GAAcH,YAAe,GAAAI,iBAAA,EAAsB,GAAA,CAAA,CAAA;MACjD9G,OAAA,CAAAU,KAAA,GAAQ1C,QAAS,CAAA+I,aAAA,CAAc,OAAO,CAAA,CAAA;MACtC/G,OAAA,CAAAU,KAAA,CAAMsG,QAAQC,EAAK,GAAA,YAAA,CAAAxG,MAAA,CAAa,CAAC,IAAIyG,IAAA,gBAAW/I,GAAO,IAAA,CAAA,CAAA,CAAA;MAC/D6B,OAAA,CAAQU,MAAMyG,SAAY,yFAAA1G,MAAA,CAGDoG,WAAA,EAAA,yBAAA,CAAA,CAAA;AAG3B,KAAC,CAAA,CAAA;AAEDO,IAAAA,eAAA,CAAgB,YAAM;MACpB1E,gBAAA,CAAiB,KAAK,CAAA,CAAA;AACVC,MAAAA,WAAA,EAAA,CAAA;AACd,KAAC,CAAA,CAAA;AAEK,IAAA,IAAA0E,YAAA,GAAenH,SAAS,YAAM;AAClC,MAAA,IAAQoH,cAAA,GAAkC/I,MAAAA,CAAlC+I,cAAA;QAAgB/G,OAAS,GAAShC,MAAAA,CAAlBgC,OAAS;QAAAgH,IAAA,GAAShJ,MAAAA,CAATgJ,IAAA,CAAA;AAC7B,MAAA,IAAA,CAAC/F,UAAUd,KAAO,EAAA;AACpB,QAAA,OAAO,CAAC6G,IAAA,CAAA;AACV,OAAO,MAAA;QACL,OAAOhH,WAAW,CAAC+G,cAAA,CAAA;AACrB,OAAA;AACF,KAAC,CAAA,CAAA;AAED,IAAA,OAAO,YAAM;MACL,IAAAE,QAAA,GAAA,CAAYvH,OAAQ,CAAAS,KAAA,IAASL,YAAa,CAAAK,KAAA,KAAA2F,WAAA,CAAA,KAAA,EAAA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,OAAA,EAAiC7F,SAAA,CAAUE,KAAAA;OAAO,EAAA,IAAA,CAAA,CAAA;AAClG,MAAA,IAAM+G,aAAavB,YAAa,EAAA,CAAA;AAC1B,MAAA,IAAAwB,IAAA,GAAO,CAACF,QAAA,EAAUC,UAAU,CAAA,CAAA;AAClC,MAAA,IAAME,QAAW,GAAA;QAAEC,MAAQrJ,EAAAA,MAAAA,CAAMqJ,MAAAA;OAAO,CAAA;MAIxC,IAAMC,QAAW,GAAA,CAAA,EAAA,CAAApH,MAAA,CACZ9B,cAAe,CAAA+B,KAAA,EAAAoH,OAAAA,CAAAA,EAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAA,EAAA,EAAA,EAAA,CAAArH,MAAA,CAEZ9B,cAAA,CAAe+B,KAAsB,EAAAT,cAAAA,CAAAA,EAAAA,OAAA,CAAQS,SAASL,YAAa,CAAAK,KAAA,CAAA,EAAA,EAAA,CAAAD,MAAA,CACnE9B,cAAA,CAAe+B,KAAyB,EAAAT,iBAAAA,CAAAA,EAAAA,OAAA,CAAQS,SAASnC,MAAM,CAAAoD,qBAAA,CAAA,EAAA,EAAA,CAAAlB,MAAA,CAC/D9B,cAAe,CAAA+B,KAAA,EAAyBN,iBAAAA,CAAAA,EAAAA,UAAW,CAAAM,KAAA,CAE3D,CAAA,CAAA;MAGE,OAAA2F,WAAA,CAAA0B,QAAA,EAAA;QAAA,UAAoB,EAAA,CAACxJ,OAAMsB,MAAU,IAAA,CAACF,gBAAgBe,KAAO;AAAA,QAAA,IAAA,EAAIf,gBAAgBe,KAAAA;AAC/E,OAAA,EAAA;AAAA,QAAA,SAAA,EAAA,SAAAsH,QAAA,GAAA;UAAA,OAAA3B,CAAAA,WAAA,CAAA4B,UAAA,EAAA;AAAA,YAAA,UAAA,EACY,GAAA;AAAA,YAAA,MAAA,EAAA,EAAA,CAAAxH,MAAA,CACD9B,cAAA,CAAe+B,KACxB,EAAA,YAAA,CAAA;AAAA,YAAA,eAAA,EAAeqE,WACf;AAAA,YAAA,cAAA,EAAcG;6BACCG,WAAA;YAAA,cACDG,EAAAA,UAAAA;AAEb,WAAA,EAAA;AAAA,YAAA,SAAA,EAAA,SAAAwC,QAAA,GAAA;cAAA,OAAAX,CAAAA,YAAA,CAAa3G;yBACuBmH,QAAU;gBAAA,OAAOF,EAAAA,QAAAA;eAAcjJ,EAAAA,OAAQ,CAAAwJ,KAAA,CACvER,EAAAA,CAAAA,iBADUnJ,MAAM,CAAAgC,OAAA,CAAlB,CAAA,CAAA,CAAA,CAAA;AAAA,aAAA;AAAA,WAAA,CAAA,CAAA,CAAA;AAAA,SAAA;AAAA,OAAA,CAAA,CAAA;KAOX,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}