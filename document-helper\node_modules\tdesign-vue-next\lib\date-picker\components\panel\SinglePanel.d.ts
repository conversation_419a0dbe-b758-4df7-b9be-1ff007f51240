import { PropType } from 'vue';
import type { TdDatePickerProps } from '../../type';
declare const _default: import("vue").DefineComponent<{
    disableDate: PropType<TdDatePickerProps["disableDate"]>;
    mode: {
        type: PropType<TdDatePickerProps["mode"]>;
        default: string;
    };
    format: PropType<TdDatePickerProps["format"]>;
    presetsPlacement: {
        type: PropType<TdDatePickerProps["presetsPlacement"]>;
        default: string;
    };
    value: PropType<TdDatePickerProps["value"]>;
    timePickerProps: PropType<TdDatePickerProps["timePickerProps"]>;
    presets: PropType<TdDatePickerProps["presets"]>;
    enableTimePicker: BooleanConstructor;
    firstDayOfWeek: NumberConstructor;
    year: NumberConstructor;
    month: NumberConstructor;
    time: StringConstructor;
    popupVisible: BooleanConstructor;
    multiple: BooleanConstructor;
    needConfirm: {
        type: BooleanConstructor;
        default: boolean;
    };
    onPanelClick: FunctionConstructor;
    onCellClick: FunctionConstructor;
    onCellMouseEnter: FunctionConstructor;
    onCellMouseLeave: FunctionConstructor;
    onJumperClick: FunctionConstructor;
    onConfirmClick: FunctionConstructor;
    onPresetClick: FunctionConstructor;
    onYearChange: FunctionConstructor;
    onMonthChange: FunctionConstructor;
    onTimePickerChange: FunctionConstructor;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    disableDate: PropType<TdDatePickerProps["disableDate"]>;
    mode: {
        type: PropType<TdDatePickerProps["mode"]>;
        default: string;
    };
    format: PropType<TdDatePickerProps["format"]>;
    presetsPlacement: {
        type: PropType<TdDatePickerProps["presetsPlacement"]>;
        default: string;
    };
    value: PropType<TdDatePickerProps["value"]>;
    timePickerProps: PropType<TdDatePickerProps["timePickerProps"]>;
    presets: PropType<TdDatePickerProps["presets"]>;
    enableTimePicker: BooleanConstructor;
    firstDayOfWeek: NumberConstructor;
    year: NumberConstructor;
    month: NumberConstructor;
    time: StringConstructor;
    popupVisible: BooleanConstructor;
    multiple: BooleanConstructor;
    needConfirm: {
        type: BooleanConstructor;
        default: boolean;
    };
    onPanelClick: FunctionConstructor;
    onCellClick: FunctionConstructor;
    onCellMouseEnter: FunctionConstructor;
    onCellMouseLeave: FunctionConstructor;
    onJumperClick: FunctionConstructor;
    onConfirmClick: FunctionConstructor;
    onPresetClick: FunctionConstructor;
    onYearChange: FunctionConstructor;
    onMonthChange: FunctionConstructor;
    onTimePickerChange: FunctionConstructor;
}>>, {
    mode: "date" | "month" | "year" | "quarter" | "week";
    enableTimePicker: boolean;
    multiple: boolean;
    popupVisible: boolean;
    presetsPlacement: "left" | "right" | "top" | "bottom";
    needConfirm: boolean;
}, {}>;
export default _default;
