{"version": 3, "file": "dialog-card-props.js", "sources": ["../../../components/dialog/dialog-card-props.ts"], "sourcesContent": ["/* eslint-disable */\n\n/**\n * 该文件为脚本自动生成文件，请勿随意修改。如需修改请联系 PMC\n * */\n\nimport { TdDialogCardProps } from '../dialog/type';\nimport { PropType } from 'vue';\n\nexport default {\n  /** 对话框内容 */\n  body: {\n    type: [String, Function] as PropType<TdDialogCardProps['body']>,\n  },\n  /** 取消按钮，可自定义。值为 null 则不显示取消按钮。值类型为字符串，则表示自定义按钮文本，值类型为 Object 则表示透传 Button 组件属性。使用 TNode 自定义按钮时，需自行控制取消事件 */\n  cancelBtn: {\n    type: [String, Object, Function] as PropType<TdDialogCardProps['cancelBtn']>,\n  },\n  /** 关闭按钮，可以自定义。值为 true 显示默认关闭按钮，值为 false 不显示关闭按钮。值类型为 string 则直接显示值，如：“关闭”。值类型为 TNode，则表示呈现自定义按钮示例 */\n  closeBtn: {\n    type: [String, Boolean, Function] as PropType<TdDialogCardProps['closeBtn']>,\n    default: true as TdDialogCardProps['closeBtn'],\n  },\n  /** 确认按钮。值为 null 则不显示确认按钮。值类型为字符串，则表示自定义按钮文本，值类型为 Object 则表示透传 Button 组件属性。使用 TNode 自定义按钮时，需自行控制确认事件 */\n  confirmBtn: {\n    type: [String, Object, Function] as PropType<TdDialogCardProps['confirmBtn']>,\n  },\n  /** 确认按钮加载状态 */\n  confirmLoading: {\n    type: Boolean,\n    default: undefined,\n  },\n  /** 底部操作栏，默认会有“确认”和“取消”两个按钮。值为 true 显示默认操作按钮，值为 false 不显示任何内容，值类型为 Function 表示自定义底部内容 */\n  footer: {\n    type: [Boolean, Function] as PropType<TdDialogCardProps['footer']>,\n  },\n  /** 头部内容。值为 true 显示空白头部，值为 false 不显示任何内容，值类型为 string 则直接显示值，值类型为 Function 表示自定义头部内容 */\n  header: {\n    type: [String, Boolean, Function] as PropType<TdDialogCardProps['header']>,\n    default: true as TdDialogCardProps['header'],\n  },\n  /** 对话框风格 */\n  theme: {\n    type: String as PropType<TdDialogCardProps['theme']>,\n    default: 'default' as TdDialogCardProps['theme'],\n    validator(val: TdDialogCardProps['theme']): boolean {\n      if (!val) return true;\n      return ['default', 'info', 'warning', 'danger', 'success'].includes(val);\n    },\n  },\n  /** 如果“取消”按钮存在，则点击“取消”按钮时触发，同时触发关闭事件 */\n  onCancel: Function as PropType<TdDialogCardProps['onCancel']>,\n  /** 点击右上角关闭按钮时触发 */\n  onCloseBtnClick: Function as PropType<TdDialogCardProps['onCloseBtnClick']>,\n  /** 如果“确认”按钮存在，则点击“确认”按钮时触发，或者键盘按下回车键时触发 */\n  onConfirm: Function as PropType<TdDialogCardProps['onConfirm']>,\n};\n"], "names": ["body", "type", "String", "Function", "cancelBtn", "Object", "closeBtn", "Boolean", "confirmBtn", "confirmLoading", "footer", "header", "theme", "validator", "val", "includes", "onCancel", "onCloseBtnClick", "onConfirm"], "mappings": ";;;;;;AASA,sBAAe;AAEbA,EAAAA,IAAM,EAAA;AACJC,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,QAAQ,CAAA;GACzB;AAEAC,EAAAA,SAAW,EAAA;AACTH,IAAAA,IAAM,EAAA,CAACC,MAAQ,EAAAG,MAAA,EAAQF,QAAQ,CAAA;GACjC;AAEAG,EAAAA,QAAU,EAAA;AACRL,IAAAA,IAAM,EAAA,CAACC,MAAQ,EAAAK,OAAA,EAASJ,QAAQ,CAAA;IAChC,SAAS,EAAA,IAAA;GACX;AAEAK,EAAAA,UAAY,EAAA;AACVP,IAAAA,IAAM,EAAA,CAACC,MAAQ,EAAAG,MAAA,EAAQF,QAAQ,CAAA;GACjC;AAEAM,EAAAA,cAAgB,EAAA;AACdR,IAAAA,IAAM,EAAAM,OAAA;AACN,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAG,EAAAA,MAAQ,EAAA;AACNT,IAAAA,IAAA,EAAM,CAACM,OAAA,EAASJ,QAAQ,CAAA;GAC1B;AAEAQ,EAAAA,MAAQ,EAAA;AACNV,IAAAA,IAAM,EAAA,CAACC,MAAQ,EAAAK,OAAA,EAASJ,QAAQ,CAAA;IAChC,SAAS,EAAA,IAAA;GACX;AAEAS,EAAAA,KAAO,EAAA;AACLX,IAAAA,IAAM,EAAAC,MAAA;AACN,IAAA,SAAA,EAAS,SAAA;AACTW,IAAAA,WAAAA,SAAAA,UAAUC,GAA0C,EAAA;AAClD,MAAA,IAAI,CAACA,GAAA,EAAY,OAAA,IAAA,CAAA;AACV,MAAA,OAAA,CAAC,WAAW,MAAQ,EAAA,SAAA,EAAW,UAAU,SAAS,CAAA,CAAEC,SAASD,GAAG,CAAA,CAAA;AACzE,KAAA;GACF;AAEAE,EAAAA,QAAU,EAAAb,QAAA;AAEVc,EAAAA,eAAiB,EAAAd,QAAA;AAEjBe,EAAAA,SAAW,EAAAf,QAAAA;AACb,CAAA;;;;"}