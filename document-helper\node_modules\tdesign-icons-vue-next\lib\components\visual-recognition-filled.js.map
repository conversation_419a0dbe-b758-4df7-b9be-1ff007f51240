{"version": 3, "file": "visual-recognition-filled.js", "sources": ["../../src/components/visual-recognition-filled.tsx"], "sourcesContent": ["import { computed, PropType, defineComponent } from 'vue';\nimport renderFn from '../utils/render-fn';\nimport {\n  TdIconSVGProps, SVGJson,\n} from '../utils/types';\nimport useSizeProps from '../utils/use-size-props';\n\nimport '../style/css';\n\nconst element: SVGJson = {\"tag\":\"svg\",\"attrs\":{\"fill\":\"none\",\"viewBox\":\"0 0 24 24\",\"width\":\"1em\",\"height\":\"1em\"},\"children\":[{\"tag\":\"path\",\"attrs\":{\"fill\":\"currentColor\",\"d\":\"M22 2H2V22H14.8762C14.0139 20.897 13.5 19.5085 13.5 18C13.5 16.61 13.9363 15.3219 14.6794 14.2652L9 8.58579L4 13.5858V4H20V11.5C20.6978 11.5 21.3699 11.61 22 11.8135V2Z\"}},{\"tag\":\"path\",\"attrs\":{\"fill\":\"currentColor\",\"d\":\"M16 5.25C14.4812 5.25 13.25 6.48122 13.25 8 13.25 9.51878 14.4812 10.75 16 10.75 17.5188 10.75 18.75 9.51878 18.75 8 18.75 6.48122 17.5188 5.25 16 5.25zM24 16V14H16V16H19V23H21V16H24z\"}}]};\n\nexport default defineComponent({\n  name: 'VisualRecognitionFilledIcon',\n  props: {\n    size: {\n      type: String,\n    },\n    onClick: {\n      type: Function as PropType<TdIconSVGProps['onClick']>,\n    },\n  },\n  setup(props, { attrs }) {\n    const propsSize = computed(() => props.size);\n\n    const { className, style } = useSizeProps(propsSize);\n\n    const finalCls = computed(() => ['t-icon', 't-icon-visual-recognition-filled', className.value]);\n    const finalStyle = computed(() => ({ ...style.value, ...(attrs.style as Styles) }));\n    const finalProps = computed(() => ({\n      class: finalCls.value,\n      style: finalStyle.value,\n      onClick: (e:MouseEvent) => props.onClick?.({ e }),\n    }));\n    return () => renderFn(element, finalProps.value);\n  },\n\n});\n"], "names": ["element", "defineComponent", "name", "props", "size", "type", "String", "onClick", "Function", "setup", "attrs", "propsSize", "computed", "className", "style", "useSizeProps", "finalCls", "value", "finalStyle", "_objectSpread", "finalProps", "class", "e", "_props$onClick", "call", "renderFn"], "mappings": ";;;;;;;;;;;;;AASA,IAAMA,UAAmB;EAAC,OAAM;EAAM,SAAQ;IAAC,QAAO;IAAO,WAAU;IAAY,SAAQ;IAAM,UAAS;;EAAO,YAAW,CAAC;IAAC,OAAM;IAAO,SAAQ;MAAC,QAAO;MAAe,KAAI;;KAA6K;IAAC,OAAM;IAAO,SAAQ;MAAC,QAAO;MAAe,KAAI;;;;AAE5Y,8BAAeC,oBAAgB;EAC7BC,MAAM;EACNC,OAAO;IACLC,MAAM;MACJC,MAAMC;;IAERC,SAAS;MACPF,MAAMG;;;EAGVC,MAAMN,aAAkB;IAAA,IAAX;MAAEO;;QACPC,YAAYC,aAAS,MAAMT,MAAMC;QAEjC;MAAES;MAAWC;QAAUC,8BAAaJ;QAEpCK,WAAWJ,aAAS,MAAM,CAAC,UAAU,oCAAoCC,UAAUI;QACnFC,aAAaN,aAAS,MAAAO,aAAA,CAAAA,aAAA,KAAYL,MAAMG,QAAWP,MAAMI;QACzDM,aAAaR,aAAS;MAC1BS,OAAOL,SAASC;MAChBH,OAAOI,WAAWD;MAClBV,SAAUe;;iCAAiBnB,MAAMI,0DAANgB,cAAA,CAAAC,IAAA,CAAArB,OAAgB;UAAEmB;;;;WAExC,MAAMG,0BAASzB,SAASoB,WAAWH;;AAAA;;;;"}