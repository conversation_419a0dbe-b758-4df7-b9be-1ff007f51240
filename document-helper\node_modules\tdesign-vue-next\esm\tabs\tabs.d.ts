import { Ref } from 'vue';
import { TabValue, TdTabsProps } from './type';
export interface InjectTabs {
    value: Ref<TabValue>;
}
declare const _default: import("vue").DefineComponent<{
    action: {
        type: import("vue").PropType<TdTabsProps["action"]>;
    };
    addable: BooleanConstructor;
    disabled: BooleanConstructor;
    dragSort: BooleanConstructor;
    list: {
        type: import("vue").PropType<TdTabsProps["list"]>;
    };
    placement: {
        type: import("vue").PropType<TdTabsProps["placement"]>;
        default: TdTabsProps["placement"];
        validator(val: TdTabsProps["placement"]): boolean;
    };
    scrollPosition: {
        type: import("vue").PropType<TdTabsProps["scrollPosition"]>;
        default: TdTabsProps["scrollPosition"];
        validator(val: TdTabsProps["scrollPosition"]): boolean;
    };
    size: {
        type: import("vue").PropType<TdTabsProps["size"]>;
        default: TdTabsProps["size"];
        validator(val: TdTabsProps["size"]): boolean;
    };
    theme: {
        type: import("vue").PropType<TdTabsProps["theme"]>;
        default: TdTabsProps["theme"];
        validator(val: TdTabsProps["theme"]): boolean;
    };
    value: {
        type: import("vue").PropType<TdTabsProps["value"]>;
        default: TdTabsProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<TdTabsProps["value"]>;
        default: TdTabsProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<TdTabsProps["defaultValue"]>;
    };
    onAdd: import("vue").PropType<TdTabsProps["onAdd"]>;
    onChange: import("vue").PropType<TdTabsProps["onChange"]>;
    onDragSort: import("vue").PropType<TdTabsProps["onDragSort"]>;
    onRemove: import("vue").PropType<TdTabsProps["onRemove"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    action: {
        type: import("vue").PropType<TdTabsProps["action"]>;
    };
    addable: BooleanConstructor;
    disabled: BooleanConstructor;
    dragSort: BooleanConstructor;
    list: {
        type: import("vue").PropType<TdTabsProps["list"]>;
    };
    placement: {
        type: import("vue").PropType<TdTabsProps["placement"]>;
        default: TdTabsProps["placement"];
        validator(val: TdTabsProps["placement"]): boolean;
    };
    scrollPosition: {
        type: import("vue").PropType<TdTabsProps["scrollPosition"]>;
        default: TdTabsProps["scrollPosition"];
        validator(val: TdTabsProps["scrollPosition"]): boolean;
    };
    size: {
        type: import("vue").PropType<TdTabsProps["size"]>;
        default: TdTabsProps["size"];
        validator(val: TdTabsProps["size"]): boolean;
    };
    theme: {
        type: import("vue").PropType<TdTabsProps["theme"]>;
        default: TdTabsProps["theme"];
        validator(val: TdTabsProps["theme"]): boolean;
    };
    value: {
        type: import("vue").PropType<TdTabsProps["value"]>;
        default: TdTabsProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<TdTabsProps["value"]>;
        default: TdTabsProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<TdTabsProps["defaultValue"]>;
    };
    onAdd: import("vue").PropType<TdTabsProps["onAdd"]>;
    onChange: import("vue").PropType<TdTabsProps["onChange"]>;
    onDragSort: import("vue").PropType<TdTabsProps["onDragSort"]>;
    onRemove: import("vue").PropType<TdTabsProps["onRemove"]>;
}>>, {
    value: TabValue;
    disabled: boolean;
    size: "medium" | "large";
    theme: "normal" | "card";
    modelValue: TabValue;
    placement: "left" | "right" | "top" | "bottom";
    dragSort: boolean;
    scrollPosition: "start" | "end" | "auto" | "center";
    addable: boolean;
}, {}>;
export default _default;
