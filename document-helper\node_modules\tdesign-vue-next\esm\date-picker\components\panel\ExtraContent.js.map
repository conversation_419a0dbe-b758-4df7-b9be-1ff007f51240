{"version": 3, "file": "ExtraContent.js", "sources": ["../../../../../components/date-picker/components/panel/ExtraContent.tsx"], "sourcesContent": ["import { defineComponent, PropType, computed } from 'vue';\nimport TDateFooter from '../base/Footer';\n\nimport type { TdDatePickerProps, TdDateRangePickerProps } from '../../type';\n\nexport default defineComponent({\n  name: 'TExtraContent',\n  props: {\n    presets: Object as PropType<TdDatePickerProps['presets'] | TdDateRangePickerProps['presets']>,\n    enableTimePicker: Boolean as PropType<TdDatePickerProps['enableTimePicker']>,\n    presetsPlacement: String as PropType<TdDatePickerProps['presetsPlacement']>,\n    needConfirm: { type: Boolean, default: true },\n    onPresetClick: Function,\n    onConfirmClick: Function,\n    selectedValue: [String, Number, Array, Date] as PropType<TdDatePickerProps['value']>,\n  },\n  setup(props) {\n    // 默认为 true\n    const showPanelFooter = computed(() => (props.enableTimePicker && props.needConfirm) || props.presets);\n\n    return () =>\n      showPanelFooter.value ? (\n        <TDateFooter\n          presets={props.presets}\n          onPresetClick={props.onPresetClick}\n          enableTimePicker={props.enableTimePicker}\n          onConfirmClick={props.onConfirmClick}\n          presetsPlacement={props.presetsPlacement}\n          selectedValue={props.selectedValue}\n          needConfirm={props.needConfirm}\n        />\n      ) : null;\n  },\n});\n"], "names": ["defineComponent", "name", "props", "presets", "Object", "enableTimePicker", "Boolean", "presetsPlacement", "String", "needConfirm", "type", "onPresetClick", "Function", "onConfirmClick", "selected<PERSON><PERSON><PERSON>", "Number", "Array", "Date", "setup", "show<PERSON><PERSON><PERSON><PERSON><PERSON>er", "computed", "value", "_createVNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,oBAAeA,eAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,eAAA;AACNC,EAAAA,KAAO,EAAA;AACLC,IAAAA,OAAS,EAAAC,MAAA;AACTC,IAAAA,gBAAkB,EAAAC,OAAA;AAClBC,IAAAA,gBAAkB,EAAAC,MAAA;AAClBC,IAAAA,WAAa,EAAA;AAAEC,MAAAA,IAAM,EAAAJ,OAAA;MAAS,SAAS,EAAA,IAAA;KAAK;AAC5CK,IAAAA,aAAe,EAAAC,QAAA;AACfC,IAAAA,cAAgB,EAAAD,QAAA;IAChBE,aAAe,EAAA,CAACN,MAAQ,EAAAO,MAAA,EAAQC,OAAOC,IAAI,CAAA;GAC7C;AACAC,EAAAA,OAAAA,SAAAA,MAAMhB,KAAO,EAAA;IAEL,IAAAiB,eAAA,GAAkBC,SAAS,YAAA;MAAA,OAAOlB,KAAA,CAAMG,oBAAoBH,KAAM,CAAAO,WAAA,IAAgBP,MAAMC,OAAO,CAAA;KAAA,CAAA,CAAA;IAE9F,OAAA,YAAA;AAAA,MAAA,OACLgB,eAAgB,CAAAE,KAAA,GAAAC,WAAA,CAAAC,WAAA,EAAA;QAAA,SAEHrB,EAAAA,KAAA,CAAMC,OACf;QAAA,eAAeD,EAAAA,KAAM,CAAAS,aAAA;QAAA,kBACHT,EAAAA,KAAA,CAAMG;wBACRH,EAAAA,KAAA,CAAMW,cACtB;QAAA,kBAAkBX,EAAAA,KAAM,CAAAK,gBAAA;QAAA,eACTL,EAAAA,KAAA,CAAMY,aACrB;AAAA,QAAA,aAAA,EAAaZ,KAAM,CAAAO,WAAAA;AAAA,OAAA,EAAA,IAAA,CAAA,GAEnB,IAAA,CAAA;AAAA,KAAA,CAAA;AACR,GAAA;AACF,CAAC,CAAA;;;;"}