{"version": 3, "file": "with-install.js", "sources": ["../../src/utils/with-install.ts"], "sourcesContent": ["import {\n  App, Plugin, Component,\n} from 'vue';\n\nfunction withInstall<T>(\n  comp: T,\n  alias?: string,\n): T & Plugin {\n  const componentPlugin = comp as T & Component & Plugin;\n\n  componentPlugin.install = (app: App, name?: string) => {\n    app.component(alias || name || componentPlugin.name, comp);\n  };\n\n  return componentPlugin as T & Plugin;\n}\n\nexport default withInstall;\n"], "names": ["comp", "alias", "componentPlugin", "install", "app", "name", "component"], "mappings": "AAIA,qBACEA,MACAC,OACY;MACNC,kBAAkBF;kBAERG,UAAU,CAACC,KAAUC,SAAkB;QACjDC,UAAUL,SAASI,QAAQH,gBAAgBG,MAAML;;SAGhDE;AAAA;;;;"}