{"version": 3, "file": "enhanced-table.mjs", "sources": ["../../../components/table/enhanced-table.tsx"], "sourcesContent": ["import { defineComponent, SetupContext, computed, ref, getCurrentInstance } from 'vue';\nimport baseTableProps from './base-table-props';\nimport primaryTableProps from './primary-table-props';\nimport enhancedTableProps from './enhanced-table-props';\nimport PrimaryTable from './primary-table';\nimport {\n  TdEnhancedTableProps,\n  PrimaryTableCol,\n  TableRowData,\n  DragSortContext,\n  TdPrimaryTableProps,\n  TableRowState,\n} from './type';\nimport useTreeData from './hooks/useTreeData';\nimport useTreeSelect from './hooks/useTreeSelect';\nimport { get } from 'lodash-es';\nimport { ComponentScrollToElementParams } from '../common';\nimport log from '@tdesign/common-js/log/index';\nimport { usePrefixClass } from '@tdesign/shared-hooks';\n\nexport default defineComponent({\n  name: 'TEnhancedTable',\n  props: {\n    ...baseTableProps,\n    ...primaryTableProps,\n    ...enhancedTableProps,\n  },\n  setup(props: TdEnhancedTableProps, context: SetupContext) {\n    const primaryTableRef = ref(null);\n    const { store, dataSource, formatTreeColumn, swapData, onExpandFoldIconClick, ...treeInstanceFunctions } =\n      useTreeData(props, context);\n    const classPrefix = usePrefixClass();\n\n    const treeDataMap = ref(store.value.treeDataMap);\n\n    const { tIndeterminateSelectedRowKeys, onInnerSelectChange } = useTreeSelect(props, treeDataMap);\n\n    // 影响列和单元格内容的因素有：树形节点需要添加操作符 [+] [-]\n    const getColumns = (columns: PrimaryTableCol<TableRowData>[]) => {\n      const arr: PrimaryTableCol<TableRowData>[] = [];\n      for (let i = 0, len = columns.length; i < len; i++) {\n        let item = { ...columns[i] };\n        item = formatTreeColumn(item);\n        if (item.children?.length) {\n          item.children = getColumns(item.children);\n        }\n        // 多级表头和自定义列配置特殊逻辑：要么子节点不存在，要么子节点长度大于 1，方便做自定义列配置\n        if (!item.children || item.children?.length) {\n          arr.push(item);\n        }\n      }\n      return arr;\n    };\n\n    const tColumns = computed(() => {\n      // 暂时只有树形结构需要处理 column.cell\n      const isTreeData = !props.tree || !Object.keys(props.tree).length;\n      return isTreeData ? props.columns : getColumns(props.columns);\n    });\n\n    const onDragSortChange = (params: DragSortContext<TableRowData>) => {\n      if (props.beforeDragSort && !props.beforeDragSort(params)) return;\n      swapData({\n        current: params.current,\n        target: params.target,\n        currentIndex: params.currentIndex,\n        targetIndex: params.targetIndex,\n      });\n      props.onDragSort?.(params);\n    };\n\n    const onEnhancedTableRowClick: TdPrimaryTableProps['onRowClick'] = (p) => {\n      if (props.tree?.expandTreeNodeOnClick) {\n        onExpandFoldIconClick(\n          {\n            row: p.row,\n            rowIndex: p.index,\n          },\n          'row-click',\n        );\n      }\n      props.onRowClick?.(p);\n    };\n\n    const getScrollRowIndex = (rowStateData: TableRowState, key: string | number): number => {\n      if (!rowStateData) return -1;\n      if (rowStateData.rowIndex >= 0) return rowStateData.rowIndex;\n      if (rowStateData.rowIndex < 0) {\n        return getScrollRowIndex(rowStateData.parent, key);\n      }\n    };\n\n    const scrollToElement = (params: ComponentScrollToElementParams) => {\n      let { index } = params;\n      if (!index && index !== 0) {\n        if (!params.key) {\n          log.error('Table', 'scrollToElement: one of `index` or `key` must exist.');\n          return;\n        }\n        const rowStateData = treeDataMap.value.get(params.key);\n        index = getScrollRowIndex(rowStateData, params.key);\n        if (index < 0 || index === undefined) {\n          log.error('Table', `${params.key} does not exist in data, check \\`rowKey\\` or \\`data\\` please.`);\n        }\n      }\n      primaryTableRef.value.scrollToElement({ ...params, index });\n    };\n\n    context.expose({\n      store: store.value,\n      dataSource: dataSource.value,\n      ...treeInstanceFunctions,\n      primaryTableRef,\n      validateRowData: (rowValue: any) => {\n        return primaryTableRef.value.validateRowData(rowValue);\n      },\n      validateTableData: () => {\n        return primaryTableRef.value.validateTableData();\n      },\n      clearValidateData: () => {\n        primaryTableRef.value.clearValidateData();\n      },\n      refreshTable: () => {\n        primaryTableRef.value.refreshTable();\n      },\n      scrollToElement,\n    });\n\n    return () => {\n      const { vnode } = getCurrentInstance();\n      const enhancedProps: TdPrimaryTableProps = {\n        ...vnode.props,\n        rowKey: props.rowKey || 'id',\n        data: dataSource.value,\n        columns: tColumns.value,\n        // 半选状态节点\n        indeterminateSelectedRowKeys: tIndeterminateSelectedRowKeys.value,\n        // 树形结构不允许本地数据分页\n        disableDataPage: Boolean(props.tree && Object.keys(props.tree).length) || props.disableDataPage,\n        onSelectChange: onInnerSelectChange,\n        onDragSort: onDragSortChange,\n        rowClassName: ({ row }) => {\n          const rowValue = get(row, props.rowKey || 'id');\n          const rowState = treeDataMap.value.get(rowValue);\n          if (!rowState) return [props.rowClassName];\n          return [`${classPrefix.value}-table-tr--level-${rowState.level}`, props.rowClassName];\n        },\n      };\n      if (props.tree?.expandTreeNodeOnClick) {\n        enhancedProps.onRowClick = onEnhancedTableRowClick;\n      }\n      // @ts-ignore ref 顺序很重要，如果移动到 v-slots 前面，会让 EnhancedTable 所有实例方法失效，勿动\n      return <PrimaryTable v-slots={context.slots} {...enhancedProps} ref={primaryTableRef} />;\n    };\n  },\n});\n"], "names": ["defineComponent", "name", "props", "_objectSpread", "baseTableProps", "primaryTableProps", "enhancedTableProps", "setup", "context", "primaryTableRef", "ref", "_useTreeData", "useTreeData", "store", "dataSource", "formatTreeColumn", "swapData", "onExpandFoldIconClick", "treeInstanceFunctions", "_objectWithoutProperties", "_excluded", "classPrefix", "usePrefixClass", "treeDataMap", "value", "_useTreeSelect", "useTreeSelect", "tIndeterminateSelectedRowKeys", "onInnerSelectChange", "getColumns", "columns", "arr", "i", "len", "length", "_item$children", "_item$children2", "item", "children", "push", "tColumns", "computed", "isTreeData", "tree", "Object", "keys", "onDragSortChange", "params", "_props$onDragSort", "beforeDragSort", "current", "target", "currentIndex", "targetIndex", "onDragSort", "call", "onEnhancedTableRowClick", "p", "_props$tree", "_props$onRowClick", "expandTreeNodeOnClick", "row", "rowIndex", "index", "onRowClick", "getScrollRowIndex", "rowStateData", "key", "parent", "scrollToElement", "log", "error", "get", "concat", "expose", "validateRowData", "rowValue", "validateTableData", "clearValidateData", "refreshTable", "_props$tree2", "_getCurrentInstance", "getCurrentInstance", "vnode", "enhancedProps", "<PERSON><PERSON><PERSON>", "data", "indeterminateSelectedRowKeys", "disableDataPage", "Boolean", "onSelectChange", "rowClassName", "_ref", "rowState", "level", "_createVNode", "PrimaryTable", "_mergeProps", "slots"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,qBAAeA,eAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,gBAAA;AACNC,EAAAA,KAAO,EAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,CACFC,EAAAA,EAAAA,cAAA,CACAC,EAAAA,iBAAA,CACAC,EAAAA,kBAAA,CACL;AACAC,EAAAA,KAAA,WAAAA,KAAAA,CAAML,OAA6BM,OAAuB,EAAA;AAClD,IAAA,IAAAC,eAAA,GAAkBC,IAAI,IAAI,CAAA,CAAA;AAC1B,IAAA,IAAAC,YAAA,GACJC,WAAY,CAAAV,KAAA,EAAOM,OAAO,CAAA;MADpBK,KAAO,GAAAF,YAAA,CAAPE,KAAO;MAAAC,UAAA,GAAAH,YAAA,CAAAG,UAAA;MAAYC,gBAAkB,GAAAJ,YAAA,CAAlBI,gBAAkB;MAAAC,QAAA,GAAAL,YAAA,CAAAK,QAAA;MAAUC,qCAAAA;AAA0BC,MAAAA,qBAAsB,GAAAC,wBAAA,CAAAR,YAAA,EAAAS,SAAA,CAAA,CAAA;AAEvG,IAAA,IAAMC,cAAcC,cAAe,EAAA,CAAA;IAEnC,IAAMC,WAAc,GAAAb,GAAA,CAAIG,KAAM,CAAAW,KAAA,CAAMD,WAAW,CAAA,CAAA;AAE/C,IAAA,IAAAE,cAAA,GAA+DC,aAAA,CAAcxB,OAAOqB,WAAW,CAAA;MAAvFI,6BAA+B,GAAAF,cAAA,CAA/BE,6BAA+B;MAAAC,mBAAA,GAAAH,cAAA,CAAAG,mBAAA,CAAA;AAGjC,IAAA,IAAAC,WAAA,GAAa,SAAbA,UAAAA,CAAcC,OAA6C,EAAA;MAC/D,IAAMC,MAAuC,EAAC,CAAA;AAC9C,MAAA,KAAA,IAASC,IAAI,CAAG,EAAAC,GAAA,GAAMH,QAAQI,MAAQ,EAAAF,CAAA,GAAIC,KAAKD,CAAK,EAAA,EAAA;QAAA,IAAAG,cAAA,EAAAC,eAAA,CAAA;QAClD,IAAIC,IAAO,GAAAlC,aAAA,CAAA,EAAA,EAAK2B,OAAA,CAAQE,CAAG,CAAA,CAAA,CAAA;AAC3BK,QAAAA,IAAA,GAAOtB,iBAAiBsB,IAAI,CAAA,CAAA;QACxB,IAAAF,CAAAA,cAAA,GAAAE,IAAA,CAAKC,yCAALH,KAAAA,CAAAA,IAAAA,cAAA,CAAeD,MAAQ,EAAA;UACpBG,IAAA,CAAAC,QAAA,GAAWT,WAAW,CAAAQ,IAAA,CAAKC,QAAQ,CAAA,CAAA;AAC1C,SAAA;AAEA,QAAA,IAAI,CAACD,IAAA,CAAKC,QAAY,IAAA,CAAAF,eAAA,GAAAC,IAAA,CAAKC,oDAALF,eAAA,CAAeF,MAAQ,EAAA;AAC3CH,UAAAA,GAAA,CAAIQ,KAAKF,IAAI,CAAA,CAAA;AACf,SAAA;AACF,OAAA;AACO,MAAA,OAAAN,GAAA,CAAA;KACT,CAAA;AAEM,IAAA,IAAAS,QAAA,GAAWC,SAAS,YAAM;AAExB,MAAA,IAAAC,UAAA,GAAa,CAACxC,KAAM,CAAAyC,IAAA,IAAQ,CAACC,MAAO,CAAAC,IAAA,CAAK3C,KAAM,CAAAyC,IAAI,CAAE,CAAAT,MAAA,CAAA;MAC3D,OAAOQ,UAAa,GAAAxC,KAAA,CAAM4B,OAAU,GAAAD,WAAA,CAAW3B,MAAM4B,OAAO,CAAA,CAAA;AAC9D,KAAC,CAAA,CAAA;AAEK,IAAA,IAAAgB,gBAAA,GAAmB,SAAnBA,gBAAAA,CAAoBC,MAA0C,EAAA;AAAA,MAAA,IAAAC,iBAAA,CAAA;MAClE,IAAI9C,KAAM,CAAA+C,cAAA,IAAkB,CAAC/C,KAAA,CAAM+C,eAAeF,MAAM,CAAA,EAAG,OAAA;AAClD/B,MAAAA,QAAA,CAAA;QACPkC,SAASH,MAAO,CAAAG,OAAA;QAChBC,QAAQJ,MAAO,CAAAI,MAAA;QACfC,cAAcL,MAAO,CAAAK,YAAA;QACrBC,aAAaN,MAAO,CAAAM,WAAAA;AACtB,OAAC,CAAA,CAAA;AACD,MAAA,CAAAL,iBAAA,GAAA9C,KAAA,CAAMoD,8CAANN,KAAAA,CAAAA,IAAAA,iBAAA,CAAAO,IAAA,CAAArD,KAAA,EAAmB6C,MAAM,CAAA,CAAA;KAC3B,CAAA;AAEM,IAAA,IAAAS,uBAAA,GAA6D,SAA7DA,uBAAAA,CAA8DC,CAAM,EAAA;MAAA,IAAAC,WAAA,EAAAC,iBAAA,CAAA;MACpE,IAAAD,CAAAA,WAAA,GAAAxD,KAAA,CAAMyC,kCAANe,KAAAA,CAAAA,IAAAA,WAAA,CAAYE,qBAAuB,EAAA;AACrC3C,QAAAA,qBAAA,CACE;UACE4C,KAAKJ,CAAE,CAAAI,GAAA;UACPC,UAAUL,CAAE,CAAAM,KAAAA;SACd,EACA,WACF,CAAA,CAAA;AACF,OAAA;AACA,MAAA,CAAAJ,iBAAA,GAAAzD,KAAA,CAAM8D,8CAANL,KAAAA,CAAAA,IAAAA,iBAAA,CAAAJ,IAAA,CAAArD,KAAA,EAAmBuD,CAAC,CAAA,CAAA;KACtB,CAAA;IAEM,IAAAQ,kBAAA,GAAoB,SAApBA,iBAAAA,CAAqBC,YAAA,EAA6BC,GAAiC,EAAA;AACvF,MAAA,IAAI,CAACD,YAAA,EAAqB,OAAA,CAAA,CAAA,CAAA;MAC1B,IAAIA,aAAaJ,QAAY,IAAA,CAAA,EAAG,OAAOI,YAAa,CAAAJ,QAAA,CAAA;AAChD,MAAA,IAAAI,YAAA,CAAaJ,WAAW,CAAG,EAAA;AACtB,QAAA,OAAAG,kBAAA,CAAkBC,YAAa,CAAAE,MAAA,EAAQD,GAAG,CAAA,CAAA;AACnD,OAAA;KACF,CAAA;AAEM,IAAA,IAAAE,eAAA,GAAkB,SAAlBA,eAAAA,CAAmBtB,MAA2C,EAAA;AAC9D,MAAA,IAAEgB,QAAUhB,MAAA,CAAVgB;AACF,MAAA,IAAA,CAACA,KAAS,IAAAA,KAAA,KAAU,CAAG,EAAA;AACrB,QAAA,IAAA,CAAChB,OAAOoB,GAAK,EAAA;AACXG,UAAAA,GAAA,CAAAC,KAAA,CAAM,SAAS,sDAAsD,CAAA,CAAA;AACzE,UAAA,OAAA;AACF,SAAA;QACA,IAAML,YAAe,GAAA3C,WAAA,CAAYC,KAAM,CAAAgD,GAAA,CAAIzB,OAAOoB,GAAG,CAAA,CAAA;QAC7CJ,KAAA,GAAAE,kBAAA,CAAkBC,YAAc,EAAAnB,MAAA,CAAOoB,GAAG,CAAA,CAAA;QAC9C,IAAAJ,KAAA,GAAQ,CAAK,IAAAA,KAAA,KAAU,KAAW,CAAA,EAAA;UACpCO,GAAA,CAAIC,KAAM,CAAA,OAAA,EAAA,EAAA,CAAAE,MAAA,CAAY1B,MAAA,CAAOoB,GAAkE,EAAA,2DAAA,CAAA,CAAA,CAAA;AACjG,SAAA;AACF,OAAA;MACA1D,eAAA,CAAgBe,MAAM6C,eAAgB,CAAAlE,aAAA,CAAAA,aAAA,CAAA,EAAA,EAAK4C,MAAA,CAAA,EAAA,EAAA,EAAA;AAAQgB,QAAAA,OAAAA,KAAAA;QAAO,CAAA,CAAA;KAC5D,CAAA;AAEAvD,IAAAA,OAAA,CAAQkE,MAAO,CAAAvE,aAAA,CAAAA,aAAA,CAAA;MACbU,OAAOA,KAAM,CAAAW,KAAA;MACbV,YAAYA,UAAW,CAAAU,KAAAA;AAAA,KAAA,EACpBN,qBAAA,CAAA,EAAA,EAAA,EAAA;AACHT,MAAAA,eAAA,EAAAA,eAAA;AACAkE,MAAAA,eAAA,EAAiB,SAAjBA,eAAAA,CAAkBC,QAAkB,EAAA;AAC3B,QAAA,OAAAnE,eAAA,CAAgBe,KAAM,CAAAmD,eAAA,CAAgBC,QAAQ,CAAA,CAAA;OACvD;AACAC,MAAAA,mBAAmB,SAAnBA,oBAAyB;AAChB,QAAA,OAAApE,eAAA,CAAgBe,MAAMqD,iBAAkB,EAAA,CAAA;OACjD;AACAC,MAAAA,mBAAmB,SAAnBA,oBAAyB;AACvBrE,QAAAA,eAAA,CAAgBe,MAAMsD,iBAAkB,EAAA,CAAA;OAC1C;AACAC,MAAAA,cAAc,SAAdA,eAAoB;AAClBtE,QAAAA,eAAA,CAAgBe,MAAMuD,YAAa,EAAA,CAAA;OACrC;AACAV,MAAAA,eAAA,EAAAA,eAAAA;AAAA,KAAA,CACD,CAAA,CAAA;AAED,IAAA,OAAO,YAAM;AAAA,MAAA,IAAAW,YAAA,CAAA;AACL,MAAA,IAAAC,mBAAA,GAAYC,kBAAmB,EAAA;QAA7BC,KAAM,GAAAF,mBAAA,CAANE,KAAM,CAAA;MACd,IAAMC,aAAqC,GAAAjF,aAAA,CAAAA,aAAA,CACtCgF,EAAAA,EAAAA,KAAM,CAAAjF,KAAA,CAAA,EAAA,EAAA,EAAA;AACTmF,QAAAA,MAAA,EAAQnF,MAAMmF,MAAU,IAAA,IAAA;QACxBC,MAAMxE,UAAW,CAAAU,KAAA;QACjBM,SAASU,QAAS,CAAAhB,KAAA;QAElB+D,8BAA8B5D,6BAA8B,CAAAH,KAAA;QAE5DgE,eAAA,EAAiBC,OAAQ,CAAAvF,KAAA,CAAMyC,IAAQ,IAAAC,MAAA,CAAOC,IAAK,CAAA3C,KAAA,CAAMyC,IAAI,CAAA,CAAET,MAAM,CAAA,IAAKhC,KAAM,CAAAsF,eAAA;AAChFE,QAAAA,cAAgB,EAAA9D,mBAAA;AAChB0B,QAAAA,UAAY,EAAAR,gBAAA;AACZ6C,QAAAA,YAAc,EAAA,SAAdA,YAAcA,CAAAC,IAAA,EAAa;AAAA,UAAA,IAAV/B,GAAA,GAAA+B,IAAA,CAAA/B,GAAA,CAAA;UACf,IAAMe,QAAW,GAAAJ,GAAA,CAAIX,GAAK,EAAA3D,KAAA,CAAMmF,UAAU,IAAI,CAAA,CAAA;UAC9C,IAAMQ,QAAW,GAAAtE,WAAA,CAAYC,KAAM,CAAAgD,GAAA,CAAII,QAAQ,CAAA,CAAA;UAC/C,IAAI,CAACiB,QAAA,EAAiB,OAAA,CAAC3F,MAAMyF,YAAY,CAAA,CAAA;AACzC,UAAA,OAAO,IAAAlB,MAAA,CAAIpD,WAAA,CAAYG,mCAAyBqE,QAAS,CAAAC,KAAA,CAAA,EAAS5F,MAAMyF,YAAY,CAAA,CAAA;AACtF,SAAA;OACF,CAAA,CAAA;MACI,IAAAX,CAAAA,YAAA,GAAA9E,KAAA,CAAMyC,mCAANqC,KAAAA,CAAAA,IAAAA,YAAA,CAAYpB,qBAAuB,EAAA;QACrCwB,aAAA,CAAcpB,UAAa,GAAAR,uBAAA,CAAA;AAC7B,OAAA;AAEA,MAAA,OAAAuC,WAAA,CAAAC,aAAA,EAAAC,UAAA,CAAiDb,aAAA,EAAA;QAAA,KAAoB3E,EAAAA,eAAAA;OAAvCD,CAAAA,EAAAA,QAAQ0F,KAAW,CAAA,CAAA;KACnD,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}