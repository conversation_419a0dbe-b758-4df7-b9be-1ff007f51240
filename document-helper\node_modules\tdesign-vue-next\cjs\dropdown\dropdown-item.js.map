{"version": 3, "file": "dropdown-item.js", "sources": ["../../../components/dropdown/dropdown-item.tsx"], "sourcesContent": ["import { defineComponent, ref, PropType } from 'vue';\nimport { TdDropdownProps } from '../dropdown/type';\n\nimport dropdownItemProps from './dropdown-item-props';\nimport { useRipple, useContent, useTNodeJSX, usePrefixClass } from '@tdesign/shared-hooks';\n\nimport { pxCompat } from '@tdesign/common-js/utils/helper';\n\nexport default defineComponent({\n  name: 'TDropdownItem',\n  props: {\n    ...dropdownItemProps,\n    maxColumnWidth: {\n      type: [String, Number] as PropType<TdDropdownProps['maxColumnWidth']>,\n      default: 100,\n    },\n    minColumnWidth: {\n      type: [String, Number] as PropType<TdDropdownProps['minColumnWidth']>,\n      default: 10,\n    },\n    isSubmenu: Boolean,\n  },\n  setup(props) {\n    const renderTNodeJSX = useTNodeJSX();\n    const renderContent = useContent();\n\n    const itemRef = ref<HTMLElement>();\n\n    useRipple(props.isSubmenu ? null : itemRef);\n    const prefixIcon = renderTNodeJSX('prefixIcon');\n    const dropdownItemClass = usePrefixClass('dropdown__item');\n    const handleItemClick = (e: MouseEvent) => {\n      if (props.disabled) return;\n      props.onClick?.(props.value, {\n        e,\n      });\n    };\n\n    return () => {\n      const content = renderContent('default', 'content');\n      const classes = [\n        dropdownItemClass.value,\n        `${dropdownItemClass.value}--theme-${props.theme}`,\n        {\n          [`${dropdownItemClass.value}--active`]: props.active,\n          [`${dropdownItemClass.value}--disabled`]: props.disabled,\n        },\n      ];\n\n      return (\n        <li\n          class={classes}\n          onClick={handleItemClick}\n          style={{\n            maxWidth: pxCompat(props.maxColumnWidth),\n            minWidth: pxCompat(props.minColumnWidth),\n          }}\n          ref={itemRef}\n        >\n          {props.prefixIcon ? <div class={`${dropdownItemClass.value}-icon`}>{prefixIcon}</div> : null}\n          {content}\n        </li>\n      );\n    };\n  },\n});\n"], "names": ["defineComponent", "name", "props", "_objectSpread", "dropdownItemProps", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "String", "Number", "minColumn<PERSON>idth", "isSubmenu", "Boolean", "setup", "renderTNodeJSX", "useTNodeJSX", "renderContent", "useContent", "itemRef", "ref", "useRipple", "prefixIcon", "dropdownItemClass", "usePrefixClass", "handleItemClick", "e", "_props$onClick", "disabled", "onClick", "call", "value", "content", "classes", "concat", "theme", "_defineProperty", "active", "_createVNode", "max<PERSON><PERSON><PERSON>", "pxCompat", "min<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,oBAAeA,mBAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,eAAA;AACNC,EAAAA,KAAO,EAAAC,aAAA,CAAAA,aAAA,KACFC,qCAAA,CAAA,EAAA,EAAA,EAAA;AACHC,IAAAA,cAAgB,EAAA;AACdC,MAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,MAAM,CAAA;MACrB,SAAS,EAAA,GAAA;KACX;AACAC,IAAAA,cAAgB,EAAA;AACdH,MAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,MAAM,CAAA;MACrB,SAAS,EAAA,EAAA;KACX;AACAE,IAAAA,SAAW,EAAAC,OAAAA;GACb,CAAA;AACAC,EAAAA,OAAAA,SAAAA,MAAMV,KAAO,EAAA;AACX,IAAA,IAAMW,iBAAiBC,iBAAY,EAAA,CAAA;AACnC,IAAA,IAAMC,gBAAgBC,gBAAW,EAAA,CAAA;AAEjC,IAAA,IAAMC,UAAUC,OAAiB,EAAA,CAAA;IAEvBC,iBAAA,CAAAjB,KAAA,CAAMQ,SAAY,GAAA,IAAA,GAAOO,OAAO,CAAA,CAAA;AACpC,IAAA,IAAAG,UAAA,GAAaP,eAAe,YAAY,CAAA,CAAA;AACxC,IAAA,IAAAQ,iBAAA,GAAoBC,uBAAe,gBAAgB,CAAA,CAAA;AACnD,IAAA,IAAAC,eAAA,GAAkB,SAAlBA,eAAAA,CAAmBC,CAAkB,EAAA;AAAA,MAAA,IAAAC,cAAA,CAAA;MACzC,IAAIvB,KAAM,CAAAwB,QAAA,EAAU,OAAA;AACd,MAAA,CAAAD,cAAA,GAAAvB,KAAA,CAAAyB,OAAA,cAAAF,cAAA,KAAA,KAAA,CAAA,IAAAA,cAAA,CAAAG,IAAA,CAAA1B,KAAA,EAAUA,MAAM2B,KAAO,EAAA;AAC3BL,QAAAA,CAAA,EAAAA,CAAAA;AACF,OAAC,CAAA,CAAA;KACH,CAAA;AAEA,IAAA,OAAO,YAAM;AACL,MAAA,IAAAM,OAAA,GAAUf,aAAc,CAAA,SAAA,EAAW,SAAS,CAAA,CAAA;AAClD,MAAA,IAAMgB,OAAU,GAAA,CACdV,iBAAkB,CAAAQ,KAAA,KAAAG,MAAA,CACfX,iBAAkB,CAAAQ,KAAA,cAAAG,MAAA,CAAgB9B,KAAM,CAAA+B,KAAA,GAAAC,mCAAA,CAAAA,mCAAA,CAAA,EAAA,EAAA,EAAA,CAAAF,MAAA,CAErCX,iBAAkB,CAAAQ,KAAA,EAAA,UAAA,CAAA,EAAkB3B,KAAM,CAAAiC,MAAA,MAAAH,MAAA,CAC1CX,iBAAkB,CAAAQ,KAAA,iBAAoB3B,KAAM,CAAAwB,QAAA,CAEpD,CAAA,CAAA;AAEA,MAAA,OAAAU,eAAA,CAAA,IAAA,EAAA;AAAA,QAAA,OAAA,EAEWL,OACP;AAAA,QAAA,SAAA,EAASR;eACF,EAAA;AACLc,UAAAA,QAAA,EAAUC,eAAS,CAAApC,KAAA,CAAMG,cAAc,CAAA;AACvCkC,UAAAA,QAAA,EAAUD,eAAS,CAAApC,KAAA,CAAMO,cAAc,CAAA;;aAEpCQ,EAAAA,OAAAA;AAAA,OAAA,EAAA,CAEJf,KAAA,CAAMkB,UAAa,GAAAgB,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAA,EAAA,CAAAJ,MAAA,CAAeX,iBAAkB,CAAAQ,KAAA,EAAA,OAAA,CAAA;AAAA,OAAA,EAAA,CAAeT,UAAW,CAAA,CAAA,GAAS,IAAA,EACvFU,OAAA,CAAA,CAAA,CAAA;KAGP,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}