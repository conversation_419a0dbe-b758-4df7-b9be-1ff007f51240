{"version": 3, "file": "EmptySvg.mjs", "sources": ["../../../../components/empty/components/EmptySvg.tsx"], "sourcesContent": ["import { defineComponent } from 'vue';\n\nexport default defineComponent({\n  name: 'EmptySvg',\n  setup() {\n    return () => (\n      <svg width=\"1em\" height=\"1em\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n        <g id=\"&#230;&#151;&#160;&#231;&#187;&#147;&#230;&#158;&#156;-no-result\">\n          <g id=\"Union\">\n            <path d=\"M22 0H26V8H22V0Z\" fill=\"var(--td-text-color-placeholder)\" />\n            <path\n              d=\"M10.002 1.17157L7.17353 4L13.002 9.82843L15.8304 7L10.002 1.17157Z\"\n              fill=\"var(--td-text-color-placeholder)\"\n            />\n            <path\n              fill-rule=\"evenodd\"\n              clip-rule=\"evenodd\"\n              d=\"M2 27.4689L10.8394 12H37.1606L46 27.4689V44H2V27.4689ZM13.1606 16L7.44636 26H17.8025L18.1889 27.5015C18.8551 30.0898 21.207 32 24 32C26.793 32 29.1449 30.0898 29.8111 27.5015L30.1975 26H40.5536L34.8394 16H13.1606Z\"\n              fill=\"var(--td-text-color-placeholder)\"\n            />\n            <path\n              d=\"M37.998 1.17157L32.1696 7L34.998 9.82843L40.8265 4L37.998 1.17157Z\"\n              fill=\"var(--td-text-color-placeholder)\"\n            />\n          </g>\n        </g>\n      </svg>\n    );\n  },\n});\n"], "names": ["defineComponent", "name", "setup", "_createVNode"], "mappings": ";;;;;;;;AAEA,eAAeA,eAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,UAAA;EACNC,KAAQ,EAAA,SAARA,KAAQA,GAAA;IACN,OAAO,YAAA;AAAA,MAAA,OAAAC,WAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAA,KAAA;AAAA,QAAA,QAAA,EAAA,KAAA;AAAA,QAAA,SAAA,EAAA,WAAA;AAAA,QAAA,MAAA,EAAA,MAAA;AAAA,QAAA,OAAA,EAAA,4BAAA;AAAA,OAAA,EAAA,CAAAA,WAAA,CAAA,GAAA,EAAA;QAAA,IAEG,EAAA,gDAAA;AACJ,OAAA,EAAA,CAAAA,WAAA,CAAA,GAAA,EAAA;AAAA,QAAA,IAAA,EAAA,OAAA;AAAA,OAAA,EAAA,CAAAA,WAAA,CAAA,MAAA,EAAA;AAAA,QAAA,GAAA,EAAA,kBAAA;AAAA,QAAA,MAAA,EAAA,kCAAA;AAAA,OAAA,EAAA,IAAA,CAAA,EAAAA,WAAA,CAAA,MAAA,EAAA;AAAA,QAAA,GAAA,EAAA,oEAAA;AAAA,QAAA,MAAA,EAAA,kCAAA;AAAA,OAAA,EAAA,IAAA,CAAA,EAAAA,WAAA,CAAA,MAAA,EAAA;AAAA,QAAA,WAAA,EAAA,SAAA;AAAA,QAAA,WAAA,EAAA,SAAA;AAAA,QAAA,GAAA,EAAA,uNAAA;AAAA,QAAA,MAAA,EAAA,kCAAA;AAAA,OAAA,EAAA,IAAA,CAAA,EAAAA,WAAA,CAAA,MAAA,EAAA;AAAA,QAAA,GAAA,EAAA,oEAAA;AAAA,QAAA,MAAA,EAAA,kCAAA;AAAA,OAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;KAFH,CAAA;AAsBL,GAAA;AACF,CAAC,CAAA;;;;"}