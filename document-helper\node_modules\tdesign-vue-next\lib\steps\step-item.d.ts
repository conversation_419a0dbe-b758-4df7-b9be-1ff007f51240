declare const _default: import("vue").DefineComponent<{
    index: NumberConstructor;
    content: {
        type: import("vue").PropType<import("./type").TdStepItemProps["content"]>;
        default: import("./type").TdStepItemProps["content"];
    };
    default: {
        type: import("vue").PropType<import("./type").TdStepItemProps["default"]>;
    };
    extra: {
        type: import("vue").PropType<import("./type").TdStepItemProps["extra"]>;
    };
    icon: {
        type: import("vue").PropType<import("./type").TdStepItemProps["icon"]>;
        default: import("./type").TdStepItemProps["icon"];
    };
    status: {
        type: import("vue").PropType<import("./type").TdStepItemProps["status"]>;
        default: import("./type").TdStepItemProps["status"];
        validator(val: import("./type").TdStepItemProps["status"]): boolean;
    };
    title: {
        type: import("vue").PropType<import("./type").TdStepItemProps["title"]>;
        default: import("./type").TdStepItemProps["title"];
    };
    value: {
        type: import("vue").PropType<import("./type").TdStepItemProps["value"]>;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    index: NumberConstructor;
    content: {
        type: import("vue").PropType<import("./type").TdStepItemProps["content"]>;
        default: import("./type").TdStepItemProps["content"];
    };
    default: {
        type: import("vue").PropType<import("./type").TdStepItemProps["default"]>;
    };
    extra: {
        type: import("vue").PropType<import("./type").TdStepItemProps["extra"]>;
    };
    icon: {
        type: import("vue").PropType<import("./type").TdStepItemProps["icon"]>;
        default: import("./type").TdStepItemProps["icon"];
    };
    status: {
        type: import("vue").PropType<import("./type").TdStepItemProps["status"]>;
        default: import("./type").TdStepItemProps["status"];
        validator(val: import("./type").TdStepItemProps["status"]): boolean;
    };
    title: {
        type: import("vue").PropType<import("./type").TdStepItemProps["title"]>;
        default: import("./type").TdStepItemProps["title"];
    };
    value: {
        type: import("vue").PropType<import("./type").TdStepItemProps["value"]>;
    };
}>>, {
    status: import("./type").StepStatus;
    title: string | ((h: typeof import("vue").h) => import("..").TNodeReturnValue);
    icon: boolean | ((h: typeof import("vue").h) => import("..").TNodeReturnValue);
    content: string | ((h: typeof import("vue").h) => import("..").TNodeReturnValue);
}, {}>;
export default _default;
