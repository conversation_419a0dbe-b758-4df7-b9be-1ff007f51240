declare const _default: import("vue").DefineComponent<{
    inputValue: {
        type: import("vue").PropType<import("..").TdSelectProps["inputValue"]>;
        default: import("..").TdSelectProps["inputValue"];
    };
    panelTopContent: {
        type: import("vue").PropType<import("..").TdSelectProps["panelTopContent"]>;
    };
    panelBottomContent: {
        type: import("vue").PropType<import("..").TdSelectProps["panelBottomContent"]>;
    };
    empty: {
        type: import("vue").PropType<import("..").TdSelectProps["empty"]>;
    };
    creatable: BooleanConstructor;
    loading: BooleanConstructor;
    loadingText: {
        type: import("vue").PropType<import("..").TdSelectProps["loadingText"]>;
    };
    multiple: BooleanConstructor;
    filterable: BooleanConstructor;
    filter: {
        type: import("vue").PropType<import("..").TdSelectProps["filter"]>;
    };
    scroll: {
        type: import("vue").PropType<import("..").TdSelectProps["scroll"]>;
    };
    size: {
        type: import("vue").PropType<import("..").TdSelectProps["size"]>;
        default: import("..").TdSelectProps["size"];
        validator(val: import("..").TdSelectProps["size"]): boolean;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    inputValue: {
        type: import("vue").PropType<import("..").TdSelectProps["inputValue"]>;
        default: import("..").TdSelectProps["inputValue"];
    };
    panelTopContent: {
        type: import("vue").PropType<import("..").TdSelectProps["panelTopContent"]>;
    };
    panelBottomContent: {
        type: import("vue").PropType<import("..").TdSelectProps["panelBottomContent"]>;
    };
    empty: {
        type: import("vue").PropType<import("..").TdSelectProps["empty"]>;
    };
    creatable: BooleanConstructor;
    loading: BooleanConstructor;
    loadingText: {
        type: import("vue").PropType<import("..").TdSelectProps["loadingText"]>;
    };
    multiple: BooleanConstructor;
    filterable: BooleanConstructor;
    filter: {
        type: import("vue").PropType<import("..").TdSelectProps["filter"]>;
    };
    scroll: {
        type: import("vue").PropType<import("..").TdSelectProps["scroll"]>;
    };
    size: {
        type: import("vue").PropType<import("..").TdSelectProps["size"]>;
        default: import("..").TdSelectProps["size"];
        validator(val: import("..").TdSelectProps["size"]): boolean;
    };
}>>, {
    multiple: boolean;
    size: import("../..").SizeEnum;
    loading: boolean;
    inputValue: string;
    creatable: boolean;
    filterable: boolean;
}, {}>;
export default _default;
