declare const _default: import("vue").DefineComponent<{
    labelAlign: {
        type: import("vue").PropType<import("./type").TdTimelineProps["labelAlign"]>;
        default: import("./type").TdTimelineProps["labelAlign"];
        validator(val: import("./type").TdTimelineProps["labelAlign"]): boolean;
    };
    layout: {
        type: import("vue").PropType<import("./type").TdTimelineProps["layout"]>;
        default: import("./type").TdTimelineProps["layout"];
        validator(val: import("./type").TdTimelineProps["layout"]): boolean;
    };
    mode: {
        type: import("vue").PropType<import("./type").TdTimelineProps["mode"]>;
        default: import("./type").TdTimelineProps["mode"];
        validator(val: import("./type").TdTimelineProps["mode"]): boolean;
    };
    reverse: BooleanConstructor;
    theme: {
        type: import("vue").PropType<import("./type").TdTimelineProps["theme"]>;
        default: import("./type").TdTimelineProps["theme"];
        validator(val: import("./type").TdTimelineProps["theme"]): boolean;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    labelAlign: {
        type: import("vue").PropType<import("./type").TdTimelineProps["labelAlign"]>;
        default: import("./type").TdTimelineProps["labelAlign"];
        validator(val: import("./type").TdTimelineProps["labelAlign"]): boolean;
    };
    layout: {
        type: import("vue").PropType<import("./type").TdTimelineProps["layout"]>;
        default: import("./type").TdTimelineProps["layout"];
        validator(val: import("./type").TdTimelineProps["layout"]): boolean;
    };
    mode: {
        type: import("vue").PropType<import("./type").TdTimelineProps["mode"]>;
        default: import("./type").TdTimelineProps["mode"];
        validator(val: import("./type").TdTimelineProps["mode"]): boolean;
    };
    reverse: BooleanConstructor;
    theme: {
        type: import("vue").PropType<import("./type").TdTimelineProps["theme"]>;
        default: import("./type").TdTimelineProps["theme"];
        validator(val: import("./type").TdTimelineProps["theme"]): boolean;
    };
}>>, {
    reverse: boolean;
    mode: "same" | "alternate";
    theme: "default" | "dot";
    layout: "vertical" | "horizontal";
    labelAlign: "left" | "right" | "top" | "bottom" | "alternate";
}, {}>;
export default _default;
