'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var defineProperty = require('../_chunks/dep-304d0f8c.js');
var vue = require('vue');
var utils_renderFn = require('../utils/render-fn.js');
var utils_useSizeProps = require('../utils/use-size-props.js');
require('../utils/use-common-classname.js');
require('../utils/config-context.js');

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { defineProperty._defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var element = {
  "tag": "svg",
  "attrs": {
    "fill": "none",
    "viewBox": "0 0 24 24",
    "width": "1em",
    "height": "1em"
  },
  "children": [{
    "tag": "g",
    "attrs": {
      "clipPath": "url(#clip0_8726_7450)"
    },
    "children": [{
      "tag": "path",
      "attrs": {
        "fill": "currentColor",
        "d": "M22 11.9993C22 9.56298 21.13 7.33251 19.6828 5.59765L19.0422 4.82977L20.5779 3.54858L21.2185 4.31646C22.9546 6.39749 24 9.07761 24 11.9993C24 14.9209 22.9546 17.601 21.2185 19.6821L20.5779 20.4499L19.0422 19.1688L19.6828 18.4009C21.13 16.666 22 14.4355 22 11.9993ZM17.3773 7.51746C18.3899 8.73125 19 10.2953 19 11.9993C19 13.7032 18.3899 15.2673 17.3773 16.4811L16.7367 17.2489L15.201 15.9677L15.8416 15.1999C16.5654 14.3323 17 13.2178 17 11.9993C17 10.7807 16.5654 9.66626 15.8416 8.79866L15.201 8.03078L16.7367 6.74959L17.3773 7.51746ZM12 10C13.1046 10 14 10.8955 14 12C14 13.1046 13.1046 14 12 14C10.8954 14 10 13.1046 10 12C10 10.8955 10.8954 10 12 10ZM8.79903 8.03078L8.15844 8.79866C7.43464 9.66626 7 10.7807 7 11.9993C7 13.2178 7.43464 14.3323 8.15844 15.1999L8.79903 15.9677L7.26327 17.2489L6.62268 16.4811C5.61009 15.2673 5 13.7032 5 11.9993C5 10.2953 5.61009 8.73125 6.62268 7.51747L7.26327 6.74959L8.79903 8.03078ZM4.95783 4.82977L4.31723 5.59765C2.86995 7.33251 2 9.56298 2 11.9993C2 14.4355 2.86995 16.666 4.31723 18.4009L4.95783 19.1687L3.42207 20.4499L2.78147 19.6821C1.0454 17.601 -1.2771e-07 14.9209 0 11.9993C1.2771e-07 9.07761 1.0454 6.39749 2.78148 4.31646L3.42207 3.54858L4.95783 4.82977Z"
      }
    }]
  }]
};
var sensors = vue.defineComponent({
  name: "SensorsIcon",
  props: {
    size: {
      type: String
    },
    onClick: {
      type: Function
    }
  },
  setup(props, _ref) {
    var {
      attrs
    } = _ref;
    var propsSize = vue.computed(() => props.size);
    var {
      className,
      style
    } = utils_useSizeProps['default'](propsSize);
    var finalCls = vue.computed(() => ["t-icon", "t-icon-sensors", className.value]);
    var finalStyle = vue.computed(() => _objectSpread(_objectSpread({}, style.value), attrs.style));
    var finalProps = vue.computed(() => ({
      class: finalCls.value,
      style: finalStyle.value,
      onClick: e => {
        var _props$onClick;
        return (_props$onClick = props.onClick) === null || _props$onClick === void 0 ? void 0 : _props$onClick.call(props, {
          e
        });
      }
    }));
    return () => utils_renderFn['default'](element, finalProps.value);
  }
});

exports.default = sensors;
//# sourceMappingURL=sensors.js.map
