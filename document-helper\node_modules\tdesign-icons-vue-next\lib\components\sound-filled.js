'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var defineProperty = require('../_chunks/dep-304d0f8c.js');
var vue = require('vue');
var utils_renderFn = require('../utils/render-fn.js');
var utils_useSizeProps = require('../utils/use-size-props.js');
require('../utils/use-common-classname.js');
require('../utils/config-context.js');

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { defineProperty._defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var element = {
  "tag": "svg",
  "attrs": {
    "fill": "none",
    "viewBox": "0 0 24 24",
    "width": "1em",
    "height": "1em"
  },
  "children": [{
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M5 6.4998V17.4998L1 17.5 1 6.5 5 6.4998zM7 18.2099L15.0004 22.7095 15.0004 1.29004 7 5.7896V18.2099zM21.5814 7.77945L20.9788 6.98145 19.3828 8.18675 19.9854 8.98475C20.6412 9.85308 20.9958 10.9116 20.9954 11.9998 20.995 13.0879 20.6397 14.1462 19.9833 15.0141L19.3801 15.8116 20.9753 17.0181 21.5785 16.2205C22.4974 15.0054 22.9949 13.5238 22.9954 12.0005 22.9959 10.4771 22.4995 8.99511 21.5814 7.77945z"
    }
  }, {
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M18.7887 9.88858L18.1861 9.09058L16.5901 10.2959L17.1927 11.0939C17.3894 11.3544 17.4958 11.6719 17.4957 11.9984C17.4956 12.3248 17.389 12.6423 17.1921 12.9027L16.5889 13.7003L18.184 14.9067L18.7872 14.1091C19.2467 13.5016 19.4954 12.7608 19.4957 11.9991C19.496 11.2374 19.2478 10.4964 18.7887 9.88858Z"
    }
  }]
};
var soundFilled = vue.defineComponent({
  name: "SoundFilledIcon",
  props: {
    size: {
      type: String
    },
    onClick: {
      type: Function
    }
  },
  setup(props, _ref) {
    var {
      attrs
    } = _ref;
    var propsSize = vue.computed(() => props.size);
    var {
      className,
      style
    } = utils_useSizeProps['default'](propsSize);
    var finalCls = vue.computed(() => ["t-icon", "t-icon-sound-filled", className.value]);
    var finalStyle = vue.computed(() => _objectSpread(_objectSpread({}, style.value), attrs.style));
    var finalProps = vue.computed(() => ({
      class: finalCls.value,
      style: finalStyle.value,
      onClick: e => {
        var _props$onClick;
        return (_props$onClick = props.onClick) === null || _props$onClick === void 0 ? void 0 : _props$onClick.call(props, {
          e
        });
      }
    }));
    return () => utils_renderFn['default'](element, finalProps.value);
  }
});

exports.default = soundFilled;
//# sourceMappingURL=sound-filled.js.map
