import { TdTabPanelProps } from '../tabs/type';
import { PropType } from 'vue';
declare const _default: {
    default: {
        type: PropType<TdTabPanelProps["default"]>;
    };
    destroyOnHide: {
        type: BooleanConstructor;
        default: boolean;
    };
    disabled: BooleanConstructor;
    draggable: {
        type: BooleanConstructor;
        default: boolean;
    };
    label: {
        type: PropType<TdTabPanelProps["label"]>;
    };
    lazy: BooleanConstructor;
    panel: {
        type: PropType<TdTabPanelProps["panel"]>;
    };
    removable: BooleanConstructor;
    value: {
        type: PropType<TdTabPanelProps["value"]>;
    };
    onRemove: PropType<TdTabPanelProps["onRemove"]>;
};
export default _default;
