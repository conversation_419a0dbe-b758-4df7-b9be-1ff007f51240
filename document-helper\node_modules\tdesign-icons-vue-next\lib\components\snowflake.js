'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var defineProperty = require('../_chunks/dep-304d0f8c.js');
var vue = require('vue');
var utils_renderFn = require('../utils/render-fn.js');
var utils_useSizeProps = require('../utils/use-size-props.js');
require('../utils/use-common-classname.js');
require('../utils/config-context.js');

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { defineProperty._defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var element = {
  "tag": "svg",
  "attrs": {
    "fill": "none",
    "viewBox": "0 0 24 24",
    "width": "1em",
    "height": "1em"
  },
  "children": [{
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M13 1V2.58579L14 1.58579L15.4142 3L13 5.41421L13 9.61712L16.943 5.6361L18.3639 7.0435L14.4143 11.0313L18.583 11.0028L21 8.58579L22.4142 10L21.4142 11H23V13H21.4142L22.4142 14L21 15.4142L18.5886 13.0028L14.4142 13.0314L18.3639 16.9429L16.9566 18.364L13 14.4456L13 18.5858L15.4142 21L14 22.4142L13 21.4142L13 23L11 23L11 21.4142L10 22.4142L8.58579 21L11 18.5858L11 14.4456L7.04339 18.364L5.63606 16.9429L9.58576 13.0314L5.41138 13.0028L3 15.4142L1.58579 14L2.58579 13L1 13L1 11L2.58578 11L1.58579 10L3 8.58579L5.41704 11.0028L9.58573 11.0313L5.63606 7.0435L7.05705 5.6361L11 9.61712L11 5.41421L8.58579 3L10 1.58579L11 2.58578V1L13 1Z"
    }
  }]
};
var snowflake = vue.defineComponent({
  name: "SnowflakeIcon",
  props: {
    size: {
      type: String
    },
    onClick: {
      type: Function
    }
  },
  setup(props, _ref) {
    var {
      attrs
    } = _ref;
    var propsSize = vue.computed(() => props.size);
    var {
      className,
      style
    } = utils_useSizeProps['default'](propsSize);
    var finalCls = vue.computed(() => ["t-icon", "t-icon-snowflake", className.value]);
    var finalStyle = vue.computed(() => _objectSpread(_objectSpread({}, style.value), attrs.style));
    var finalProps = vue.computed(() => ({
      class: finalCls.value,
      style: finalStyle.value,
      onClick: e => {
        var _props$onClick;
        return (_props$onClick = props.onClick) === null || _props$onClick === void 0 ? void 0 : _props$onClick.call(props, {
          e
        });
      }
    }));
    return () => utils_renderFn['default'](element, finalProps.value);
  }
});

exports.default = snowflake;
//# sourceMappingURL=snowflake.js.map
