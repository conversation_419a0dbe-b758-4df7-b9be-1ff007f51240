declare const _default: import("vue").DefineComponent<{
    animation: {
        type: import("vue").PropType<import("./type").TdStatisticProps["animation"]>;
    };
    animationStart: BooleanConstructor;
    color: {
        type: import("vue").PropType<import("./type").TdStatisticProps["color"]>;
        validator(val: import("./type").TdStatisticProps["color"]): boolean;
    };
    decimalPlaces: {
        type: NumberConstructor;
    };
    extra: {
        type: import("vue").PropType<import("./type").TdStatisticProps["extra"]>;
    };
    format: {
        type: import("vue").PropType<import("./type").TdStatisticProps["format"]>;
    };
    loading: BooleanConstructor;
    prefix: {
        type: import("vue").PropType<import("./type").TdStatisticProps["prefix"]>;
    };
    separator: {
        type: StringConstructor;
        default: string;
    };
    suffix: {
        type: import("vue").PropType<import("./type").TdStatisticProps["suffix"]>;
    };
    title: {
        type: import("vue").PropType<import("./type").TdStatisticProps["title"]>;
    };
    trend: {
        type: import("vue").PropType<import("./type").TdStatisticProps["trend"]>;
        validator(val: import("./type").TdStatisticProps["trend"]): boolean;
    };
    trendPlacement: {
        type: import("vue").PropType<import("./type").TdStatisticProps["trendPlacement"]>;
        default: import("./type").TdStatisticProps["trendPlacement"];
        validator(val: import("./type").TdStatisticProps["trendPlacement"]): boolean;
    };
    unit: {
        type: import("vue").PropType<import("./type").TdStatisticProps["unit"]>;
    };
    value: {
        type: NumberConstructor;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    animation: {
        type: import("vue").PropType<import("./type").TdStatisticProps["animation"]>;
    };
    animationStart: BooleanConstructor;
    color: {
        type: import("vue").PropType<import("./type").TdStatisticProps["color"]>;
        validator(val: import("./type").TdStatisticProps["color"]): boolean;
    };
    decimalPlaces: {
        type: NumberConstructor;
    };
    extra: {
        type: import("vue").PropType<import("./type").TdStatisticProps["extra"]>;
    };
    format: {
        type: import("vue").PropType<import("./type").TdStatisticProps["format"]>;
    };
    loading: BooleanConstructor;
    prefix: {
        type: import("vue").PropType<import("./type").TdStatisticProps["prefix"]>;
    };
    separator: {
        type: StringConstructor;
        default: string;
    };
    suffix: {
        type: import("vue").PropType<import("./type").TdStatisticProps["suffix"]>;
    };
    title: {
        type: import("vue").PropType<import("./type").TdStatisticProps["title"]>;
    };
    trend: {
        type: import("vue").PropType<import("./type").TdStatisticProps["trend"]>;
        validator(val: import("./type").TdStatisticProps["trend"]): boolean;
    };
    trendPlacement: {
        type: import("vue").PropType<import("./type").TdStatisticProps["trendPlacement"]>;
        default: import("./type").TdStatisticProps["trendPlacement"];
        validator(val: import("./type").TdStatisticProps["trendPlacement"]): boolean;
    };
    unit: {
        type: import("vue").PropType<import("./type").TdStatisticProps["unit"]>;
    };
    value: {
        type: NumberConstructor;
    };
}>>, {
    animationStart: boolean;
    loading: boolean;
    separator: string;
    trendPlacement: "left" | "right";
}, {}>;
export default _default;
