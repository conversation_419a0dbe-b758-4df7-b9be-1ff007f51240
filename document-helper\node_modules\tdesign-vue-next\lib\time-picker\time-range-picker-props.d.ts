import { TdTimeRangePickerProps } from '../time-picker/type';
import { PropType } from 'vue';
declare const _default: {
    allowInput: BooleanConstructor;
    autoSwap: {
        type: BooleanConstructor;
        default: boolean;
    };
    borderless: BooleanConstructor;
    clearable: BooleanConstructor;
    disableTime: {
        type: PropType<TdTimeRangePickerProps["disableTime"]>;
    };
    disabled: {
        type: PropType<TdTimeRangePickerProps["disabled"]>;
        default: TdTimeRangePickerProps["disabled"];
    };
    format: {
        type: StringConstructor;
        default: string;
    };
    hideDisabledTime: {
        type: BooleanConstructor;
        default: boolean;
    };
    label: {
        type: PropType<TdTimeRangePickerProps["label"]>;
    };
    placeholder: {
        type: PropType<TdTimeRangePickerProps["placeholder"]>;
        default: TdTimeRangePickerProps["placeholder"];
    };
    popupProps: {
        type: PropType<TdTimeRangePickerProps["popupProps"]>;
    };
    presets: {
        type: PropType<TdTimeRangePickerProps["presets"]>;
    };
    rangeInputProps: {
        type: PropType<TdTimeRangePickerProps["rangeInputProps"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    size: {
        type: PropType<TdTimeRangePickerProps["size"]>;
        default: TdTimeRangePickerProps["size"];
        validator(val: TdTimeRangePickerProps["size"]): boolean;
    };
    status: {
        type: PropType<TdTimeRangePickerProps["status"]>;
        default: TdTimeRangePickerProps["status"];
        validator(val: TdTimeRangePickerProps["status"]): boolean;
    };
    steps: {
        type: PropType<TdTimeRangePickerProps["steps"]>;
        default: () => TdTimeRangePickerProps["steps"];
    };
    tips: {
        type: PropType<TdTimeRangePickerProps["tips"]>;
    };
    value: {
        type: PropType<TdTimeRangePickerProps["value"]>;
        default: TdTimeRangePickerProps["value"];
    };
    modelValue: {
        type: PropType<TdTimeRangePickerProps["value"]>;
        default: TdTimeRangePickerProps["value"];
    };
    defaultValue: {
        type: PropType<TdTimeRangePickerProps["defaultValue"]>;
    };
    onBlur: PropType<TdTimeRangePickerProps["onBlur"]>;
    onChange: PropType<TdTimeRangePickerProps["onChange"]>;
    onFocus: PropType<TdTimeRangePickerProps["onFocus"]>;
    onInput: PropType<TdTimeRangePickerProps["onInput"]>;
    onPick: PropType<TdTimeRangePickerProps["onPick"]>;
};
export default _default;
