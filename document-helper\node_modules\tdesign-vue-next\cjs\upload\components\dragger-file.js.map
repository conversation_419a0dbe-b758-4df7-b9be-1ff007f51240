{"version": 3, "file": "dragger-file.js", "sources": ["../../../../components/upload/components/dragger-file.tsx"], "sourcesContent": ["import { defineComponent, toRefs, PropType, ref, computed, h } from 'vue';\nimport {\n  CheckCircleFilledIcon as TdCheckCircleFilledIcon,\n  ErrorCircleFilledIcon as TdErrorCircleFilledIcon,\n} from 'tdesign-icons-vue-next';\nimport { abridgeName, getFileSizeText } from '@tdesign/common-js/upload/utils';\nimport { TdUploadProps, UploadFile } from '../type';\nimport Button from '../../button';\nimport { CommonDisplayFileProps } from '../types';\nimport { commonProps } from '../consts';\nimport { useTNodeJSX, useGlobalIcon, useCommonClassName } from '@tdesign/shared-hooks';\nimport TLoading from '../../loading';\nimport useDrag, { UploadDragEvents } from '../hooks/useDrag';\n\nimport ImageViewer, { ImageViewerProps } from '../../image-viewer';\n\nimport { UploadConfig } from '../../config-provider';\nimport Image from '../../image';\n\nexport interface DraggerProps extends CommonDisplayFileProps {\n  trigger?: TdUploadProps['trigger'];\n  triggerUpload?: (e: MouseEvent) => void;\n  uploadFiles?: (toFiles?: UploadFile[]) => void;\n  cancelUpload?: (context: { e: MouseEvent; file: UploadFile }) => void;\n  dragEvents: UploadDragEvents;\n}\n\nexport default defineComponent({\n  name: 'UploadDraggerFile',\n  props: {\n    ...commonProps,\n    trigger: Function as PropType<DraggerProps['trigger']>,\n    triggerUpload: Function as PropType<DraggerProps['triggerUpload']>,\n    uploadFiles: Function as PropType<DraggerProps['uploadFiles']>,\n    cancelUpload: Function as PropType<DraggerProps['cancelUpload']>,\n    dragEvents: Object as PropType<DraggerProps['dragEvents']>,\n  },\n  setup(props, { slots }) {\n    const { displayFiles, disabled, accept } = toRefs(props);\n    const locale = computed(() => props.locale as UploadConfig);\n\n    const renderTNodeJSX = useTNodeJSX();\n\n    const { SIZE } = useCommonClassName();\n    const uploadPrefix = `${props.classPrefix}-upload`;\n\n    const drag = useDrag(props.dragEvents, accept);\n    const { dragActive } = drag;\n\n    const draggerFileRef = ref();\n\n    const classes = computed(() => [\n      `${uploadPrefix}__dragger`,\n      { [`${uploadPrefix}__dragger-center`]: !displayFiles.value[0] },\n      { [`${uploadPrefix}__dragger-error`]: displayFiles.value[0]?.status === 'fail' },\n    ]);\n\n    const { CheckCircleFilledIcon, ErrorCircleFilledIcon } = useGlobalIcon({\n      CheckCircleFilledIcon: TdCheckCircleFilledIcon,\n      ErrorCircleFilledIcon: TdErrorCircleFilledIcon,\n    });\n\n    const renderImage = () => {\n      if (!props.displayFiles.length) return;\n      const file = displayFiles.value[0];\n      if (!file) return null;\n      const url = file?.url || file?.response?.url;\n      return (\n        <div class={`${uploadPrefix}__dragger-img-wrap`}>\n          <ImageViewer\n            images={[url]}\n            trigger={(h, { open }: any) => <Image src={url || file.raw} onClick={open} error=\"\" loading=\"\" />}\n            {...(props.imageViewerProps as ImageViewerProps)}\n          ></ImageViewer>\n        </div>\n      );\n    };\n\n    const renderUploading = () => {\n      if (!props.displayFiles.length) return;\n      const file = displayFiles.value[0];\n      if (!file) return null;\n      if (file?.status === 'progress') {\n        return (\n          <div class={`${uploadPrefix}__single-progress`}>\n            <TLoading />\n            {props.showUploadProgress && <span class={`${uploadPrefix}__single-percent`}>{file.percent}%</span>}\n          </div>\n        );\n      }\n    };\n\n    const renderMainPreview = () => {\n      const file = displayFiles.value[0];\n      const fileName = props.abridgeName ? abridgeName(file.name, ...props.abridgeName) : file.name;\n\n      const fileInfo = [\n        <div class={`${uploadPrefix}__dragger-text`} key=\"info\">\n          <span class={`${uploadPrefix}__single-name`}>{fileName}</span>\n          {file.status === 'progress' && renderUploading()}\n          {file.status === 'success' && <CheckCircleFilledIcon />}\n          {file.status === 'fail' && <ErrorCircleFilledIcon />}\n        </div>,\n        <small class={`${SIZE.value.small}`} key=\"size\">\n          {locale.value.file.fileSizeText}：{getFileSizeText(file.size)}\n        </small>,\n        <small class={`${SIZE.value.small}`} key=\"time\">\n          {locale.value.file.fileOperationDateText}：{file.uploadTime || '-'}\n        </small>,\n      ];\n      return (\n        <div class={`${uploadPrefix}__dragger-progress`}>\n          {props.theme === 'image' && renderImage()}\n          <div class={`${uploadPrefix}__dragger-progress-info`}>\n            {renderTNodeJSX('fileListDisplay', { params: { files: props.displayFiles } }) || fileInfo}\n\n            <div class={`${uploadPrefix}__dragger-btns`}>\n              {['progress', 'waiting'].includes(file.status) && !disabled.value && (\n                <Button\n                  theme=\"primary\"\n                  variant=\"text\"\n                  class={`${uploadPrefix}__dragger-progress-cancel`}\n                  onClick={(e: MouseEvent) =>\n                    props.cancelUpload?.({\n                      e,\n                      file: props.toUploadFiles[0] || props.files[0],\n                    })\n                  }\n                >\n                  {locale.value?.cancelUploadText}\n                </Button>\n              )}\n              {!props.autoUpload && file.status === 'waiting' && (\n                <Button\n                  theme=\"primary\"\n                  variant=\"text\"\n                  disabled={disabled.value}\n                  onClick={() => props.uploadFiles?.()}\n                  class={`${uploadPrefix}__dragger-upload-btn`}\n                >\n                  {locale.value.triggerUploadText.normal}\n                </Button>\n              )}\n            </div>\n            {['fail', 'success'].includes(file?.status) && !disabled.value && (\n              <div class={`${uploadPrefix}__dragger-btns`}>\n                <Button\n                  theme=\"primary\"\n                  variant=\"text\"\n                  disabled={disabled.value}\n                  class={`${uploadPrefix}__dragger-progress-cancel`}\n                  onClick={props.triggerUpload}\n                >\n                  {locale.value.triggerUploadText.reupload}\n                </Button>\n                <Button\n                  theme=\"danger\"\n                  variant=\"text\"\n                  disabled={disabled.value}\n                  class={`${uploadPrefix}__dragger-delete-btn`}\n                  onClick={(e: MouseEvent) => props.onRemove({ e, index: 0, file })}\n                >\n                  {locale.value.triggerUploadText.delete}\n                </Button>\n              </div>\n            )}\n          </div>\n        </div>\n      );\n    };\n\n    const renderDefaultDragElement = () => {\n      const unActiveElement = (\n        <div>\n          <span class={`${uploadPrefix}--highlight`}>{locale.value.triggerUploadText?.normal}</span>\n          <span>&nbsp;&nbsp;/&nbsp;&nbsp;{locale.value.dragger.draggingText}</span>\n        </div>\n      );\n      const activeElement = <div>{locale.value.dragger.dragDropText}</div>;\n      return dragActive.value ? activeElement : unActiveElement;\n    };\n\n    const getContent = () => {\n      const file = displayFiles.value[0];\n      if (file && (['progress', 'success', 'fail', 'waiting'].includes(file.status) || !file.status)) {\n        return renderMainPreview();\n      }\n      return (\n        <div class={`${uploadPrefix}__trigger`} onClick={props.triggerUpload}>\n          {slots.default?.() || renderDefaultDragElement()}\n        </div>\n      );\n    };\n\n    return () => (\n      <div\n        ref={draggerFileRef}\n        class={classes.value}\n        onDrop={drag.handleDrop}\n        onDragenter={drag.handleDragenter}\n        onDragover={drag.handleDragover}\n        onDragleave={drag.handleDragleave}\n      >\n        {props.trigger?.(h, { files: displayFiles.value, dragActive: dragActive.value }) || getContent()}\n      </div>\n    );\n  },\n});\n"], "names": ["defineComponent", "name", "props", "_objectSpread", "commonProps", "trigger", "Function", "triggerUpload", "uploadFiles", "cancelUpload", "dragEvents", "Object", "setup", "_ref", "slots", "_toRefs", "toRefs", "displayFiles", "disabled", "accept", "locale", "computed", "renderTNodeJSX", "useTNodeJSX", "_useCommonClassName", "useCommonClassName", "SIZE", "uploadPrefix", "concat", "classPrefix", "drag", "useDrag", "dragActive", "draggerFileRef", "ref", "classes", "_displayFiles$value$", "_defineProperty", "value", "status", "_useGlobalIcon", "useGlobalIcon", "CheckCircleFilledIcon", "TdCheckCircleFilledIcon", "ErrorCircleFilledIcon", "TdErrorCircleFilledIcon", "renderImage", "_file$response", "length", "file", "url", "response", "_createVNode", "h", "open", "_ref4", "Image", "raw", "imageViewerProps", "renderUploading", "TLoading", "showUploadProgress", "percent", "renderMainPreview", "_locale$value", "fileName", "abridgeName", "apply", "fileInfo", "small", "fileSizeText", "getFileSizeText", "size", "fileOperationDateText", "uploadTime", "theme", "params", "files", "includes", "<PERSON><PERSON>", "onClick", "e", "_props$cancelUpload", "call", "toUploadFiles", "_default", "cancelUploadText", "autoUpload", "_props$uploadFiles", "triggerUploadText", "normal", "reupload", "onRemove", "index", "renderDefaultDragElement", "_locale$value$trigger", "unActiveElement", "dragger", "draggingText", "activeElement", "dragDropText", "get<PERSON>ontent", "_slots$default", "_props$trigger", "handleDrop", "handleDragenter", "handleDragover", "handleDragleave"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,kBAAeA,mBAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,mBAAA;AACNC,EAAAA,KAAO,EAAAC,aAAA,CAAAA,aAAA,KACFC,+BAAA,CAAA,EAAA,EAAA,EAAA;AACHC,IAAAA,OAAS,EAAAC,QAAA;AACTC,IAAAA,aAAe,EAAAD,QAAA;AACfE,IAAAA,WAAa,EAAAF,QAAA;AACbG,IAAAA,YAAc,EAAAH,QAAA;AACdI,IAAAA,UAAY,EAAAC,MAAAA;GACd,CAAA;AACAC,EAAAA,KAAM,WAANA,KAAMA,CAAAV,KAAA,EAAAW,IAAA,EAAkB;AAAA,IAAA,IAATC,KAAA,GAAAD,IAAA,CAAAC,KAAA,CAAA;AACb,IAAA,IAAAC,OAAA,GAA2CC,WAAOd,KAAK,CAAA;MAA/Ce,YAAc,GAAAF,OAAA,CAAdE,YAAc;MAAAC,QAAA,GAAAH,OAAA,CAAAG,QAAA;MAAUC,MAAO,GAAAJ,OAAA,CAAPI,MAAO,CAAA;IACvC,IAAMC,MAAS,GAAAC,YAAA,CAAS,YAAA;MAAA,OAAMnB,KAAA,CAAMkB,MAAsB,CAAA;KAAA,CAAA,CAAA;AAE1D,IAAA,IAAME,iBAAiBC,iBAAY,EAAA,CAAA;AAE7B,IAAA,IAAAC,mBAAA,GAAWC,0BAAmB,EAAA;MAA5BC,IAAK,GAAAF,mBAAA,CAALE,IAAK,CAAA;AACP,IAAA,IAAAC,YAAA,GAAAC,EAAAA,CAAAA,MAAA,CAAkB1B,KAAM,CAAA2B,WAAA,EAAA,SAAA,CAAA,CAAA;IAE9B,IAAMC,IAAO,GAAAC,+BAAA,CAAQ7B,KAAM,CAAAQ,UAAA,EAAYS,MAAM,CAAA,CAAA;AACvC,IAAA,IAAEa,aAAeF,IAAA,CAAfE;AAER,IAAA,IAAMC,iBAAiBC,OAAI,EAAA,CAAA;IAErB,IAAAC,OAAA,GAAUd,aAAS,YAAA;AAAA,MAAA,IAAAe,oBAAA,CAAA;AAAA,MAAA,OAAM,IAAAR,MAAA,CAC1BD,YAAA,EAAAU,WAAAA,CAAAA,EAAAA,mCAAA,QAAAT,MAAA,CACGD,cAAiC,kBAAA,CAAA,EAAA,CAACV,YAAA,CAAaqB,MAAM,CAAG,CAAA,GAAAD,mCAAA,CAAA,EAAA,EAAA,EAAA,CAAAT,MAAA,CACxDD,YAAA,EAAgC,iBAAA,CAAA,EAAA,CAAA,CAAAS,oBAAA,GAAAnB,aAAaqB,KAAM,CAAA,CAAA,CAAA,MAAA,IAAA,IAAAF,oBAAA,KAAnBA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,oBAAA,CAAuBG,YAAW,MAAO,CAChF,CAAA,CAAA;KAAA,CAAA,CAAA;IAED,IAAAC,cAAA,GAAyDC,qBAAc,CAAA;AACrEC,QAAAA,qBAAuB,EAAAC,yCAAA;AACvBC,QAAAA,qBAAuB,EAAAC,yCAAAA;AACzB,OAAC,CAAA;MAHOH,qBAAA,GAAAF,cAAA,CAAAE,qBAAA;MAAuBE,qBAAsB,GAAAJ,cAAA,CAAtBI,qBAAsB,CAAA;AAKrD,IAAA,IAAME,cAAc,SAAdA,cAAoB;AAAA,MAAA,IAAAC,cAAA,CAAA;AACpB,MAAA,IAAA,CAAC7C,MAAMe,YAAa,CAAA+B,MAAA,EAAQ,OAAA;AAC1B,MAAA,IAAAC,IAAA,GAAOhC,aAAaqB,KAAM,CAAA,CAAA,CAAA,CAAA;AAChC,MAAA,IAAI,CAACW,IAAA,EAAa,OAAA,IAAA,CAAA;MAClB,IAAMC,GAAM,GAAA,CAAAD,IAAA,KAAA,IAAA,IAAAA,IAAA,KAAAA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAA,CAAMC,GAAO,MAAAD,IAAA,aAAAA,IAAA,KAAA,KAAA,CAAA,IAAA,CAAAF,cAAA,GAAAE,IAAA,CAAME,QAAU,MAAA,IAAA,IAAAJ,cAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAhBA,cAAA,CAAgBG,GAAA,CAAA,CAAA;AACzC,MAAA,OAAAE,eAAA,CAAA,KAAA,EAAA;QAAA,OAAAxB,EAAAA,EAAAA,CAAAA,MAAA,CACiBD;;gBAEH,EAAA,CAACuB,GAAG,CAAA;AAAA,QAAA,SAAA,EACH,SAAA7C,OAAAA,CAACgD;cAAKC,IAAA,GAAAC,KAAA,CAAAD,IAAA,CAAA;UAAA,OAAAF,eAAA,CAAAI,iBAAA,EAAA;AAAA,YAAA,KAAA,EAA4BN,GAAO,IAAAD,IAAA,CAAKQ;uBAAcH,IAAA;AAAA,YAAA,OAAA,EAAA,EAAA;AAAA,YAAA,SAAA,EAAA,EAAA;AAAA,WAAA,EAAA,IAAA,CAAA,CAAA;AAAA,SAAA;OAChEpD,EAAAA,KAAM,CAAAwD,gBAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;KAInB,CAAA;AAEA,IAAA,IAAMC,kBAAkB,SAAlBA,kBAAwB;AACxB,MAAA,IAAA,CAACzD,MAAMe,YAAa,CAAA+B,MAAA,EAAQ,OAAA;AAC1B,MAAA,IAAAC,IAAA,GAAOhC,aAAaqB,KAAM,CAAA,CAAA,CAAA,CAAA;AAChC,MAAA,IAAI,CAACW,IAAA,EAAa,OAAA,IAAA,CAAA;MACd,IAAA,CAAAA,IAAA,KAAA,IAAA,IAAAA,IAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,IAAA,CAAMV,YAAW,UAAY,EAAA;AAE7B,QAAA,OAAAa,eAAA,CAAA,KAAA,EAAA;UAAA,OAAAxB,EAAAA,EAAAA,CAAAA,MAAA,CAAeD,YAAA,EAAA,mBAAA,CAAA;SAAAyB,EAAAA,CAAAA,eAAA,CAAAQ,qBAAA,EAAA,IAAA,EAAA,IAAA,CAAA,EAEZ1D,KAAM,CAAA2D,kBAAA,IAAAT,eAAA,CAAA,MAAA,EAAA;UAAA,OAAAxB,EAAAA,EAAAA,CAAAA,MAAA,CAAsCD,YAAA,EAAA,kBAAA,CAAA;AAAA,SAAA,EAAA,CAAiCsB,IAAK,CAAAa,OAAA,EAAQ,GAAA,CAA7D,CAAA,CAAA,CAAA,CAAA;AAGpC,OAAA;KACF,CAAA;AAEA,IAAA,IAAMC,oBAAoB,SAApBA,oBAA0B;AAAA,MAAA,IAAAC,aAAA,CAAA;AACxB,MAAA,IAAAf,IAAA,GAAOhC,aAAaqB,KAAM,CAAA,CAAA,CAAA,CAAA;MAC1B,IAAA2B,QAAA,GAAW/D,KAAM,CAAAgE,WAAA,GAAcA,iBAAY,CAAAC,KAAA,CAAA,KAAA,CAAA,EAAA,CAAAlB,IAAA,CAAKhD,oDAASC,KAAA,CAAMgE,WAAW,CAAA,CAAA,CAAA,GAAIjB,IAAK,CAAAhD,IAAA,CAAA;MAEzF,IAAMmE,QAAW,GAAA,CAAAhB,eAAA,CAAA,KAAA,EAAA;QAAA,OAAAxB,EAAAA,EAAAA,CAAAA,MAAA,CACAD;;;2BACGA,YAAA,EAAA,eAAA,CAAA;AAAA,OAAA,EAAA,CAA8BsC,YAC7ChB,IAAA,CAAKV,MAAW,KAAA,UAAA,IAAcoB,eAAgB,EAAA,EAC9CV,IAAK,CAAAV,MAAA,KAAW,SAAa,IAAAa,eAAA,CAAAV,qBAAA,EAAuB,IAAA,EAAA,IAAA,CAAA,EACpDO,IAAK,CAAAV,MAAA,KAAW,MAAU,IAAAa,eAAA,CAAAR,qBAAA,EAAA,IAAA,EAAA,IAAA,CAAuB,IAAAQ,eAAA,CAAA,OAAA,EAAA;AAAA,QAAA,OAAA,EAAA,EAAA,CAAAxB,MAAA,CAEnCF,IAAK,CAAAY,KAAA,CAAM+B;;UACzBjD,MAAA,CAAOkB,MAAMW,IAAK,CAAAqB,YAAA,EAAa,QAAA,EAAEC,qBAAA,CAAgBtB,KAAKuB,IAAI,CAAA,IAAApB,eAAA,CAAA,OAAA,EAAA;AAAA,QAAA,OAAA,EAAA,EAAA,CAAAxB,MAAA,CAE5CF,IAAK,CAAAY,KAAA,CAAM+B;;UACzBjD,MAAA,CAAOkB,MAAMW,IAAK,CAAAwB,qBAAA,EAAsB,QAAA,EAAExB,KAAKyB,UAAc,IAAA,GAAA,CAElE,CAAA,CAAA,CAAA;AAEE,MAAA,OAAAtB,eAAA,CAAA,KAAA,EAAA;QAAA,OAAAxB,EAAAA,EAAAA,CAAAA,MAAA,CAAeD,YAAA,EAAA,oBAAA,CAAA;OACZzB,EAAAA,CAAAA,KAAA,CAAMyE,KAAU,KAAA,OAAA,IAAW7B,WAAY,EAAA,EAAAM,eAAA,CAAA,KAAA,EAAA;QAAA,OAAAxB,EAAAA,EAAAA,CAAAA,MAAA,CACzBD,YAAA,EAAA,yBAAA,CAAA;OACZL,EAAAA,CAAAA,cAAA,CAAe,iBAAmB,EAAA;AAAEsD,QAAAA,MAAQ,EAAA;UAAEC,OAAO3E,KAAM,CAAAe,YAAAA;AAAa,SAAA;AAAE,OAAC,CAAK,IAAAmD,QAAA,EAAAhB,eAAA,CAAA,KAAA,EAAA;QAAA,OAAAxB,EAAAA,EAAAA,CAAAA,MAAA,CAElED,YAAA,EAAA,gBAAA,CAAA;OACZ,EAAA,CAAA,CAAC,UAAY,EAAA,SAAS,CAAE,CAAAmD,QAAA,CAAS7B,KAAKV,MAAM,CAAA,IAAK,CAACrB,QAAA,CAASoB,KAC1D,IAAAc,eAAA,CAAA2B,mBAAA,EAAA;AAAA,QAAA,OAAA,EAAA,SAAA;AAAA,QAAA,SAAA,EAAA,MAAA;QAAA,OAAAnD,EAAAA,EAAAA,CAAAA,MAAA,CAGYD;iBACD,EAAA,SAAAqD,QAACC,CACR,EAAA;AAAA,UAAA,IAAAC,mBAAA,CAAA;AAAA,UAAA,OAAA,CAAAA,mBAAA,GAAAhF,KAAA,CAAMO,YAAe,MAAA,IAAA,IAAAyE,mBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAArBA,mBAAA,CAAAC,IAAA,CAAAjF,KAAA,EAAqB;AACnB+E,YAAAA,CAAA,EAAAA,CAAA;AACAhC,YAAAA,IAAM,EAAA/C,KAAA,CAAMkF,aAAc,CAAA,CAAA,CAAA,IAAMlF,MAAM2E,KAAM,CAAA,CAAA,CAAA;AAC9C,WAAC,CAAA,CAAA;AAAA,SAAA;AAAA,OAAA,EAAA;AAAA,QAAA,SAAA,EAAA,SAAAQ,QAAA,GAAA;UAAA,OAAArB,CAAAA,CAAAA,aAAA,GAGF5C,MAAO,CAAAkB,KAAA,cAAA0B,aAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAPA,aAAA,CAAcsB;;QAXhB,EAcF,CAACpF,KAAM,CAAAqF,UAAA,IAActC,IAAK,CAAAV,MAAA,KAAW,SACpC,IAAAa,eAAA,CAAA2B,mBAAA,EAAA;AAAA,QAAA,OAAA,EAAA,SAAA;AAAA,QAAA,SAAA,EAAA,MAAA;QAAA,UAGY7D,EAAAA,QAAS,CAAAoB,KAAA;AAAA,QAAA,SAAA,EACV,SAAA0C,OAAA,GAAA;AAAA,UAAA,IAAAQ,kBAAA,CAAA;AAAA,UAAA,OAAA,CAAAA,kBAAA,GAAMtF,KAAM,CAAAM,WAAA,MAAA,IAAA,IAAAgF,kBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAANA,kBAAA,CAAAL,IAAA,CAAAjF,KAAoB,CACnC,CAAA;AAAA,SAAA;QAAA,OAAA0B,EAAAA,EAAAA,CAAAA,MAAA,CAAUD,YAAA,EAAA,sBAAA,CAAA;AAAA,OAAA,EAAA;AAAA,QAAA,SAAA,EAAA,SAAA0D,QAAA,GAAA;AAAA,UAAA,OAAA,CAETjE,MAAO,CAAAkB,KAAA,CAAMmD,iBAAkB,CAAAC,MAAA,CAAA,CAAA;AAAA,SAAA;OAPjC,CAAA,CAAA,CAAA,EAWJ,CAAC,MAAA,EAAQ,SAAS,CAAA,CAAEZ,SAAS7B,IAAM,KAAA,IAAA,IAANA,IAAM,KAANA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAM,CAAAV,MAAM,CAAK,IAAA,CAACrB,QAAS,CAAAoB,KAAA,IAAAc,eAAA,CAAA,KAAA,EAAA;QAAA,OAAAxB,EAAAA,EAAAA,CAAAA,MAAA,CACxCD,YAAA,EAAA,gBAAA,CAAA;OAAAyB,EAAAA,CAAAA,eAAA,CAAA2B,mBAAA,EAAA;AAAA,QAAA,OAAA,EAAA,SAAA;AAAA,QAAA,SAAA,EAAA,MAAA;QAAA,UAID7D,EAAAA,QAAS,CAAAoB,KAAA;QAAA,OAAAV,EAAAA,EAAAA,CAAAA,MAAA,CACTD,YAAA,EAAA,2BAAA,CAAA;AAAA,QAAA,SAAA,EACDzB,KAAM,CAAAK,aAAAA;AAAA,OAAA,EAAA;AAAA,QAAA,SAAA,EAAA,SAAA8E,QAAA,GAAA;AAAA,UAAA,OAAA,CAEdjE,OAAOkB,KAAM,CAAAmD,iBAAA,CAAkBE;;;;;kBAKtBzE,EAAAA,QAAA,CAASoB,KACnB;QAAA,OAAAV,EAAAA,EAAAA,CAAAA,MAAA,CAAUD,YAAA,EAAA,sBAAA,CAAA;QAAA,SACD,EAAA,SAAAqD,QAACC,CAAkB,EAAA;UAAA,OAAA/E,KAAA,CAAM0F,QAAS,CAAA;AAAEX,YAAAA,CAAG,EAAHA,CAAG;AAAAY,YAAAA,KAAA,EAAO,CAAG;AAAA5C,YAAAA,IAAA,EAAAA,IAAAA;AAAK,WAAC,CAAA,CAAA;AAAA,SAAA;AAAA,OAAA,EAAA;AAAA,QAAA,SAAA,EAAA,SAAAoC,QAAA,GAAA;AAAA,UAAA,OAAA,CAE/DjE,MAAO,CAAAkB,KAAA,CAAMmD,iBAAkB,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,SAAA;OAjBnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;KAwBX,CAAA;AAEA,IAAA,IAAMK,2BAA2B,SAA3BA,2BAAiC;AAAA,MAAA,IAAAC,qBAAA,CAAA;AACrC,MAAA,IAAMC;2BAEcrE;mCAA4BP,MAAO,CAAAkB,KAAA,CAAMmD,iBAAmB,MAAA,IAAA,IAAAM,qBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAhCA,qBAAA,CAAgCL,MAAA,CAAAtC,CAAAA,EAAAA,eAAA,CACtE,MAAA,EAAA,IAAA,EAAA,CAAA,mBAAA,EAA0BhC,MAAA,CAAOkB,MAAM2D,OAAQ,CAAAC,YAAA,CAFtD,CAAA,CAAA,CAAA,CAAA;MAKH,IAAMC,gCAAsB/E,KAAAA,EAAAA,IAAAA,EAAAA,CAAAA,OAAOkB,KAAM,CAAA2D,OAAA,CAAQG,aAA1B,CAAA,CAAA;AAChB,MAAA,OAAApE,UAAA,CAAWM,QAAQ6D,aAAgB,GAAAH,eAAA,CAAA;KAC5C,CAAA;AAEA,IAAA,IAAMK,aAAa,SAAbA,aAAmB;AAAA,MAAA,IAAAC,cAAA,CAAA;AACjB,MAAA,IAAArD,IAAA,GAAOhC,aAAaqB,KAAM,CAAA,CAAA,CAAA,CAAA;MAChC,IAAIW,IAAS,KAAA,CAAC,UAAY,EAAA,SAAA,EAAW,MAAQ,EAAA,SAAS,CAAE,CAAA6B,QAAA,CAAS7B,IAAK,CAAAV,MAAM,CAAK,IAAA,CAACU,KAAKV,MAAS,CAAA,EAAA;QAC9F,OAAOwB,iBAAkB,EAAA,CAAA;AAC3B,OAAA;AACA,MAAA,OAAAX,eAAA,CAAA,KAAA,EAAA;QAAA,OAAAxB,EAAAA,EAAAA,CAAAA,MAAA,CACiBD,YAAyB,EAAA,WAAA,CAAA;AAAA,QAAA,SAAA,EAASzB,KAAM,CAAAK,aAAAA;AAAA,OAAA,EAAA,CACpD,EAAA+F,cAAA,GAAAxF,KAAM,CAAA,SAAA,CAAA,cAAAwF,cAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAANA,cAAA,CAAAnB,IAAA,CAAArE,KAAgB,CAAK,KAAAgF,wBAAA;KAG5B,CAAA;IAEA,OAAO,YAAA;AAAA,MAAA,IAAAS,cAAA,CAAA;AAAA,MAAA,OAAAnD,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,KAAA,EAEEnB;eACEE,EAAAA,OAAA,CAAQG,KACf;QAAA,QAAQR,EAAAA,KAAK0E,UACb;QAAA,aAAa1E,EAAAA,IAAK,CAAA2E,eAAA;QAAA,YACN3E,EAAAA,IAAK,CAAA4E,cAAA;AAAA,QAAA,aAAA,EACJ5E,IAAA,CAAK6E,eAAAA;AAEjB,OAAA,EAAA,CAAA,EAAAJ,cAAA,GAAArG,KAAA,CAAMG,OAAU,MAAAkG,IAAAA,IAAAA,cAAA,KAAhBA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,cAAA,CAAApB,IAAA,CAAAjF,KAAA,EAAgBmD,KAAA,EAAG;QAAEwB,KAAO,EAAA5D,YAAA,CAAaqB,KAAO;QAAAN,UAAA,EAAYA,WAAWM,KAAAA;AAAM,OAAC,CAAK,KAAA+D,UAAA;KARrF,CAAA;AAWL,GAAA;AACF,CAAC,CAAA;;;;"}