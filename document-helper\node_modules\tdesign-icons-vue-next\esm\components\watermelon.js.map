{"version": 3, "file": "watermelon.js", "sources": ["../../src/components/watermelon.tsx"], "sourcesContent": ["import { computed, PropType, defineComponent } from 'vue';\nimport renderFn from '../utils/render-fn';\nimport {\n  TdIconSVGProps, SVGJson,\n} from '../utils/types';\nimport useSizeProps from '../utils/use-size-props';\n\nimport '../style/css';\n\nconst element: SVGJson = {\"tag\":\"svg\",\"attrs\":{\"fill\":\"none\",\"viewBox\":\"0 0 24 24\",\"width\":\"1em\",\"height\":\"1em\"},\"children\":[{\"tag\":\"path\",\"attrs\":{\"fill\":\"currentColor\",\"d\":\"M17.9946 0.59082L1.59082 16.9946L2.28748 17.7017C3.32792 18.7576 4.81049 19.8194 6.41278 20.62C8.00658 21.4163 9.81015 21.9998 11.488 21.9998C17.8458 21.9998 22.9998 16.8458 22.9998 10.488C22.9998 8.81017 22.4163 7.00659 21.62 5.41278C20.8194 3.81048 19.7577 2.32791 18.7016 1.28747L17.9946 0.59082ZM20.9408 9.66091C20.9796 9.94539 20.9998 10.222 20.9998 10.488C20.9998 15.7412 16.7412 19.9998 11.488 19.9998C11.222 19.9998 10.9454 19.9796 10.6609 19.9408C16.0821 19.3922 20.3922 15.0821 20.9408 9.66091ZM17.9035 3.51032C18.3671 4.29781 18.6493 5.27542 18.8101 6.21714C18.9175 6.8457 18.9638 7.41526 18.9837 7.82761C18.9936 8.03296 18.9969 8.198 18.9981 8.31075C18.9986 8.36125 18.9987 8.40115 18.9988 8.43244L18.9989 8.44357C18.9989 8.45743 18.999 8.47714 18.9992 8.49321C18.9993 8.49607 18.9994 8.50416 18.9998 8.51518C18.9851 13.7559 14.7322 17.9998 9.48802 17.9998H9.47022L9.45433 18.0004L9.45296 18.0004L9.42857 18.0009C9.40692 18.0013 9.37201 18.0016 9.32501 18.0014C9.23092 18.001 9.08881 17.9983 8.90792 17.9888C8.54524 17.9699 8.0317 17.9239 7.43985 17.8164C6.55047 17.6547 5.52481 17.3612 4.57295 16.8409L17.9035 3.51032ZM17.0018 7.99785H14.9979V10.0018H17.0018V7.99785ZM14.0018 10.9979H11.9979V13.0018H14.0018V10.9979ZM11.0018 13.9979H8.99786V16.0018H11.0018V13.9979Z\"}}]};\n\nexport default defineComponent({\n  name: 'WatermelonIcon',\n  props: {\n    size: {\n      type: String,\n    },\n    onClick: {\n      type: Function as PropType<TdIconSVGProps['onClick']>,\n    },\n  },\n  setup(props, { attrs }) {\n    const propsSize = computed(() => props.size);\n\n    const { className, style } = useSizeProps(propsSize);\n\n    const finalCls = computed(() => ['t-icon', 't-icon-watermelon', className.value]);\n    const finalStyle = computed(() => ({ ...style.value, ...(attrs.style as Styles) }));\n    const finalProps = computed(() => ({\n      class: finalCls.value,\n      style: finalStyle.value,\n      onClick: (e:MouseEvent) => props.onClick?.({ e }),\n    }));\n    return () => renderFn(element, finalProps.value);\n  },\n\n});\n"], "names": ["element", "defineComponent", "name", "props", "size", "type", "String", "onClick", "Function", "setup", "attrs", "propsSize", "computed", "className", "style", "useSizeProps", "finalCls", "value", "finalStyle", "_objectSpread", "finalProps", "class", "e", "_props$onClick", "call", "renderFn"], "mappings": ";;;;;;;;;;AASA,IAAMA,UAAmB;EAAC,OAAM;EAAM,SAAQ;IAAC,QAAO;IAAO,WAAU;IAAY,SAAQ;IAAM,UAAS;;EAAO,YAAW,CAAC;IAAC,OAAM;IAAO,SAAQ;MAAC,QAAO;MAAe,KAAI;;;;AAE9K,iBAAeC,gBAAgB;EAC7BC,MAAM;EACNC,OAAO;IACLC,MAAM;MACJC,MAAMC;;IAERC,SAAS;MACPF,MAAMG;;;EAGVC,MAAMN,aAAkB;IAAA,IAAX;MAAEO;;QACPC,YAAYC,SAAS,MAAMT,MAAMC;QAEjC;MAAES;MAAWC;QAAUC,aAAaJ;QAEpCK,WAAWJ,SAAS,MAAM,CAAC,UAAU,qBAAqBC,UAAUI;QACpEC,aAAaN,SAAS,MAAAO,aAAA,CAAAA,aAAA,KAAYL,MAAMG,QAAWP,MAAMI;QACzDM,aAAaR,SAAS;MAC1BS,OAAOL,SAASC;MAChBH,OAAOI,WAAWD;MAClBV,SAAUe;;iCAAiBnB,MAAMI,0DAANgB,cAAA,CAAAC,IAAA,CAAArB,OAAgB;UAAEmB;;;;WAExC,MAAMG,SAASzB,SAASoB,WAAWH;;AAAA;;;;"}