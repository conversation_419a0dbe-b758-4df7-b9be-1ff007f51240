{"version": 3, "file": "wave-right-filled.js", "sources": ["../../src/components/wave-right-filled.tsx"], "sourcesContent": ["import { computed, PropType, defineComponent } from 'vue';\nimport renderFn from '../utils/render-fn';\nimport {\n  TdIconSVGProps, SVGJson,\n} from '../utils/types';\nimport useSizeProps from '../utils/use-size-props';\n\nimport '../style/css';\n\nconst element: SVGJson = {\"tag\":\"svg\",\"attrs\":{\"fill\":\"none\",\"viewBox\":\"0 0 24 24\",\"width\":\"1em\",\"height\":\"1em\"},\"children\":[{\"tag\":\"path\",\"attrs\":{\"fill\":\"currentColor\",\"d\":\"M18.0471 1.37012L18.8705 1.9376C20.0768 2.76906 21.1233 3.81549 21.9548 5.02184L22.5223 5.8452 20.8756 6.98024 20.3081 6.15688C19.6146 5.15081 18.7416 4.27779 17.7355 3.58437L16.9121 3.01688 18.0471 1.37012zM13.7523 4.46212C13.2151 3.92523 12.3441 3.92651 11.8086 4.46497L6.55473 9.74721 6.99913 8.65149C7.55869 7.27181 5.99352 6.00154 4.75852 6.83306 4.57016 6.95988 4.41285 7.12759 4.29834 7.32369L1.91873 11.3985C.271144 14.2198.733135 17.7974 3.04341 20.1077L3.88772 20.952C6.69539 23.7597 11.2475 23.7597 14.0552 20.952L19.0896 15.9164C19.6263 15.3796 19.6265 14.5094 19.09 13.9724 18.5531 13.4349 17.6821 13.4347 17.1449 13.9719L15.5413 15.5755 15.0103 15.0444 18.7004 11.3543C19.2372 10.8174 19.2372 9.94702 18.7004 9.41014 18.1635 8.87324 17.2931 8.87323 16.7562 9.41011L13.0661 13.1002 12.5357 12.5699 17.5926 7.51303C18.1294 6.97621 18.1296 6.1059 17.593 5.56887 17.0561 5.03154 16.1852 5.03137 15.6481 5.56849L10.5912 10.6253 10.061 10.0952 13.7525 6.40366C14.2887 5.86749 14.2886 4.99816 13.7523 4.46212z\"}}]};\n\nexport default defineComponent({\n  name: 'WaveRightFilledIcon',\n  props: {\n    size: {\n      type: String,\n    },\n    onClick: {\n      type: Function as PropType<TdIconSVGProps['onClick']>,\n    },\n  },\n  setup(props, { attrs }) {\n    const propsSize = computed(() => props.size);\n\n    const { className, style } = useSizeProps(propsSize);\n\n    const finalCls = computed(() => ['t-icon', 't-icon-wave-right-filled', className.value]);\n    const finalStyle = computed(() => ({ ...style.value, ...(attrs.style as Styles) }));\n    const finalProps = computed(() => ({\n      class: finalCls.value,\n      style: finalStyle.value,\n      onClick: (e:MouseEvent) => props.onClick?.({ e }),\n    }));\n    return () => renderFn(element, finalProps.value);\n  },\n\n});\n"], "names": ["element", "defineComponent", "name", "props", "size", "type", "String", "onClick", "Function", "setup", "attrs", "propsSize", "computed", "className", "style", "useSizeProps", "finalCls", "value", "finalStyle", "_objectSpread", "finalProps", "class", "e", "_props$onClick", "call", "renderFn"], "mappings": ";;;;;;;;;;AASA,IAAMA,UAAmB;EAAC,OAAM;EAAM,SAAQ;IAAC,QAAO;IAAO,WAAU;IAAY,SAAQ;IAAM,UAAS;;EAAO,YAAW,CAAC;IAAC,OAAM;IAAO,SAAQ;MAAC,QAAO;MAAe,KAAI;;;;AAE9K,sBAAeC,gBAAgB;EAC7BC,MAAM;EACNC,OAAO;IACLC,MAAM;MACJC,MAAMC;;IAERC,SAAS;MACPF,MAAMG;;;EAGVC,MAAMN,aAAkB;IAAA,IAAX;MAAEO;;QACPC,YAAYC,SAAS,MAAMT,MAAMC;QAEjC;MAAES;MAAWC;QAAUC,aAAaJ;QAEpCK,WAAWJ,SAAS,MAAM,CAAC,UAAU,4BAA4BC,UAAUI;QAC3EC,aAAaN,SAAS,MAAAO,aAAA,CAAAA,aAAA,KAAYL,MAAMG,QAAWP,MAAMI;QACzDM,aAAaR,SAAS;MAC1BS,OAAOL,SAASC;MAChBH,OAAOI,WAAWD;MAClBV,SAAUe;;iCAAiBnB,MAAMI,0DAANgB,cAAA,CAAAC,IAAA,CAAArB,OAAgB;UAAEmB;;;;WAExC,MAAMG,SAASzB,SAASoB,WAAWH;;AAAA;;;;"}