'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var defineProperty = require('../_chunks/dep-304d0f8c.js');
var vue = require('vue');
var utils_renderFn = require('../utils/render-fn.js');
var utils_useSizeProps = require('../utils/use-size-props.js');
require('../utils/use-common-classname.js');
require('../utils/config-context.js');

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { defineProperty._defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var element = {
  "tag": "svg",
  "attrs": {
    "fill": "none",
    "viewBox": "0 0 24 24",
    "width": "1em",
    "height": "1em"
  },
  "children": [{
    "tag": "path",
    "attrs": {
      "fill": "currentColor",
      "d": "M15 1.29004V22.7095L5.7369 17.4998L1 17.4998L1 6.49976H5.7369L15 1.29004ZM4.99882 8.49976H3L3 15.4998L4.99882 15.4998L4.99882 8.49976ZM6.99882 15.9149L13 19.29V4.70949L6.99882 8.08465V15.9149ZM20.4497 5.6358L21.1569 6.34291C24.281 9.4671 24.281 14.5324 21.1569 17.6566L20.4497 18.3637L19.0355 16.9495L19.7426 16.2424C22.0858 13.8993 22.0858 10.1003 19.7426 7.75712L19.0355 7.05002L20.4497 5.6358ZM17.2678 8.81778L18.682 7.40357L19.3891 8.11068C21.537 10.2586 21.537 13.741 19.3891 15.8888L18.682 16.596L17.2678 15.1817L17.9749 14.4746C19.3417 13.1078 19.3417 10.8917 17.9749 9.52489L17.2678 8.81778ZM16.9142 9.17134L17.6213 9.87844C18.7929 11.05 18.7929 12.9495 17.6213 14.1211L16.9142 14.8282L15.5 13.414L16.2071 12.7069C16.5976 12.3163 16.5976 11.6832 16.2071 11.2927L15.5 10.5855L16.9142 9.17134Z"
    }
  }]
};
var soundHigh = vue.defineComponent({
  name: "SoundHighIcon",
  props: {
    size: {
      type: String
    },
    onClick: {
      type: Function
    }
  },
  setup(props, _ref) {
    var {
      attrs
    } = _ref;
    var propsSize = vue.computed(() => props.size);
    var {
      className,
      style
    } = utils_useSizeProps['default'](propsSize);
    var finalCls = vue.computed(() => ["t-icon", "t-icon-sound-high", className.value]);
    var finalStyle = vue.computed(() => _objectSpread(_objectSpread({}, style.value), attrs.style));
    var finalProps = vue.computed(() => ({
      class: finalCls.value,
      style: finalStyle.value,
      onClick: e => {
        var _props$onClick;
        return (_props$onClick = props.onClick) === null || _props$onClick === void 0 ? void 0 : _props$onClick.call(props, {
          e
        });
      }
    }));
    return () => utils_renderFn['default'](element, finalProps.value);
  }
});

exports.default = soundHigh;
//# sourceMappingURL=sound-high.js.map
