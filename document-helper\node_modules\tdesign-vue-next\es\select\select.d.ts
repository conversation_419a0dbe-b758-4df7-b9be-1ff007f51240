import { PropType } from 'vue';
import type { TdSelectProps, SelectValue, TdOptionProps } from './type';
import { SelectInputValueDisplayOptions } from '../select-input/hooks/useSingle';
declare const _default: import("vue").DefineComponent<{
    valueDisplayOptions: {
        type: PropType<SelectInputValueDisplayOptions>;
    };
    autoWidth: BooleanConstructor;
    autofocus: BooleanConstructor;
    borderless: BooleanConstructor;
    clearable: BooleanConstructor;
    collapsedItems: {
        type: PropType<TdSelectProps["collapsedItems"]>;
    };
    creatable: BooleanConstructor;
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    empty: {
        type: PropType<TdSelectProps["empty"]>;
    };
    filter: {
        type: PropType<TdSelectProps["filter"]>;
    };
    filterable: BooleanConstructor;
    inputProps: {
        type: PropType<TdSelectProps["inputProps"]>;
    };
    inputValue: {
        type: PropType<TdSelectProps["inputValue"]>;
        default: TdSelectProps["inputValue"];
    };
    defaultInputValue: {
        type: PropType<TdSelectProps["defaultInputValue"]>;
    };
    keys: {
        type: PropType<TdSelectProps["keys"]>;
    };
    label: {
        type: PropType<TdSelectProps["label"]>;
    };
    loading: BooleanConstructor;
    loadingText: {
        type: PropType<TdSelectProps["loadingText"]>;
    };
    max: {
        type: NumberConstructor;
        default: number;
    };
    minCollapsedNum: {
        type: NumberConstructor;
        default: number;
    };
    multiple: BooleanConstructor;
    options: {
        type: PropType<TdSelectProps["options"]>;
    };
    panelBottomContent: {
        type: PropType<TdSelectProps["panelBottomContent"]>;
    };
    panelTopContent: {
        type: PropType<TdSelectProps["panelTopContent"]>;
    };
    placeholder: {
        type: StringConstructor;
        default: any;
    };
    popupProps: {
        type: PropType<TdSelectProps["popupProps"]>;
    };
    popupVisible: {
        type: BooleanConstructor;
        default: any;
    };
    defaultPopupVisible: BooleanConstructor;
    prefixIcon: {
        type: PropType<TdSelectProps["prefixIcon"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    reserveKeyword: BooleanConstructor;
    scroll: {
        type: PropType<TdSelectProps["scroll"]>;
    };
    selectInputProps: {
        type: PropType<TdSelectProps["selectInputProps"]>;
    };
    showArrow: {
        type: BooleanConstructor;
        default: boolean;
    };
    size: {
        type: PropType<TdSelectProps["size"]>;
        default: TdSelectProps["size"];
        validator(val: TdSelectProps["size"]): boolean;
    };
    status: {
        type: PropType<TdSelectProps["status"]>;
        default: TdSelectProps["status"];
        validator(val: TdSelectProps["status"]): boolean;
    };
    suffix: {
        type: PropType<TdSelectProps["suffix"]>;
    };
    suffixIcon: {
        type: PropType<TdSelectProps["suffixIcon"]>;
    };
    tagInputProps: {
        type: PropType<TdSelectProps["tagInputProps"]>;
    };
    tagProps: {
        type: PropType<TdSelectProps["tagProps"]>;
    };
    tips: {
        type: PropType<TdSelectProps["tips"]>;
    };
    value: {
        type: PropType<TdSelectProps["value"]>;
        default: TdSelectProps["value"];
    };
    modelValue: {
        type: PropType<TdSelectProps["value"]>;
        default: TdSelectProps["value"];
    };
    defaultValue: {
        type: PropType<TdSelectProps["defaultValue"]>;
        default: any;
    };
    valueDisplay: {
        type: PropType<TdSelectProps["valueDisplay"]>;
    };
    valueType: {
        type: PropType<TdSelectProps["valueType"]>;
        default: TdSelectProps["valueType"];
        validator(val: TdSelectProps["valueType"]): boolean;
    };
    onBlur: PropType<TdSelectProps["onBlur"]>;
    onChange: PropType<TdSelectProps["onChange"]>;
    onClear: PropType<TdSelectProps["onClear"]>;
    onCreate: PropType<TdSelectProps["onCreate"]>;
    onEnter: PropType<TdSelectProps["onEnter"]>;
    onFocus: PropType<TdSelectProps["onFocus"]>;
    onInputChange: PropType<TdSelectProps["onInputChange"]>;
    onPopupVisibleChange: PropType<TdSelectProps["onPopupVisibleChange"]>;
    onRemove: PropType<TdSelectProps["onRemove"]>;
    onSearch: PropType<TdSelectProps["onSearch"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    valueDisplayOptions: {
        type: PropType<SelectInputValueDisplayOptions>;
    };
    autoWidth: BooleanConstructor;
    autofocus: BooleanConstructor;
    borderless: BooleanConstructor;
    clearable: BooleanConstructor;
    collapsedItems: {
        type: PropType<TdSelectProps["collapsedItems"]>;
    };
    creatable: BooleanConstructor;
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    empty: {
        type: PropType<TdSelectProps["empty"]>;
    };
    filter: {
        type: PropType<TdSelectProps["filter"]>;
    };
    filterable: BooleanConstructor;
    inputProps: {
        type: PropType<TdSelectProps["inputProps"]>;
    };
    inputValue: {
        type: PropType<TdSelectProps["inputValue"]>;
        default: TdSelectProps["inputValue"];
    };
    defaultInputValue: {
        type: PropType<TdSelectProps["defaultInputValue"]>;
    };
    keys: {
        type: PropType<TdSelectProps["keys"]>;
    };
    label: {
        type: PropType<TdSelectProps["label"]>;
    };
    loading: BooleanConstructor;
    loadingText: {
        type: PropType<TdSelectProps["loadingText"]>;
    };
    max: {
        type: NumberConstructor;
        default: number;
    };
    minCollapsedNum: {
        type: NumberConstructor;
        default: number;
    };
    multiple: BooleanConstructor;
    options: {
        type: PropType<TdSelectProps["options"]>;
    };
    panelBottomContent: {
        type: PropType<TdSelectProps["panelBottomContent"]>;
    };
    panelTopContent: {
        type: PropType<TdSelectProps["panelTopContent"]>;
    };
    placeholder: {
        type: StringConstructor;
        default: any;
    };
    popupProps: {
        type: PropType<TdSelectProps["popupProps"]>;
    };
    popupVisible: {
        type: BooleanConstructor;
        default: any;
    };
    defaultPopupVisible: BooleanConstructor;
    prefixIcon: {
        type: PropType<TdSelectProps["prefixIcon"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    reserveKeyword: BooleanConstructor;
    scroll: {
        type: PropType<TdSelectProps["scroll"]>;
    };
    selectInputProps: {
        type: PropType<TdSelectProps["selectInputProps"]>;
    };
    showArrow: {
        type: BooleanConstructor;
        default: boolean;
    };
    size: {
        type: PropType<TdSelectProps["size"]>;
        default: TdSelectProps["size"];
        validator(val: TdSelectProps["size"]): boolean;
    };
    status: {
        type: PropType<TdSelectProps["status"]>;
        default: TdSelectProps["status"];
        validator(val: TdSelectProps["status"]): boolean;
    };
    suffix: {
        type: PropType<TdSelectProps["suffix"]>;
    };
    suffixIcon: {
        type: PropType<TdSelectProps["suffixIcon"]>;
    };
    tagInputProps: {
        type: PropType<TdSelectProps["tagInputProps"]>;
    };
    tagProps: {
        type: PropType<TdSelectProps["tagProps"]>;
    };
    tips: {
        type: PropType<TdSelectProps["tips"]>;
    };
    value: {
        type: PropType<TdSelectProps["value"]>;
        default: TdSelectProps["value"];
    };
    modelValue: {
        type: PropType<TdSelectProps["value"]>;
        default: TdSelectProps["value"];
    };
    defaultValue: {
        type: PropType<TdSelectProps["defaultValue"]>;
        default: any;
    };
    valueDisplay: {
        type: PropType<TdSelectProps["valueDisplay"]>;
    };
    valueType: {
        type: PropType<TdSelectProps["valueType"]>;
        default: TdSelectProps["valueType"];
        validator(val: TdSelectProps["valueType"]): boolean;
    };
    onBlur: PropType<TdSelectProps["onBlur"]>;
    onChange: PropType<TdSelectProps["onChange"]>;
    onClear: PropType<TdSelectProps["onClear"]>;
    onCreate: PropType<TdSelectProps["onCreate"]>;
    onEnter: PropType<TdSelectProps["onEnter"]>;
    onFocus: PropType<TdSelectProps["onFocus"]>;
    onInputChange: PropType<TdSelectProps["onInputChange"]>;
    onPopupVisibleChange: PropType<TdSelectProps["onPopupVisibleChange"]>;
    onRemove: PropType<TdSelectProps["onRemove"]>;
    onSearch: PropType<TdSelectProps["onSearch"]>;
}>>, {
    value: string | number | boolean | import("..").PlainObject | TdOptionProps | import("./type").SelectOptionGroup | SelectValue<import("./type").SelectOption>[];
    disabled: boolean;
    valueType: "object" | "value";
    multiple: boolean;
    max: number;
    size: import("..").SizeEnum;
    status: "error" | "default" | "success" | "warning";
    loading: boolean;
    defaultValue: any;
    placeholder: string;
    modelValue: string | number | boolean | import("..").PlainObject | TdOptionProps | import("./type").SelectOptionGroup | SelectValue<import("./type").SelectOption>[];
    readonly: boolean;
    showArrow: boolean;
    autoWidth: boolean;
    autofocus: boolean;
    borderless: boolean;
    clearable: boolean;
    inputValue: string;
    minCollapsedNum: number;
    popupVisible: boolean;
    defaultPopupVisible: boolean;
    reserveKeyword: boolean;
    creatable: boolean;
    filterable: boolean;
}, {}>;
export default _default;
