import { TdStepItemProps } from '../steps/type';
import { PropType } from 'vue';
declare const _default: {
    content: {
        type: PropType<TdStepItemProps["content"]>;
        default: TdStepItemProps["content"];
    };
    default: {
        type: PropType<TdStepItemProps["default"]>;
    };
    extra: {
        type: PropType<TdStepItemProps["extra"]>;
    };
    icon: {
        type: PropType<TdStepItemProps["icon"]>;
        default: TdStepItemProps["icon"];
    };
    status: {
        type: PropType<TdStepItemProps["status"]>;
        default: TdStepItemProps["status"];
        validator(val: TdStepItemProps["status"]): boolean;
    };
    title: {
        type: PropType<TdStepItemProps["title"]>;
        default: TdStepItemProps["title"];
    };
    value: {
        type: PropType<TdStepItemProps["value"]>;
    };
};
export default _default;
