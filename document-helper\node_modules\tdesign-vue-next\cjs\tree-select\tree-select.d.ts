import { TreeSelectValue, TdTreeSelectProps } from './type';
import { TreeOptionData } from '../common';
declare const _default: import("vue").DefineComponent<{
    autoWidth: BooleanConstructor;
    borderless: BooleanConstructor;
    clearable: BooleanConstructor;
    collapsedItems: {
        type: import("vue").PropType<TdTreeSelectProps["collapsedItems"]>;
    };
    data: {
        type: import("vue").PropType<TdTreeSelectProps["data"]>;
        default: () => TdTreeSelectProps["data"];
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    empty: {
        type: import("vue").PropType<TdTreeSelectProps["empty"]>;
        default: string;
    };
    filter: {
        type: import("vue").PropType<TdTreeSelectProps["filter"]>;
    };
    filterable: BooleanConstructor;
    inputProps: {
        type: import("vue").PropType<TdTreeSelectProps["inputProps"]>;
    };
    inputValue: {
        type: import("vue").PropType<TdTreeSelectProps["inputValue"]>;
        default: any;
    };
    defaultInputValue: {
        type: import("vue").PropType<TdTreeSelectProps["defaultInputValue"]>;
    };
    keys: {
        type: import("vue").PropType<TdTreeSelectProps["keys"]>;
    };
    loading: BooleanConstructor;
    loadingText: {
        type: import("vue").PropType<TdTreeSelectProps["loadingText"]>;
        default: string;
    };
    max: {
        type: NumberConstructor;
        default: number;
    };
    minCollapsedNum: {
        type: NumberConstructor;
        default: number;
    };
    multiple: BooleanConstructor;
    panelBottomContent: {
        type: import("vue").PropType<TdTreeSelectProps["panelBottomContent"]>;
    };
    panelTopContent: {
        type: import("vue").PropType<TdTreeSelectProps["panelTopContent"]>;
    };
    placeholder: {
        type: StringConstructor;
        default: any;
    };
    popupProps: {
        type: import("vue").PropType<TdTreeSelectProps["popupProps"]>;
    };
    popupVisible: {
        type: BooleanConstructor;
        default: any;
    };
    prefixIcon: {
        type: import("vue").PropType<TdTreeSelectProps["prefixIcon"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    selectInputProps: {
        type: import("vue").PropType<TdTreeSelectProps["selectInputProps"]>;
    };
    size: {
        type: import("vue").PropType<TdTreeSelectProps["size"]>;
        default: TdTreeSelectProps["size"];
        validator(val: TdTreeSelectProps["size"]): boolean;
    };
    suffix: {
        type: import("vue").PropType<TdTreeSelectProps["suffix"]>;
    };
    suffixIcon: {
        type: import("vue").PropType<TdTreeSelectProps["suffixIcon"]>;
    };
    tagProps: {
        type: import("vue").PropType<TdTreeSelectProps["tagProps"]>;
    };
    treeProps: {
        type: import("vue").PropType<TdTreeSelectProps["treeProps"]>;
    };
    value: {
        type: import("vue").PropType<TdTreeSelectProps["value"]>;
        default: TdTreeSelectProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<TdTreeSelectProps["value"]>;
        default: TdTreeSelectProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<TdTreeSelectProps["defaultValue"]>;
    };
    valueDisplay: {
        type: import("vue").PropType<TdTreeSelectProps["valueDisplay"]>;
    };
    valueType: {
        type: import("vue").PropType<TdTreeSelectProps["valueType"]>;
        default: TdTreeSelectProps["valueType"];
        validator(val: TdTreeSelectProps["valueType"]): boolean;
    };
    onBlur: import("vue").PropType<TdTreeSelectProps["onBlur"]>;
    onChange: import("vue").PropType<TdTreeSelectProps["onChange"]>;
    onClear: import("vue").PropType<TdTreeSelectProps["onClear"]>;
    onFocus: import("vue").PropType<TdTreeSelectProps["onFocus"]>;
    onInputChange: import("vue").PropType<TdTreeSelectProps["onInputChange"]>;
    onPopupVisibleChange: import("vue").PropType<TdTreeSelectProps["onPopupVisibleChange"]>;
    onRemove: import("vue").PropType<TdTreeSelectProps["onRemove"]>;
    onSearch: import("vue").PropType<TdTreeSelectProps["onSearch"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    autoWidth: BooleanConstructor;
    borderless: BooleanConstructor;
    clearable: BooleanConstructor;
    collapsedItems: {
        type: import("vue").PropType<TdTreeSelectProps["collapsedItems"]>;
    };
    data: {
        type: import("vue").PropType<TdTreeSelectProps["data"]>;
        default: () => TdTreeSelectProps["data"];
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    empty: {
        type: import("vue").PropType<TdTreeSelectProps["empty"]>;
        default: string;
    };
    filter: {
        type: import("vue").PropType<TdTreeSelectProps["filter"]>;
    };
    filterable: BooleanConstructor;
    inputProps: {
        type: import("vue").PropType<TdTreeSelectProps["inputProps"]>;
    };
    inputValue: {
        type: import("vue").PropType<TdTreeSelectProps["inputValue"]>;
        default: any;
    };
    defaultInputValue: {
        type: import("vue").PropType<TdTreeSelectProps["defaultInputValue"]>;
    };
    keys: {
        type: import("vue").PropType<TdTreeSelectProps["keys"]>;
    };
    loading: BooleanConstructor;
    loadingText: {
        type: import("vue").PropType<TdTreeSelectProps["loadingText"]>;
        default: string;
    };
    max: {
        type: NumberConstructor;
        default: number;
    };
    minCollapsedNum: {
        type: NumberConstructor;
        default: number;
    };
    multiple: BooleanConstructor;
    panelBottomContent: {
        type: import("vue").PropType<TdTreeSelectProps["panelBottomContent"]>;
    };
    panelTopContent: {
        type: import("vue").PropType<TdTreeSelectProps["panelTopContent"]>;
    };
    placeholder: {
        type: StringConstructor;
        default: any;
    };
    popupProps: {
        type: import("vue").PropType<TdTreeSelectProps["popupProps"]>;
    };
    popupVisible: {
        type: BooleanConstructor;
        default: any;
    };
    prefixIcon: {
        type: import("vue").PropType<TdTreeSelectProps["prefixIcon"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    selectInputProps: {
        type: import("vue").PropType<TdTreeSelectProps["selectInputProps"]>;
    };
    size: {
        type: import("vue").PropType<TdTreeSelectProps["size"]>;
        default: TdTreeSelectProps["size"];
        validator(val: TdTreeSelectProps["size"]): boolean;
    };
    suffix: {
        type: import("vue").PropType<TdTreeSelectProps["suffix"]>;
    };
    suffixIcon: {
        type: import("vue").PropType<TdTreeSelectProps["suffixIcon"]>;
    };
    tagProps: {
        type: import("vue").PropType<TdTreeSelectProps["tagProps"]>;
    };
    treeProps: {
        type: import("vue").PropType<TdTreeSelectProps["treeProps"]>;
    };
    value: {
        type: import("vue").PropType<TdTreeSelectProps["value"]>;
        default: TdTreeSelectProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<TdTreeSelectProps["value"]>;
        default: TdTreeSelectProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<TdTreeSelectProps["defaultValue"]>;
    };
    valueDisplay: {
        type: import("vue").PropType<TdTreeSelectProps["valueDisplay"]>;
    };
    valueType: {
        type: import("vue").PropType<TdTreeSelectProps["valueType"]>;
        default: TdTreeSelectProps["valueType"];
        validator(val: TdTreeSelectProps["valueType"]): boolean;
    };
    onBlur: import("vue").PropType<TdTreeSelectProps["onBlur"]>;
    onChange: import("vue").PropType<TdTreeSelectProps["onChange"]>;
    onClear: import("vue").PropType<TdTreeSelectProps["onClear"]>;
    onFocus: import("vue").PropType<TdTreeSelectProps["onFocus"]>;
    onInputChange: import("vue").PropType<TdTreeSelectProps["onInputChange"]>;
    onPopupVisibleChange: import("vue").PropType<TdTreeSelectProps["onPopupVisibleChange"]>;
    onRemove: import("vue").PropType<TdTreeSelectProps["onRemove"]>;
    onSearch: import("vue").PropType<TdTreeSelectProps["onSearch"]>;
}>>, {
    value: TreeSelectValue;
    disabled: boolean;
    data: TreeOptionData[];
    valueType: "object" | "value";
    multiple: boolean;
    max: number;
    size: "small" | "medium" | "large";
    loading: boolean;
    placeholder: string;
    empty: string | ((h: typeof import("vue").h) => import("..").TNodeReturnValue);
    modelValue: TreeSelectValue;
    readonly: boolean;
    autoWidth: boolean;
    borderless: boolean;
    clearable: boolean;
    inputValue: string;
    minCollapsedNum: number;
    popupVisible: boolean;
    loadingText: string | ((h: typeof import("vue").h) => import("..").TNodeReturnValue);
    filterable: boolean;
}, {}>;
export default _default;
