{"version": 3, "file": "Footer.js", "sources": ["../../../../../components/date-picker/components/base/Footer.tsx"], "sourcesContent": ["import { defineComponent, computed, PropType } from 'vue';\nimport { useConfig, usePrefixClass } from '@tdesign/shared-hooks';\nimport TButton from '../../../button';\n\nimport type { TdDatePickerProps } from '../../type';\n\nexport default defineComponent({\n  name: 'TDatePickerTable',\n  props: {\n    enableTimePicker: Boolean,\n    presetsPlacement: String,\n    presets: Object,\n    needConfirm: {\n      type: Boolean,\n      default: true,\n    },\n    selectedValue: [String, Number, Array, Date] as PropType<TdDatePickerProps['value']>,\n    onPresetClick: Function,\n    onConfirmClick: Function,\n  },\n  setup(props) {\n    const COMPONENT_NAME = usePrefixClass('date-picker__footer');\n    const presetsClass = usePrefixClass('date-picker__presets');\n    const { t, globalConfig } = useConfig('datePicker');\n\n    const footerClass = computed(() => [COMPONENT_NAME.value, `${COMPONENT_NAME.value}--${props.presetsPlacement}`]);\n\n    return () => (\n      <div class={footerClass.value}>\n        {\n          <div class={presetsClass.value}>\n            {props.presets &&\n              Object.keys(props.presets).map((key: string) => (\n                <TButton\n                  key={key}\n                  size=\"small\"\n                  variant=\"text\"\n                  onClick={(e: MouseEvent) =>\n                    props.onPresetClick?.(props.presets[key], { e, preset: { [key]: props.presets[key] } })\n                  }\n                >\n                  {key}\n                </TButton>\n              ))}\n          </div>\n        }\n        {props.enableTimePicker && props.needConfirm && (\n          <TButton\n            disabled={!props.selectedValue}\n            size=\"small\"\n            theme=\"primary\"\n            onClick={(e: MouseEvent) => props.onConfirmClick?.({ e })}\n          >\n            {t(globalConfig.value.confirm)}\n          </TButton>\n        )}\n      </div>\n    );\n  },\n});\n"], "names": ["_isSlot", "s", "Object", "prototype", "toString", "call", "_isVNode", "defineComponent", "name", "props", "enableTimePicker", "Boolean", "presetsPlacement", "String", "presets", "needConfirm", "type", "selected<PERSON><PERSON><PERSON>", "Number", "Array", "Date", "onPresetClick", "Function", "onConfirmClick", "setup", "COMPONENT_NAME", "usePrefixClass", "presetsClass", "_useConfig", "useConfig", "t", "globalConfig", "footerClass", "computed", "value", "concat", "_slot", "_createVNode", "keys", "map", "key", "TButton", "onClick", "e", "_props$onPresetClick", "preset", "_defineProperty", "_default", "_props$onConfirmClick", "confirm"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEoB,SAAAA,QAAAC,CAAA,EAAA;AAAA,EAAA,OAAA,OAAAA,CAAA,KAAA,UAAA,IAAAC,MAAA,CAAAC,SAAA,CAAAC,QAAA,CAAAC,IAAA,CAAAJ,CAAA,CAAAK,KAAAA,iBAAAA,IAAAA,CAAAA,WAAA,CAAAL,CAAA,CAAA,CAAA;AAAA,CAAA;AAIpB,kBAAeM,mBAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,kBAAA;AACNC,EAAAA,KAAO,EAAA;AACLC,IAAAA,gBAAkB,EAAAC,OAAA;AAClBC,IAAAA,gBAAkB,EAAAC,MAAA;AAClBC,IAAAA,OAAS,EAAAZ,MAAA;AACTa,IAAAA,WAAa,EAAA;AACXC,MAAAA,IAAM,EAAAL,OAAA;MACN,SAAS,EAAA,IAAA;KACX;IACAM,aAAe,EAAA,CAACJ,MAAQ,EAAAK,MAAA,EAAQC,OAAOC,IAAI,CAAA;AAC3CC,IAAAA,aAAe,EAAAC,QAAA;AACfC,IAAAA,cAAgB,EAAAD,QAAAA;GAClB;AACAE,EAAAA,OAAAA,SAAAA,MAAMf,KAAO,EAAA;AACL,IAAA,IAAAgB,cAAA,GAAiBC,qBAAe,qBAAqB,CAAA,CAAA;AACrD,IAAA,IAAAC,YAAA,GAAeD,qBAAe,sBAAsB,CAAA,CAAA;AAC1D,IAAA,IAAAE,UAAA,GAA4BC,yCAAU,YAAY,CAAA;MAA1CC,CAAA,GAAAF,UAAA,CAAAE,CAAA;MAAGC,YAAa,GAAAH,UAAA,CAAbG,YAAa,CAAA;IAElB,IAAAC,WAAA,GAAcC,YAAS,CAAA,YAAA;AAAA,MAAA,OAAM,CAACR,cAAA,CAAeS,KAAO,EAAA,EAAA,CAAAC,MAAA,CAAGV,cAAe,CAAAS,KAAA,QAAAC,MAAA,CAAU1B,KAAM,CAAAG,gBAAA,CAAmB,CAAA,CAAA;KAAA,CAAA,CAAA;IAE/G,OAAO,YAAA;AAAA,MAAA,IAAAwB,KAAA,CAAA;AAAA,MAAA,OAAAC,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EACOL,WAAY,CAAAE,KAAAA;AAAA,OAAA,EAAA,CAAAG,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAERV,YAAA,CAAaO,KAAAA;UACtBzB,KAAM,CAAAK,OAAA,IACLZ,OAAOoC,IAAK,CAAA7B,KAAA,CAAMK,OAAO,CAAE,CAAAyB,GAAA,CAAI,UAACC,GAC9B,EAAA;QAAA,OAAAH,eAAA,CAAAI,mBAAA,EAAA;AAAA,UAAA,KAAA,EACOD,GAAA;AAAA,UAAA,MAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA,MAAA;UAAA,SAGI,EAAA,SAAAE,QAACC,CAAA,EAAA;AAAA,YAAA,IAAAC,oBAAA,CAAA;YAAA,OAAAA,CAAAA,oBAAA,GACRnC,MAAMY,aAAgB,MAAAuB,IAAAA,IAAAA,oBAAA,uBAAtBA,oBAAA,CAAAvC,IAAA,CAAAI,OAAsBA,KAAA,CAAMK,QAAQ0B,GAAM,CAAA,EAAA;AAAEG,cAAAA,CAAG,EAAHA,CAAG;cAAAE,MAAA,EAAAC,mCAAA,CAAA,EAAA,EAAWN,KAAM/B,KAAM,CAAAK,OAAA,CAAQ0B;AAAO,aAAC;;mBAGvFA,GACH,CAAA,GADGA,GACH,GAAA;AAAA,UAAA,SAAA,EAAA,SAAAO,QAAA,GAAA;AAAA,YAAA,OAAA,CADGP,GACH,CAAA,CAAA;AAAA,WAAA;AAAA,SAAA,CAAA,CAAA;AAAA,OACD,CAAA,CAAA,CAAA,EAGN/B,KAAM,CAAAC,gBAAA,IAAoBD,KAAM,CAAAM,WAAA,IAAAsB,eAAA,CAAAI,mBAAA,EAAA;QAAA,UAEnB,EAAA,CAAChC,KAAM,CAAAQ,aAAA;AAAA,QAAA,MAAA,EAAA,OAAA;AAAA,QAAA,OAAA,EAAA,SAAA;QAAA,SAGR,EAAA,SAAAyB,QAACC,CAAkB,EAAA;AAAA,UAAA,IAAAK,qBAAA,CAAA;AAAA,UAAA,OAAA,CAAAA,qBAAA,GAAAvC,KAAA,CAAMc,cAAiB,MAAA,IAAA,IAAAyB,qBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAvBA,qBAAA,CAAA3C,IAAA,CAAAI,KAAA,EAAuB;AAAEkC,YAAAA,CAAE,EAAFA,CAAAA;AAAE,WAAC,CAEvD,CAAA;AAAA,SAAA;AAAA,OAAA,EAAA3C,OAAA,CAAAoC,KAAA,GAAAN,CAAA,CAAEC,YAAa,CAAAG,KAAA,CAAMe,OAAO,CAAA,IAAAb,KAAA,GAAA;AAAA,QAAA,SAAA,EAAA,SAAAW,QAAA,GAAA;AAAA,UAAA,OAAA,CAAAX,KAAA,CAAA,CAAA;AAAA,SAAA;OAN9B,CAAA,CAAA,CAAA,CAAA;KAnBJ,CAAA;AA8BL,GAAA;AACF,CAAC,CAAA;;;;"}