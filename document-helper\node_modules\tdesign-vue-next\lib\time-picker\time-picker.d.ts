import { TdTimePickerProps } from './type';
declare const _default: import("vue").DefineComponent<{
    allowInput: BooleanConstructor;
    borderless: BooleanConstructor;
    clearable: BooleanConstructor;
    disableTime: {
        type: import("vue").PropType<TdTimePickerProps["disableTime"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    format: {
        type: StringConstructor;
        default: string;
    };
    hideDisabledTime: {
        type: BooleanConstructor;
        default: boolean;
    };
    inputProps: {
        type: import("vue").PropType<TdTimePickerProps["inputProps"]>;
    };
    label: {
        type: import("vue").PropType<TdTimePickerProps["label"]>;
    };
    placeholder: {
        type: StringConstructor;
        default: any;
    };
    popupProps: {
        type: import("vue").PropType<TdTimePickerProps["popupProps"]>;
    };
    presets: {
        type: import("vue").PropType<TdTimePickerProps["presets"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    selectInputProps: {
        type: import("vue").PropType<TdTimePickerProps["selectInputProps"]>;
    };
    size: {
        type: import("vue").PropType<TdTimePickerProps["size"]>;
        default: TdTimePickerProps["size"];
        validator(val: TdTimePickerProps["size"]): boolean;
    };
    status: {
        type: import("vue").PropType<TdTimePickerProps["status"]>;
        default: TdTimePickerProps["status"];
        validator(val: TdTimePickerProps["status"]): boolean;
    };
    steps: {
        type: import("vue").PropType<TdTimePickerProps["steps"]>;
        default: () => TdTimePickerProps["steps"];
    };
    tips: {
        type: import("vue").PropType<TdTimePickerProps["tips"]>;
    };
    value: {
        type: import("vue").PropType<TdTimePickerProps["value"]>;
        default: TdTimePickerProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<TdTimePickerProps["value"]>;
        default: TdTimePickerProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<TdTimePickerProps["defaultValue"]>;
        default: TdTimePickerProps["defaultValue"];
    };
    valueDisplay: {
        type: import("vue").PropType<TdTimePickerProps["valueDisplay"]>;
    };
    onBlur: import("vue").PropType<TdTimePickerProps["onBlur"]>;
    onChange: import("vue").PropType<TdTimePickerProps["onChange"]>;
    onClear: import("vue").PropType<TdTimePickerProps["onClear"]>;
    onClose: import("vue").PropType<TdTimePickerProps["onClose"]>;
    onConfirm: import("vue").PropType<TdTimePickerProps["onConfirm"]>;
    onFocus: import("vue").PropType<TdTimePickerProps["onFocus"]>;
    onInput: import("vue").PropType<TdTimePickerProps["onInput"]>;
    onOpen: import("vue").PropType<TdTimePickerProps["onOpen"]>;
    onPick: import("vue").PropType<TdTimePickerProps["onPick"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    allowInput: BooleanConstructor;
    borderless: BooleanConstructor;
    clearable: BooleanConstructor;
    disableTime: {
        type: import("vue").PropType<TdTimePickerProps["disableTime"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    format: {
        type: StringConstructor;
        default: string;
    };
    hideDisabledTime: {
        type: BooleanConstructor;
        default: boolean;
    };
    inputProps: {
        type: import("vue").PropType<TdTimePickerProps["inputProps"]>;
    };
    label: {
        type: import("vue").PropType<TdTimePickerProps["label"]>;
    };
    placeholder: {
        type: StringConstructor;
        default: any;
    };
    popupProps: {
        type: import("vue").PropType<TdTimePickerProps["popupProps"]>;
    };
    presets: {
        type: import("vue").PropType<TdTimePickerProps["presets"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    selectInputProps: {
        type: import("vue").PropType<TdTimePickerProps["selectInputProps"]>;
    };
    size: {
        type: import("vue").PropType<TdTimePickerProps["size"]>;
        default: TdTimePickerProps["size"];
        validator(val: TdTimePickerProps["size"]): boolean;
    };
    status: {
        type: import("vue").PropType<TdTimePickerProps["status"]>;
        default: TdTimePickerProps["status"];
        validator(val: TdTimePickerProps["status"]): boolean;
    };
    steps: {
        type: import("vue").PropType<TdTimePickerProps["steps"]>;
        default: () => TdTimePickerProps["steps"];
    };
    tips: {
        type: import("vue").PropType<TdTimePickerProps["tips"]>;
    };
    value: {
        type: import("vue").PropType<TdTimePickerProps["value"]>;
        default: TdTimePickerProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<TdTimePickerProps["value"]>;
        default: TdTimePickerProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<TdTimePickerProps["defaultValue"]>;
        default: TdTimePickerProps["defaultValue"];
    };
    valueDisplay: {
        type: import("vue").PropType<TdTimePickerProps["valueDisplay"]>;
    };
    onBlur: import("vue").PropType<TdTimePickerProps["onBlur"]>;
    onChange: import("vue").PropType<TdTimePickerProps["onChange"]>;
    onClear: import("vue").PropType<TdTimePickerProps["onClear"]>;
    onClose: import("vue").PropType<TdTimePickerProps["onClose"]>;
    onConfirm: import("vue").PropType<TdTimePickerProps["onConfirm"]>;
    onFocus: import("vue").PropType<TdTimePickerProps["onFocus"]>;
    onInput: import("vue").PropType<TdTimePickerProps["onInput"]>;
    onOpen: import("vue").PropType<TdTimePickerProps["onOpen"]>;
    onPick: import("vue").PropType<TdTimePickerProps["onPick"]>;
}>>, {
    value: string;
    format: string;
    disabled: boolean;
    size: "small" | "medium" | "large";
    status: "error" | "default" | "success" | "warning";
    defaultValue: string;
    placeholder: string;
    modelValue: string;
    readonly: boolean;
    steps: (string | number)[];
    borderless: boolean;
    clearable: boolean;
    allowInput: boolean;
    hideDisabledTime: boolean;
}, {}>;
export default _default;
