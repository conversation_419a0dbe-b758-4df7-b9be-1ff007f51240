import { PropType } from 'vue';
import { TdSliderProps } from './type';
declare const _default: import("vue").DefineComponent<{
    mark: {
        type: PropType<TdSliderProps["marks"]>;
    };
    point: {
        type: NumberConstructor;
    };
    onClickMarkPoint: {
        type: FunctionConstructor;
        default: () => void;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    mark: {
        type: PropType<TdSliderProps["marks"]>;
    };
    point: {
        type: NumberConstructor;
    };
    onClickMarkPoint: {
        type: FunctionConstructor;
        default: () => void;
    };
}>>, {
    onClickMarkPoint: Function;
}, {}>;
export default _default;
