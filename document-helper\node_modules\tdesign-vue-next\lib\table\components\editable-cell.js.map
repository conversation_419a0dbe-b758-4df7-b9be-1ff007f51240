{"version": 3, "file": "editable-cell.js", "sources": ["../../../../components/table/components/editable-cell.tsx"], "sourcesContent": ["import { computed, defineComponent, onMounted, PropType, ref, SetupContext, toRefs, watch } from 'vue';\nimport { get, set, isFunction, cloneDeep, isObject } from 'lodash-es';\nimport { Edit1Icon as TdEdit1Icon } from 'tdesign-icons-vue-next';\nimport {\n  TableRowData,\n  PrimaryTableCol,\n  PrimaryTableRowEditContext,\n  PrimaryTableRowValidateContext,\n  TdBaseTableProps,\n  TableEditableCellPropsParams,\n} from '../type';\nimport { TableClassName } from '../hooks/useClassName';\nimport { useGlobalIcon, usePrefixClass } from '@tdesign/shared-hooks';\nimport { renderCell } from './tr';\nimport { validate } from '../../form/utils/form-model';\nimport log from '@tdesign/common-js/log/index';\nimport { AllValidateResult } from '../../form/type';\nimport { on, off } from '@tdesign/shared-utils';\n\nexport interface OnEditableChangeContext<T> extends PrimaryTableRowEditContext<T> {\n  isEdit: boolean;\n  validateEdit: (trigger: 'self' | 'parent') => Promise<true | AllValidateResult[]>;\n}\n\nexport interface EditableCellProps {\n  rowKey: string;\n  row: TableRowData;\n  rowIndex: number;\n  col: PrimaryTableCol<TableRowData>;\n  colIndex: number;\n  oldCell: PrimaryTableCol<TableRowData>['cell'];\n  tableBaseClass?: TableClassName['tableBaseClass'];\n  /** 行编辑需要使用 editable。单元格编辑则无需使用，设置为 undefined */\n  editable?: boolean;\n  readonly?: boolean;\n  errors?: AllValidateResult[];\n  cellEmptyContent?: TdBaseTableProps['cellEmptyContent'];\n  /** 编辑数据时触发 */\n  onChange?: (context: PrimaryTableRowEditContext<TableRowData>) => void;\n  /** 校验结束后触发 */\n  onValidate?: (context: PrimaryTableRowValidateContext<TableRowData>) => void;\n  /** 校验规则发生变化时触发 */\n  onRuleChange?: (context: PrimaryTableRowEditContext<TableRowData>) => void;\n  /** 进入或退出编辑态时触发 */\n  onEditableChange?: (context: OnEditableChangeContext<TableRowData>) => void;\n}\n\nexport default defineComponent({\n  name: 'TableEditableCell',\n  props: {\n    row: Object as PropType<EditableCellProps['row']>,\n    rowKey: String,\n    rowIndex: Number,\n    col: Object as PropType<EditableCellProps['col']>,\n    colIndex: Number,\n    oldCell: [Function, String] as PropType<EditableCellProps['oldCell']>,\n    tableBaseClass: Object as PropType<EditableCellProps['tableBaseClass']>,\n    cellEmptyContent: [Function, String] as PropType<EditableCellProps['cellEmptyContent']>,\n    editable: {\n      type: Boolean,\n      default: undefined,\n    },\n    readonly: {\n      type: Boolean,\n    },\n    errors: {\n      type: Array as PropType<EditableCellProps['errors']>,\n      default: undefined,\n    },\n    onChange: Function as PropType<EditableCellProps['onChange']>,\n    onValidate: Function as PropType<EditableCellProps['onValidate']>,\n    onRuleChange: Function as PropType<EditableCellProps['onRuleChange']>,\n    onEditableChange: Function as PropType<EditableCellProps['onEditableChange']>,\n  },\n\n  emits: ['update-edited-cell'],\n\n  setup(props: EditableCellProps, context: SetupContext) {\n    const { row, col } = toRefs(props);\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const tableEditableCellRef = ref(null);\n    const isKeepEditMode = computed(() => col.value.edit?.keepEditMode);\n    const isEdit = ref(isKeepEditMode.value || props.col.edit?.defaultEditable || false);\n    const editValue = ref();\n    const errorList = ref<AllValidateResult[]>();\n    const classPrefix = usePrefixClass();\n\n    const { Edit1Icon } = useGlobalIcon({ Edit1Icon: TdEdit1Icon });\n\n    const updateEditedCellValue: TableEditableCellPropsParams<TableRowData>['updateEditedCellValue'] = (obj) => {\n      if (typeof obj === 'object' && ('rowValue' in obj || obj.isUpdateCurrentRow)) {\n        const rowValue = obj.isUpdateCurrentRow ? get(row.value, props.rowKey) : obj.rowValue;\n        delete obj.rowValue;\n        delete obj.isUpdateCurrentRow;\n        context.emit('update-edited-cell', rowValue, row.value, obj);\n      } else {\n        editValue.value = obj;\n      }\n    };\n\n    watch([isKeepEditMode], (val) => {\n      if (val) {\n        isEdit.value = true;\n      }\n    });\n\n    const editOnListeners = computed(() => {\n      return col.value.edit?.on?.({ ...cellParams.value, editedRow: currentRow.value, updateEditedCellValue }) || {};\n    });\n\n    const cellParams = computed(() => ({\n      rowIndex: props.rowIndex,\n      colIndex: props.colIndex,\n      col: props.col,\n      row: props.row,\n    }));\n\n    const currentRow = computed(() => {\n      const { colKey } = col.value;\n      // handle colKey like a.b.c\n      const [firstKey, ...restKeys] = colKey.split('.') || [];\n      const newRow = { ...row.value };\n      if (restKeys.length) {\n        newRow[firstKey] = cloneDeep(row.value[firstKey]);\n        set(newRow[firstKey], restKeys.join('.'), editValue.value);\n      } else {\n        set(newRow, colKey, editValue.value);\n      }\n      return newRow;\n    });\n\n    const cellNode = computed(() => {\n      const node = renderCell(\n        {\n          row: currentRow.value,\n          col: { ...col.value, cell: props.oldCell },\n          rowIndex: props.rowIndex,\n          colIndex: props.colIndex,\n        },\n        context.slots,\n        { cellEmptyContent: props.cellEmptyContent },\n      );\n      return node;\n    });\n\n    const editProps = computed(() => {\n      const { edit } = col.value;\n      return isFunction(edit.props)\n        ? edit.props({\n            ...cellParams.value,\n            editedRow: currentRow.value,\n            updateEditedCellValue,\n          })\n        : { ...edit.props };\n    });\n\n    const componentProps = computed(() => {\n      const { edit } = col.value;\n      if (!edit) return {};\n      const tmpProps = { ...editProps.value };\n      // for removing warn: runtime-core.esm-bundler.js:38 [Vue warn]: Invalid prop: type check failed for prop \"onChange\". Expected Function, got Array\n      delete tmpProps.onChange;\n      delete tmpProps.value;\n      edit.abortEditOnEvent?.forEach((item) => {\n        delete tmpProps[item];\n      });\n      return tmpProps;\n    });\n\n    const isAbortEditOnChange = computed(() => {\n      const { edit } = col.value;\n      if (!edit) return false;\n      return Boolean(edit.abortEditOnEvent?.includes('onChange'));\n    });\n\n    const validateEdit = (trigger: 'self' | 'parent'): Promise<true | AllValidateResult[]> => {\n      return new Promise((resolve) => {\n        const params: PrimaryTableRowValidateContext<TableRowData> = {\n          result: [\n            {\n              ...cellParams.value,\n              errorList: [],\n              value: editValue.value,\n            },\n          ],\n          trigger,\n        };\n        const rules = isFunction(col.value.edit.rules) ? col.value.edit.rules(cellParams.value) : col.value.edit.rules;\n        if (!col.value.edit || !rules || !rules.length) {\n          props.onValidate?.(params);\n          resolve(true);\n          return;\n        }\n        validate(editValue.value, rules).then((result) => {\n          const list = result?.filter((t) => !t.result);\n          params.result[0].errorList = list;\n          props.onValidate?.(params);\n          if (!list || !list.length) {\n            errorList.value = [];\n            resolve(true);\n          } else {\n            errorList.value = list;\n            resolve(list);\n          }\n        });\n      });\n    };\n\n    const isSame = (a: any, b: any) => {\n      if (isObject(a) && isObject(b)) {\n        return JSON.stringify(a) === JSON.stringify(b);\n      }\n      return a === b;\n    };\n\n    const updateAndSaveAbort = (outsideAbortEvent: Function, eventName: string, ...args: any) => {\n      validateEdit('self').then((result) => {\n        if (result !== true) return;\n        const oldValue = get(row.value, col.value.colKey);\n        // 相同的值无需触发变化\n        if (!isSame(editValue.value, oldValue)) {\n          editValue.value = oldValue;\n          outsideAbortEvent?.(...args);\n        }\n        editOnListeners.value[eventName]?.(args[2]);\n        // 此处必须在事件执行完成后异步销毁编辑组件，否则会导致事件清除不及时引起的其他问题\n        const timer = setTimeout(() => {\n          if (!isKeepEditMode.value) {\n            isEdit.value = false;\n          }\n          errorList.value = [];\n          props.onEditableChange?.({\n            ...cellParams.value,\n            value: editValue.value,\n            editedRow: { ...props.row, [props.col.colKey]: editValue.value },\n            validateEdit,\n            isEdit: false,\n          });\n          clearTimeout(timer);\n        }, 0);\n      });\n    };\n\n    const listeners = computed<{ [key: string]: Function }>(() => {\n      const { edit } = col.value;\n      const isCellEditable = props.editable === undefined;\n      if (!isEdit.value || !isCellEditable) return;\n      if (!edit?.abortEditOnEvent?.length) return {};\n      // 自定义退出编辑态的事件\n      const tListeners: Record<string, Function> = {};\n      const outsideAbortEvent = edit?.onEdited;\n      edit.abortEditOnEvent.forEach((itemEvent) => {\n        if (itemEvent === 'onChange') return;\n        tListeners[itemEvent] = (...args: any) => {\n          updateAndSaveAbort(\n            outsideAbortEvent,\n            itemEvent,\n            {\n              ...cellParams.value,\n              trigger: itemEvent,\n              newRowData: currentRow.value,\n            },\n            ...args,\n          );\n        };\n      });\n\n      return tListeners;\n    });\n\n    // 数据输入时触发\n    const onEditChange = (val: any, ...args: any) => {\n      editValue.value = val;\n      const params = {\n        ...cellParams.value,\n        value: val,\n        editedRow: { ...props.row, [props.col.colKey]: val },\n      };\n      editProps.value?.onChange?.(val, ...args);\n      props.onChange?.(params);\n      props.onRuleChange?.(params);\n      editOnListeners.value?.onChange?.(params);\n      const isCellEditable = props.editable === undefined;\n      if (isCellEditable && isAbortEditOnChange.value) {\n        const outsideAbortEvent = col.value.edit?.onEdited;\n        updateAndSaveAbort(\n          outsideAbortEvent,\n          'change',\n          {\n            ...cellParams.value,\n            trigger: 'onChange',\n            newRowData: currentRow.value,\n          },\n          ...args,\n        );\n      }\n      if (col.value.edit?.validateTrigger === 'change') {\n        validateEdit('self');\n      }\n    };\n\n    const documentClickHandler = (e: MouseEvent) => {\n      if (!col.value.edit || !col.value.edit.component) return;\n      if (!isEdit.value) return;\n      // @ts-ignore some browser is also only support e.path\n      const path = e.composedPath?.() || e.path || [];\n      const node = path.find((node: HTMLElement) => node.classList?.contains(`${classPrefix.value}-popup__content`));\n      if (node) return;\n      const outsideAbortEvent = col.value.edit.onEdited;\n      updateAndSaveAbort(outsideAbortEvent, '', {\n        ...cellParams.value,\n        trigger: 'document',\n        newRowData: currentRow.value,\n      });\n    };\n\n    const enterEdit = () => {\n      props.onEditableChange?.({\n        ...cellParams.value,\n        value: editValue.value,\n        editedRow: props.row,\n        isEdit: true,\n        validateEdit,\n      });\n    };\n\n    const onCellClick = (e: MouseEvent) => {\n      isEdit.value = true;\n      enterEdit();\n      e.stopPropagation();\n    };\n\n    onMounted(() => {\n      if (props.col.edit?.defaultEditable) {\n        enterEdit();\n      }\n    });\n\n    const cellValue = computed(() => get(row.value, col.value.colKey));\n\n    watch(\n      cellValue,\n      (cellValue) => {\n        editValue.value = cellValue;\n      },\n      { immediate: true },\n    );\n\n    watch(\n      isEdit,\n      (isEdit) => {\n        const isCellEditable = props.editable === undefined;\n        if (!col.value.edit || !col.value.edit.component || !isCellEditable) return;\n        if (isEdit) {\n          on(document, 'click', documentClickHandler);\n        } else {\n          off(document, 'click', documentClickHandler);\n        }\n      },\n      { immediate: true },\n    );\n\n    watch(\n      () => [props.editable, props.row, props.col, props.rowIndex, props.colIndex],\n      ([editable]: [boolean]) => {\n        // 退出编辑态时，恢复原始值，等待父组件传入新的 data 值\n        if (editable === false) {\n          editValue.value = cellValue.value;\n        } else if (editable === true) {\n          props.onRuleChange?.({\n            ...cellParams.value,\n            value: cellValue.value,\n            editedRow: row.value,\n          });\n        }\n      },\n      { immediate: true },\n    );\n\n    watch(\n      () => props.errors,\n      (errors) => {\n        errorList.value = errors;\n      },\n    );\n\n    return () => {\n      if (props.readonly) {\n        return cellNode.value;\n      }\n      // props.editable = undefined 表示由组件内部控制编辑状态\n      if ((props.editable === undefined && !isEdit.value) || props.editable === false) {\n        return (\n          <div class={props.tableBaseClass.cellEditable} onClick={onCellClick}>\n            {cellNode.value}\n            {col.value.edit?.showEditIcon !== false && <Edit1Icon />}\n          </div>\n        );\n      }\n      const Component = col.value.edit?.component;\n      if (!Component) {\n        log.error('Table', 'edit.component is required.');\n        return null;\n      }\n      const errorMessage = errorList.value?.[0]?.message;\n      const tmpEditOnListeners = { ...editOnListeners.value };\n      delete tmpEditOnListeners.onChange;\n      // remove conflict events\n      if (col.value.edit?.abortEditOnEvent?.length) {\n        col.value.edit.abortEditOnEvent.forEach((onEventName) => {\n          if (tmpEditOnListeners[onEventName]) {\n            delete tmpEditOnListeners[onEventName];\n          }\n        });\n      }\n      return (\n        <div\n          class={props.tableBaseClass.cellEditWrap}\n          onClick={(e: MouseEvent) => {\n            e.stopPropagation();\n          }}\n          ref=\"tableEditableCellRef\"\n        >\n          <Component\n            status={errorMessage ? errorList.value?.[0]?.type || 'error' : undefined}\n            tips={errorMessage}\n            {...componentProps.value}\n            {...listeners.value}\n            {...tmpEditOnListeners}\n            value={editValue.value}\n            onChange={onEditChange}\n          />\n        </div>\n      );\n    };\n  },\n});\n"], "names": ["defineComponent", "name", "props", "row", "Object", "<PERSON><PERSON><PERSON>", "String", "rowIndex", "Number", "col", "colIndex", "oldCell", "Function", "tableBaseClass", "cellEmptyContent", "editable", "type", "Boolean", "readonly", "errors", "Array", "onChange", "onValidate", "onRuleChange", "onEditableChange", "emits", "setup", "context", "_props$col$edit", "_toRefs", "toRefs", "ref", "isKeepEditMode", "computed", "_col$value$edit", "value", "edit", "keepEditMode", "isEdit", "defaultEditable", "editValue", "errorList", "classPrefix", "usePrefixClass", "_useGlobalIcon", "useGlobalIcon", "Edit1Icon", "TdEdit1Icon", "updateEditedCellValue", "obj", "_typeof", "isUpdateCurrentRow", "rowValue", "get", "emit", "watch", "val", "editOnListeners", "_col$value$edit2", "_col$value$edit2$on", "on", "call", "_objectSpread", "cellParams", "editedRow", "currentRow", "co<PERSON><PERSON><PERSON>", "_ref", "split", "_ref2", "_toArray", "firstKey", "restKeys", "slice", "newRow", "length", "cloneDeep", "set", "join", "cellNode", "node", "renderCell", "cell", "slots", "editProps", "isFunction", "componentProps", "_edit$abortEditOnEven", "tmpProps", "abortEditOnEvent", "for<PERSON>ach", "item", "isAbortEditOnChange", "_edit$abortEditOnEven2", "includes", "validateEdit", "trigger", "Promise", "resolve", "params", "result", "rules", "_props$onValidate", "validate", "then", "_props$onValidate2", "list", "filter", "t", "isSame", "a", "b", "isObject", "JSON", "stringify", "updateAndSaveAbort", "outsideAbortEvent", "eventName", "_len", "arguments", "args", "_key", "_editOnListeners$valu", "_editOnListeners$valu2", "oldValue", "apply", "timer", "setTimeout", "_props$onEditableChan", "_defineProperty", "clearTimeout", "listeners", "_edit$abortEditOnEven3", "isCellEditable", "tListeners", "onEdited", "itemEvent", "_len2", "_key2", "newRowData", "concat", "onEditChange", "_editProps$value", "_editProps$value$onCh", "_props$onChange", "_props$onRuleChange", "_editOnListeners$valu3", "_editOnListeners$valu4", "_col$value$edit4", "_len3", "_key3", "_col$value$edit3", "validate<PERSON><PERSON>ger", "documentClickHandler", "e", "_e$composedPath", "component", "path", "<PERSON><PERSON><PERSON>", "find", "classList", "_node2$classList", "contains", "enterEdit", "_props$onEditableChan2", "onCellClick", "stopPropagation", "onMounted", "_props$col$edit2", "cellValue", "immediate", "document", "off", "_ref3", "_ref4", "_slicedToArray", "_props$onRuleChange2", "_col$value$edit6", "_errorList$value", "_col$value$edit7", "_errorList$value2", "_col$value$edit5", "_createVNode", "cellEditable", "showEditIcon", "Component", "log", "error", "errorMessage", "message", "tmpEditOnListeners", "onEventName", "cellEditWrap", "onClick", "_mergeProps"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA,mBAAeA,eAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,mBAAA;AACNC,EAAAA,KAAO,EAAA;AACLC,IAAAA,GAAK,EAAAC,MAAA;AACLC,IAAAA,MAAQ,EAAAC,MAAA;AACRC,IAAAA,QAAU,EAAAC,MAAA;AACVC,IAAAA,GAAK,EAAAL,MAAA;AACLM,IAAAA,QAAU,EAAAF,MAAA;AACVG,IAAAA,OAAA,EAAS,CAACC,QAAA,EAAUN,MAAM,CAAA;AAC1BO,IAAAA,cAAgB,EAAAT,MAAA;AAChBU,IAAAA,gBAAA,EAAkB,CAACF,QAAA,EAAUN,MAAM,CAAA;AACnCS,IAAAA,QAAU,EAAA;AACRC,MAAAA,IAAM,EAAAC,OAAA;AACN,MAAA,SAAA,EAAS,KAAA,CAAA;KACX;AACAC,IAAAA,QAAU,EAAA;AACRF,MAAAA,IAAM,EAAAC,OAAAA;KACR;AACAE,IAAAA,MAAQ,EAAA;AACNH,MAAAA,IAAM,EAAAI,KAAA;AACN,MAAA,SAAA,EAAS,KAAA,CAAA;KACX;AACAC,IAAAA,QAAU,EAAAT,QAAA;AACVU,IAAAA,UAAY,EAAAV,QAAA;AACZW,IAAAA,YAAc,EAAAX,QAAA;AACdY,IAAAA,gBAAkB,EAAAZ,QAAAA;GACpB;EAEAa,KAAA,EAAO,CAAC,oBAAoB,CAAA;AAE5BC,EAAAA,KAAA,WAAAA,KAAAA,CAAMxB,OAA0ByB,OAAuB,EAAA;AAAA,IAAA,IAAAC,eAAA,CAAA;AACrD,IAAA,IAAAC,OAAA,GAAqBC,OAAO5B,KAAK,CAAA;MAAzBC,GAAA,GAAA0B,OAAA,CAAA1B,GAAA;MAAKM,GAAI,GAAAoB,OAAA,CAAJpB,GAAI,CAAA;AAEX,IAAuBsB,IAAI,IAAI,EAAA;IACrC,IAAMC,iBAAiBC,QAAS,CAAA,YAAA;AAAA,MAAA,IAAAC,eAAA,CAAA;AAAA,MAAA,OAAA,CAAAA,eAAA,GAAMzB,GAAI,CAAA0B,KAAA,CAAMC,wDAAVF,eAAA,CAAgBG,YAAY,CAAA;KAAA,CAAA,CAAA;IAC5D,IAAAC,MAAA,GAASP,IAAIC,cAAe,CAAAG,KAAA,KAAAP,CAAAA,eAAA,GAAS1B,MAAMO,GAAI,CAAA2B,IAAA,MAAA,IAAA,IAAAR,eAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAVA,eAAA,CAAgBW,gBAAmB,IAAA,KAAK,CAAA,CAAA;AACnF,IAAA,IAAMC,YAAYT,GAAI,EAAA,CAAA;AACtB,IAAA,IAAMU,YAAYV,GAAyB,EAAA,CAAA;AAC3C,IAAA,IAAMW,cAAcC,cAAe,EAAA,CAAA;IAEnC,IAAAC,cAAA,GAAsBC,cAAc;AAAEC,QAAAA,SAAA,EAAWC,SAAAA;AAAY,OAAC,CAAA;MAAtDD,WAAU,GAAAF,cAAA,CAAVE,SAAU,CAAA;AAEZ,IAAA,IAAAE,qBAAA,GAA6F,SAA7FA,qBAAAA,CAA8FC,GAAQ,EAAA;AAC1G,MAAA,IAAIC,OAAA,CAAOD,GAAQ,CAAA,KAAA,QAAA,KAAa,UAAc,IAAAA,GAAA,IAAOA,IAAIE,kBAAqB,CAAA,EAAA;AACtE,QAAA,IAAAC,QAAA,GAAWH,IAAIE,kBAAqB,GAAAE,GAAA,CAAIlD,IAAIgC,KAAO,EAAAjC,KAAA,CAAMG,MAAM,CAAA,GAAI4C,GAAI,CAAAG,QAAA,CAAA;QAC7E,OAAOH,GAAI,CAAAG,QAAA,CAAA;QACX,OAAOH,GAAI,CAAAE,kBAAA,CAAA;AACXxB,QAAAA,OAAA,CAAQ2B,IAAK,CAAA,oBAAA,EAAsBF,QAAU,EAAAjD,GAAA,CAAIgC,OAAOc,GAAG,CAAA,CAAA;AAC7D,OAAO,MAAA;QACLT,SAAA,CAAUL,KAAQ,GAAAc,GAAA,CAAA;AACpB,OAAA;KACF,CAAA;AAEAM,IAAAA,KAAA,CAAM,CAACvB,cAAc,CAAG,EAAA,UAACwB,GAAQ,EAAA;AAC/B,MAAA,IAAIA,GAAK,EAAA;QACPlB,MAAA,CAAOH,KAAQ,GAAA,IAAA,CAAA;AACjB,OAAA;AACF,KAAC,CAAA,CAAA;AAEK,IAAA,IAAAsB,eAAA,GAAkBxB,SAAS,YAAM;MAAA,IAAAyB,gBAAA,EAAAC,mBAAA,CAAA;AACrC,MAAA,OAAO,CAAAD,CAAAA,gBAAA,GAAAjD,GAAI,CAAA0B,KAAA,CAAMC,IAAM,MAAAsB,IAAAA,IAAAA,gBAAA,KAAAC,KAAAA,CAAAA,IAAAA,CAAAA,mBAAA,GAAhBD,gBAAA,CAAgBE,EAAA,MAAA,IAAA,IAAAD,mBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAhBA,mBAAA,CAAAE,IAAA,CAAAH,gBAAA,EAAAI,aAAA,CAAAA,aAAA,CAA0BC,EAAAA,EAAAA,UAAA,CAAW5B,KAAO,CAAA,EAAA,EAAA,EAAA;QAAA6B,SAAA,EAAWC,UAAW,CAAA9B,KAAA;AAAOa,QAAAA,qBAAsB,EAAtBA,qBAAAA;OAAuB,CAAA,MAAK,EAAC,CAAA;AAC/G,KAAC,CAAA,CAAA;IAEK,IAAAe,UAAA,GAAa9B,SAAS,YAAA;MAAA,OAAO;QACjC1B,UAAUL,KAAM,CAAAK,QAAA;QAChBG,UAAUR,KAAM,CAAAQ,QAAA;QAChBD,KAAKP,KAAM,CAAAO,GAAA;QACXN,KAAKD,KAAM,CAAAC,GAAAA;OACX,CAAA;AAAA,KAAA,CAAA,CAAA;AAEI,IAAA,IAAA8D,UAAA,GAAahC,SAAS,YAAM;AAC1B,MAAA,IAAEiC,MAAO,GAAIzD,GAAI,CAAA0B,KAAA,CAAf+B,MAAO,CAAA;MAET,IAAAC,IAAA,GAA0BD,OAAOE,KAAM,CAAA,GAAG,KAAK,EAAC;QAAAC,KAAA,GAAAC,QAAA,CAAAH,IAAA,CAAA;AAA/CI,QAAAA;QAAaC,QAAQ,GAAAH,KAAA,CAAAI,KAAA,CAAA,CAAA,CAAA,CAAA;AAC5B,MAAA,IAAMC,MAAS,GAAAZ,aAAA,KAAK3D,GAAA,CAAIgC,KAAM,CAAA,CAAA;MAC9B,IAAIqC,SAASG,MAAQ,EAAA;AACnBD,QAAAA,MAAA,CAAOH,QAAY,CAAA,GAAAK,SAAA,CAAUzE,GAAI,CAAAgC,KAAA,CAAMoC,QAAS,CAAA,CAAA,CAAA;AAChDM,QAAAA,GAAA,CAAIH,OAAOH,QAAW,CAAA,EAAAC,QAAA,CAASM,KAAK,GAAG,CAAA,EAAGtC,UAAUL,KAAK,CAAA,CAAA;AAC3D,OAAO,MAAA;QACD0C,GAAA,CAAAH,MAAA,EAAQR,MAAQ,EAAA1B,SAAA,CAAUL,KAAK,CAAA,CAAA;AACrC,OAAA;AACO,MAAA,OAAAuC,MAAA,CAAA;AACT,KAAC,CAAA,CAAA;AAEK,IAAA,IAAAK,QAAA,GAAW9C,SAAS,YAAM;MAC9B,IAAM+C,IAAO,GAAAC,UAAA,CACX;QACE9E,KAAK8D,UAAW,CAAA9B,KAAA;AAChB1B,QAAAA,iCAAUA,EAAAA,EAAAA,IAAI0B,KAAO,CAAA,EAAA,EAAA,EAAA;UAAA+C,IAAA,EAAMhF,MAAMS,OAAAA;SAAQ,CAAA;QACzCJ,UAAUL,KAAM,CAAAK,QAAA;QAChBG,UAAUR,KAAM,CAAAQ,QAAAA;AAClB,OAAA,EACAiB,OAAQ,CAAAwD,KAAA,EACR;QAAErE,gBAAkB,EAAAZ,KAAA,CAAMY,gBAAAA;AAAiB,OAC7C,CAAA,CAAA;AACO,MAAA,OAAAkE,IAAA,CAAA;AACT,KAAC,CAAA,CAAA;AAEK,IAAA,IAAAI,SAAA,GAAYnD,SAAS,YAAM;AACzB,MAAA,IAAEG,IAAK,GAAI3B,GAAI,CAAA0B,KAAA,CAAbC,IAAK,CAAA;AACb,MAAA,OAAOiD,UAAW,CAAAjD,IAAA,CAAKlC,KAAK,CAAA,GACxBkC,KAAKlC,KAAM,CAAA4D,aAAA,CAAAA,aAAA,CACNC,EAAAA,EAAAA,UAAW,CAAA5B,KAAA,CAAA,EAAA,EAAA,EAAA;QACd6B,WAAWC,UAAW,CAAA9B,KAAA;AACtBa,QAAAA,qBAAA,EAAAA,qBAAAA;AAAA,OAAA,CACD,CAAA,GAAAc,aAAA,KACI1B,KAAKlC,KAAM,CAAA,CAAA;AACtB,KAAC,CAAA,CAAA;AAEK,IAAA,IAAAoF,cAAA,GAAiBrD,SAAS,YAAM;AAAA,MAAA,IAAAsD,qBAAA,CAAA;AAC9B,MAAA,IAAEnD,IAAK,GAAI3B,GAAI,CAAA0B,KAAA,CAAbC,IAAK,CAAA;AACb,MAAA,IAAI,CAACA,IAAA,EAAM,OAAO,EAAC,CAAA;AACnB,MAAA,IAAMoD,QAAW,GAAA1B,aAAA,KAAKsB,SAAA,CAAUjD,KAAM,CAAA,CAAA;MAEtC,OAAOqD,QAAS,CAAAnE,QAAA,CAAA;MAChB,OAAOmE,QAAS,CAAArD,KAAA,CAAA;AACX,MAAA,CAAAoD,qBAAA,GAAAnD,IAAA,CAAAqD,gBAAA,MAAAF,IAAAA,IAAAA,qBAAA,KAAAA,KAAAA,CAAAA,IAAAA,qBAAA,CAAkBG,OAAQ,CAAA,UAACC,IAAS,EAAA;QACvC,OAAOH,QAAS,CAAAG,IAAA,CAAA,CAAA;AAClB,OAAC,CAAA,CAAA;AACM,MAAA,OAAAH,QAAA,CAAA;AACT,KAAC,CAAA,CAAA;AAEK,IAAA,IAAAI,mBAAA,GAAsB3D,SAAS,YAAM;AAAA,MAAA,IAAA4D,sBAAA,CAAA;AACnC,MAAA,IAAEzD,IAAK,GAAI3B,GAAI,CAAA0B,KAAA,CAAbC,IAAK,CAAA;AACb,MAAA,IAAI,CAACA,IAAA,EAAa,OAAA,KAAA,CAAA;AAClB,MAAA,OAAOnB,OAAQ,CAAA4E,CAAAA,sBAAA,GAAAzD,IAAA,CAAKqD,gBAAkB,MAAA,IAAA,IAAAI,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAvBA,sBAAA,CAAuBC,QAAA,CAAS,UAAU,CAAC,CAAA,CAAA;AAC5D,KAAC,CAAA,CAAA;AAEK,IAAA,IAAAC,YAAA,GAAe,SAAfA,YAAAA,CAAgBC,OAAoE,EAAA;AACjF,MAAA,OAAA,IAAIC,OAAQ,CAAA,UAACC,OAAY,EAAA;AAC9B,QAAA,IAAMC,MAAuD,GAAA;UAC3DC,MAAQ,EAAA,CAAAtC,aAAA,CAAAA,aAAA,CAEDC,EAAAA,EAAAA,UAAW,CAAA5B,KAAA,CAAA,EAAA,EAAA,EAAA;AACdM,YAAAA,WAAW,EAAC;YACZN,OAAOK,SAAU,CAAAL,KAAAA;WAErB,CAAA,CAAA;AACA6D,UAAAA,OAAA,EAAAA,OAAAA;SACF,CAAA;AACA,QAAA,IAAMK,QAAQhB,UAAW,CAAA5E,GAAA,CAAI0B,KAAM,CAAAC,IAAA,CAAKiE,KAAK,CAAI,GAAA5F,GAAA,CAAI0B,KAAM,CAAAC,IAAA,CAAKiE,MAAMtC,UAAW,CAAA5B,KAAK,CAAI,GAAA1B,GAAA,CAAI0B,MAAMC,IAAK,CAAAiE,KAAA,CAAA;AACrG,QAAA,IAAA,CAAC5F,IAAI0B,KAAM,CAAAC,IAAA,IAAQ,CAACiE,KAAS,IAAA,CAACA,MAAM1B,MAAQ,EAAA;AAAA,UAAA,IAAA2B,iBAAA,CAAA;AAC9C,UAAA,CAAAA,iBAAA,GAAApG,KAAA,CAAMoB,8CAANgF,KAAAA,CAAAA,IAAAA,iBAAA,CAAAzC,IAAA,CAAA3D,KAAA,EAAmBiG,MAAM,CAAA,CAAA;UACzBD,OAAA,CAAQ,IAAI,CAAA,CAAA;AACZ,UAAA,OAAA;AACF,SAAA;AACAK,QAAAA,QAAA,CAAS/D,UAAUL,KAAO,EAAAkE,KAAK,CAAE,CAAAG,IAAA,CAAK,UAACJ,MAAW,EAAA;AAAA,UAAA,IAAAK,kBAAA,CAAA;UAChD,IAAMC,OAAON,MAAQ,KAARA,IAAAA,IAAAA,MAAQ,KAARA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAQ,CAAAO,MAAA,CAAO,UAACC,CAAM,EAAA;YAAA,OAAA,CAACA,EAAER,MAAM,CAAA;WAAA,CAAA,CAAA;UACrCD,MAAA,CAAAC,MAAA,CAAO,GAAG3D,SAAY,GAAAiE,IAAA,CAAA;AAC7B,UAAA,CAAAD,kBAAA,GAAAvG,KAAA,CAAMoB,+CAANmF,KAAAA,CAAAA,IAAAA,kBAAA,CAAA5C,IAAA,CAAA3D,KAAA,EAAmBiG,MAAM,CAAA,CAAA;AACzB,UAAA,IAAI,CAACO,IAAA,IAAQ,CAACA,IAAA,CAAK/B,MAAQ,EAAA;YACzBlC,SAAA,CAAUN,QAAQ,EAAC,CAAA;YACnB+D,OAAA,CAAQ,IAAI,CAAA,CAAA;AACd,WAAO,MAAA;YACLzD,SAAA,CAAUN,KAAQ,GAAAuE,IAAA,CAAA;YAClBR,OAAA,CAAQQ,IAAI,CAAA,CAAA;AACd,WAAA;AACF,SAAC,CAAA,CAAA;AACH,OAAC,CAAA,CAAA;KACH,CAAA;IAEM,IAAAG,MAAA,GAAS,SAATA,MAAAA,CAAUC,CAAA,EAAQC,CAAW,EAAA;MACjC,IAAIC,QAAS,CAAAF,CAAC,CAAK,IAAAE,QAAA,CAASD,CAAC,CAAG,EAAA;AAC9B,QAAA,OAAOE,KAAKC,SAAU,CAAAJ,CAAC,CAAM,KAAAG,IAAA,CAAKC,UAAUH,CAAC,CAAA,CAAA;AAC/C,OAAA;MACA,OAAOD,CAAM,KAAAC,CAAA,CAAA;KACf,CAAA;IAEA,IAAMI,kBAAqB,GAAA,SAArBA,kBAAqBA,CAACC,iBAA6B,EAAAC,SAAA,EAAoC;MAAA,KAAAC,IAAAA,IAAA,GAAAC,SAAA,CAAA5C,MAAA,EAAd6C,IAAc,OAAApG,KAAA,CAAAkG,IAAA,GAAAA,CAAAA,GAAAA,IAAA,WAAAG,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA,EAAA,EAAA;AAAdD,QAAAA,IAAc,CAAAC,IAAA,GAAAF,CAAAA,CAAAA,GAAAA,SAAA,CAAAE,IAAA,CAAA,CAAA;AAAA,OAAA;MAC3F1B,YAAA,CAAa,MAAM,CAAA,CAAES,IAAK,CAAA,UAACJ,MAAW,EAAA;QAAA,IAAAsB,qBAAA,EAAAC,sBAAA,CAAA;QACpC,IAAIvB,MAAW,KAAA,IAAA,EAAM,OAAA;AACrB,QAAA,IAAMwB,WAAWvE,GAAI,CAAAlD,GAAA,CAAIgC,KAAO,EAAA1B,GAAA,CAAI0B,MAAM+B,MAAM,CAAA,CAAA;QAEhD,IAAI,CAAC2C,MAAA,CAAOrE,SAAU,CAAAL,KAAA,EAAOyF,QAAQ,CAAG,EAAA;UACtCpF,SAAA,CAAUL,KAAQ,GAAAyF,QAAA,CAAA;UAClBR,iBAAA,KAAA,IAAA,IAAAA,iBAAA,KAAAA,KAAAA,CAAAA,IAAAA,iBAAA,CAAAS,KAAA,CAAA,KAAA,CAAA,EAAuBL,IAAI,CAAA,CAAA;AAC7B,SAAA;QACgB,CAAAE,qBAAA,IAAAC,sBAAA,GAAAlE,eAAA,CAAAtB,KAAA,EAAMkF,SAAa,CAAA,MAAA,IAAA,IAAAK,qBAAA,KAAnBA,KAAAA,CAAAA,IAAAA,qBAAA,CAAA7D,IAAA,CAAA8D,sBAAA,EAAmBH,IAAA,CAAK,CAAE,CAAA,CAAA,CAAA;AAEpC,QAAA,IAAAM,KAAA,GAAQC,WAAW,YAAM;AAAA,UAAA,IAAAC,qBAAA,CAAA;AACzB,UAAA,IAAA,CAAChG,eAAeG,KAAO,EAAA;YACzBG,MAAA,CAAOH,KAAQ,GAAA,KAAA,CAAA;AACjB,WAAA;UACAM,SAAA,CAAUN,QAAQ,EAAC,CAAA;UACnB,CAAA6F,qBAAA,GAAA9H,KAAA,CAAMsB,gBAAmB,MAAAwG,IAAAA,IAAAA,qBAAA,eAAzBA,qBAAA,CAAAnE,IAAA,CAAA3D,KAAA,EAAA4D,aAAA,CAAAA,aAAA,CACKC,EAAAA,EAAAA,UAAW,CAAA5B,KAAA,CAAA,EAAA,EAAA,EAAA;YACdA,OAAOK,SAAU,CAAAL,KAAA;YACjB6B,SAAA,EAAAF,aAAA,CAAAA,aAAA,KAAgB5D,KAAM,CAAAC,GAAA,CAAA8H,EAAAA,EAAAA,EAAAA,eAAA,KAAM/H,KAAM,CAAAO,GAAA,CAAIyD,MAAS,EAAA1B,SAAA,CAAUL,KAAM,CAAA,CAAA;AAC/D4D,YAAAA,YAAA,EAAAA,YAAA;AACAzD,YAAAA,MAAQ,EAAA,KAAA;AAAA,WAAA,CACT,CAAA,CAAA;UACD4F,YAAA,CAAaJ,KAAK,CAAA,CAAA;WACjB,CAAC,CAAA,CAAA;AACN,OAAC,CAAA,CAAA;KACH,CAAA;AAEM,IAAA,IAAAK,SAAA,GAAYlG,SAAsC,YAAM;AAAA,MAAA,IAAAmG,sBAAA,CAAA;AACtD,MAAA,IAAEhG,IAAK,GAAI3B,GAAI,CAAA0B,KAAA,CAAbC,IAAK,CAAA;AACP,MAAA,IAAAiG,cAAA,GAAiBnI,MAAMa,QAAa,KAAA,KAAA,CAAA,CAAA;AACtC,MAAA,IAAA,CAACuB,MAAO,CAAAH,KAAA,IAAS,CAACkG,cAAA,EAAgB,OAAA;AAClC,MAAA,IAAA,EAACjG,iBAAAA,6CAAAA,KAAMqD,gBAAkB,MAAA2C,IAAAA,IAAAA,sBAAA,eAAxBA,sBAAA,CAAwBzD,MAAA,CAAQ,EAAA,OAAO,EAAC,CAAA;MAE7C,IAAM2D,aAAuC,EAAC,CAAA;MAC9C,IAAMlB,oBAAoBhF,IAAM,KAAA,IAAA,IAANA,IAAM,KAANA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAM,CAAAmG,QAAA,CAAA;AAC3BnG,MAAAA,IAAA,CAAAqD,gBAAA,CAAiBC,OAAQ,CAAA,UAAC8C,SAAc,EAAA;QAC3C,IAAIA,SAAc,KAAA,UAAA,EAAY,OAAA;AACnBF,QAAAA,UAAA,CAAAE,SAAA,CAAA,GAAa,YAAkB;AAAA,UAAA,KAAA,IAAAC,KAAA,GAAAlB,SAAA,CAAA5C,MAAA,EAAd6C,IAAc,GAAApG,IAAAA,KAAA,CAAAqH,KAAA,GAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;AAAdlB,YAAAA,IAAc,CAAAkB,KAAA,CAAAnB,GAAAA,SAAA,CAAAmB,KAAA,CAAA,CAAA;AAAA,WAAA;AACxCvB,UAAAA,kBAAA,CAAAU,KAAA,CACET,KAAAA,CAAAA,EAAAA,CAAAA,iBAAA,EACAoB,SAAA,EAAA1E,aAAA,CAAAA,aAAA,CAEKC,EAAAA,EAAAA,UAAW,CAAA5B,KAAA,CAAA,EAAA,EAAA,EAAA;AACd6D,YAAAA,OAAS,EAAAwC,SAAA;YACTG,YAAY1E,UAAW,CAAA9B,KAAAA;AAAA,WAAA,CAAA,CAAA,CAAAyG,MAAA,CAEtBpB,IAAA,CACL,CAAA,CAAA;SACF,CAAA;AACF,OAAC,CAAA,CAAA;AAEM,MAAA,OAAAc,UAAA,CAAA;AACT,KAAC,CAAA,CAAA;AAGK,IAAA,IAAAO,YAAA,GAAe,SAAfA,YAAAA,CAAgBrF,GAAA,EAA2B;AAAA,MAAA,IAAAsF,gBAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,mBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA,CAAA;MAC/C5G,SAAA,CAAUL,KAAQ,GAAAqB,GAAA,CAAA;MAClB,IAAM2C,MAAS,GAAArC,aAAA,CAAAA,aAAA,CACVC,EAAAA,EAAAA,UAAW,CAAA5B,KAAA,CAAA,EAAA,EAAA,EAAA;AACdA,QAAAA,KAAO,EAAAqB,GAAA;AACPQ,QAAAA,SAAA,EAAAF,aAAA,CAAAA,aAAA,CAAA,EAAA,EAAgB5D,KAAA,CAAMC,8BAAMD,KAAA,CAAMO,GAAI,CAAAyD,MAAA,EAASV,GAAI,CAAA,CAAA;OACrD,CAAA,CAAA;MAAA,KAAA6F,IAAAA,KAAA,GAAA9B,SAAA,CAAA5C,MAAA,EANiC6C,IAAc,OAAApG,KAAA,CAAAiI,KAAA,GAAAA,CAAAA,GAAAA,KAAA,WAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;AAAd9B,QAAAA,IAAc,CAAA8B,KAAA,GAAA/B,CAAAA,CAAAA,GAAAA,SAAA,CAAA+B,KAAA,CAAA,CAAA;AAAA,OAAA;AAO/C,MAAA,CAAAR,gBAAA,GAAA1D,SAAA,CAAUjD,KAAO,MAAA,IAAA,IAAA2G,gBAAA,KAAA,KAAA,CAAA,IAAA,CAAAC,qBAAA,GAAjBD,gBAAA,CAAiBzH,QAAA,MAAA0H,IAAAA,IAAAA,qBAAA,KAAjBA,KAAAA,CAAAA,IAAAA,qBAAA,CAAAlF,IAAA,CAAAgE,KAAA,CAAAkB,qBAAA,EAAA,CAAAD,gBAAA,EAA4BtF,GAAK,CAAA,CAAAoF,MAAA,CAAGpB,IAAI,CAAA,CAAA,CAAA;AACxC,MAAA,CAAAwB,eAAA,GAAA9I,KAAA,CAAMmB,0CAAN2H,KAAAA,CAAAA,IAAAA,eAAA,CAAAnF,IAAA,CAAA3D,KAAA,EAAiBiG,MAAM,CAAA,CAAA;AACvB,MAAA,CAAA8C,mBAAA,GAAA/I,KAAA,CAAMqB,kDAAN0H,KAAAA,CAAAA,IAAAA,mBAAA,CAAApF,IAAA,CAAA3D,KAAA,EAAqBiG,MAAM,CAAA,CAAA;MACX,CAAA+C,sBAAA,GAAAzF,eAAA,CAAAtB,KAAA,MAAA+G,IAAAA,IAAAA,sBAAA,KAAAC,KAAAA,CAAAA,IAAAA,CAAAA,sBAAA,GAAAD,sBAAA,CAAO7H,2DAAP8H,sBAAA,CAAAtF,IAAA,CAAAqF,sBAAA,EAAkB/C,MAAM,CAAA,CAAA;AAClC,MAAA,IAAAkC,cAAA,GAAiBnI,MAAMa,QAAa,KAAA,KAAA,CAAA,CAAA;AACtC,MAAA,IAAAsH,cAAA,IAAkBzC,oBAAoBzD,KAAO,EAAA;AAAA,QAAA,IAAAoH,gBAAA,CAAA;AACzC,QAAA,IAAAnC,iBAAA,GAAA,CAAAmC,gBAAA,GAAoB9I,GAAI,CAAA0B,KAAA,CAAMC,IAAM,MAAAmH,IAAAA,IAAAA,gBAAA,KAAhBA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,gBAAA,CAAgBhB,QAAA,CAAA;AAC1CpB,QAAAA,kBAAA,CAAAU,KAAA,CACET,KAAAA,CAAAA,EAAAA,CAAAA,iBAAA,EACA,QAAA,EAAAtD,aAAA,CAAAA,aAAA,CAEKC,EAAAA,EAAAA,UAAW,CAAA5B,KAAA,CAAA,EAAA,EAAA,EAAA;AACd6D,UAAAA,OAAS,EAAA,UAAA;UACT2C,YAAY1E,UAAW,CAAA9B,KAAAA;AAAA,SAAA,CAAA,CAAA,CAAAyG,MAAA,CAEtBpB,IAAA,CACL,CAAA,CAAA;AACF,OAAA;AACA,MAAA,IAAI,EAAA4B,gBAAA,GAAA3I,GAAI,CAAA0B,KAAA,CAAMC,IAAM,MAAAgH,IAAAA,IAAAA,gBAAA,uBAAhBA,gBAAA,CAAgBI,eAAA,MAAoB,QAAU,EAAA;QAChDzD,YAAA,CAAa,MAAM,CAAA,CAAA;AACrB,OAAA;KACF,CAAA;AAEM,IAAA,IAAA0D,oBAAA,GAAuB,SAAvBA,oBAAAA,CAAwBC,CAAkB,EAAA;AAAA,MAAA,IAAAC,eAAA,CAAA;AAC9C,MAAA,IAAI,CAAClJ,GAAI,CAAA0B,KAAA,CAAMC,QAAQ,CAAC3B,GAAA,CAAI0B,MAAMC,IAAK,CAAAwH,SAAA,EAAW,OAAA;AAClD,MAAA,IAAI,CAACtH,MAAO,CAAAH,KAAA,EAAO,OAAA;MAEnB,IAAM0H,OAAO,CAAAF,CAAAA,eAAA,GAAAD,CAAE,CAAAI,YAAA,MAAA,IAAA,IAAAH,eAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAFA,eAAA,CAAA9F,IAAA,CAAA6F,CAAiB,CAAK,KAAAA,CAAA,CAAEG,QAAQ,EAAC,CAAA;AACxC,MAAA,IAAA7E,IAAA,GAAO6E,IAAK,CAAAE,IAAA,CAAK,UAAC/E,KAAAA,EAAAA;AAAAA,QAAAA,IAAAA,gBAAAA,CAAAA;AAAAA,QAAAA,OAAAA,CAAAA,gBAAAA,GAAsBA,KAAK,CAAAgF,SAAA,MAAA,IAAA,IAAAC,gBAAA,KAALjF,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,gBAAAA,CAAgBkF,QAAS,IAAAtB,MAAA,CAAGlG,WAAY,CAAAP,KAAA,oBAAsB,CAAC,CAAA;OAAA,CAAA,CAAA;AACzG,MAAA,IAAA6C,IAAA,EAAM,OAAA;MACJ,IAAAoC,iBAAA,GAAoB3G,GAAI,CAAA0B,KAAA,CAAMC,IAAK,CAAAmG,QAAA,CAAA;MACzCpB,kBAAA,CAAmBC,mBAAmB,EAAI,EAAAtD,aAAA,CAAAA,aAAA,CAAA,EAAA,EACrCC,UAAW,CAAA5B,KAAA,CAAA,EAAA,EAAA,EAAA;AACd6D,QAAAA,OAAS,EAAA,UAAA;QACT2C,YAAY1E,UAAW,CAAA9B,KAAAA;AAAA,OAAA,CACxB,CAAA,CAAA;KACH,CAAA;AAEA,IAAA,IAAMgI,YAAY,SAAZA,YAAkB;AAAA,MAAA,IAAAC,sBAAA,CAAA;MACtB,CAAAA,sBAAA,GAAAlK,KAAA,CAAMsB,gBAAmB,MAAA4I,IAAAA,IAAAA,sBAAA,eAAzBA,sBAAA,CAAAvG,IAAA,CAAA3D,KAAA,EAAA4D,aAAA,CAAAA,aAAA,CACKC,EAAAA,EAAAA,UAAW,CAAA5B,KAAA,CAAA,EAAA,EAAA,EAAA;QACdA,OAAOK,SAAU,CAAAL,KAAA;QACjB6B,WAAW9D,KAAM,CAAAC,GAAA;AACjBmC,QAAAA,MAAQ,EAAA,IAAA;AACRyD,QAAAA,YAAA,EAAAA,YAAAA;AAAA,OAAA,CACD,CAAA,CAAA;KACH,CAAA;AAEM,IAAA,IAAAsE,WAAA,GAAc,SAAdA,WAAAA,CAAeX,CAAkB,EAAA;MACrCpH,MAAA,CAAOH,KAAQ,GAAA,IAAA,CAAA;AACLgI,MAAAA,SAAA,EAAA,CAAA;MACVT,CAAA,CAAEY,eAAgB,EAAA,CAAA;KACpB,CAAA;AAEAC,IAAAA,SAAA,CAAU,YAAM;AAAA,MAAA,IAAAC,gBAAA,CAAA;AACV,MAAA,IAAA,CAAAA,gBAAA,GAAAtK,KAAA,CAAMO,GAAI,CAAA2B,IAAA,MAAA,IAAA,IAAAoI,gBAAA,KAAA,KAAA,CAAA,IAAVA,gBAAA,CAAgBjI,eAAiB,EAAA;AACzB4H,QAAAA,SAAA,EAAA,CAAA;AACZ,OAAA;AACF,KAAC,CAAA,CAAA;IAEK,IAAAM,SAAA,GAAYxI,SAAS,YAAA;MAAA,OAAMoB,GAAA,CAAIlD,IAAIgC,KAAO,EAAA1B,GAAA,CAAI0B,KAAM,CAAA+B,MAAM,CAAC,CAAA;KAAA,CAAA,CAAA;AAEjEX,IAAAA,KAAA,CACEkH,SAAA,EACA,UAACA,UAAc,EAAA;MACbjI,SAAA,CAAUL,KAAQsI,GAAAA,UAAAA,CAAAA;AACpB,KAAA,EACA;AAAEC,MAAAA,WAAW,IAAA;AAAK,KACpB,CAAA,CAAA;AAEAnH,IAAAA,KAAA,CACEjB,MAAA,EACA,UAACA,OAAW,EAAA;AACJ,MAAA,IAAA+F,cAAA,GAAiBnI,MAAMa,QAAa,KAAA,KAAA,CAAA,CAAA;AACtC,MAAA,IAAA,CAACN,IAAI0B,KAAM,CAAAC,IAAA,IAAQ,CAAC3B,GAAI,CAAA0B,KAAA,CAAMC,IAAK,CAAAwH,SAAA,IAAa,CAACvB,cAAA,EAAgB,OAAA;AACrE,MAAA,IAAI/F,OAAQ,EAAA;AACPsB,QAAAA,EAAA,CAAA+G,QAAA,EAAU,SAASlB,oBAAoB,CAAA,CAAA;AAC5C,OAAO,MAAA;AACDmB,QAAAA,GAAA,CAAAD,QAAA,EAAU,SAASlB,oBAAoB,CAAA,CAAA;AAC7C,OAAA;AACF,KAAA,EACA;AAAEiB,MAAAA,WAAW,IAAA;AAAK,KACpB,CAAA,CAAA;AAEAnH,IAAAA,KAAA,CACE,YAAA;MAAA,OAAM,CAACrD,KAAA,CAAMa,QAAU,EAAAb,KAAA,CAAMC,GAAK,EAAAD,KAAA,CAAMO,GAAK,EAAAP,KAAA,CAAMK,QAAU,EAAAL,KAAA,CAAMQ,QAAQ,CAAA,CAAA;KAC3E,EAAA,UAAAmK,KAAA,EAA2B;AAAA,MAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAF,KAAA,EAAA,CAAA,CAAA;AAAzB9J,QAAAA,QAAQ,GAAA+J,KAAA,CAAA,CAAA,CAAA,CAAA;MAER,IAAI/J,aAAa,KAAO,EAAA;AACtByB,QAAAA,SAAA,CAAUL,QAAQsI,SAAU,CAAAtI,KAAA,CAAA;AAC9B,OAAA,MAAA,IAAWpB,aAAa,IAAM,EAAA;AAAA,QAAA,IAAAiK,oBAAA,CAAA;QAC5B,CAAAA,oBAAA,GAAA9K,KAAA,CAAMqB,YAAe,MAAAyJ,IAAAA,IAAAA,oBAAA,eAArBA,oBAAA,CAAAnH,IAAA,CAAA3D,KAAA,EAAA4D,aAAA,CAAAA,aAAA,CACKC,EAAAA,EAAAA,UAAW,CAAA5B,KAAA,CAAA,EAAA,EAAA,EAAA;UACdA,OAAOsI,SAAU,CAAAtI,KAAA;UACjB6B,WAAW7D,GAAI,CAAAgC,KAAAA;AAAA,SAAA,CAChB,CAAA,CAAA;AACH,OAAA;AACF,KAAA,EACA;AAAEuI,MAAAA,WAAW,IAAA;AAAK,KACpB,CAAA,CAAA;AAEAnH,IAAAA,KAAA,CACE,YAAA;MAAA,OAAMrD,KAAM,CAAAiB,MAAA,CAAA;KACZ,EAAA,UAACA,MAAW,EAAA;MACVsB,SAAA,CAAUN,KAAQ,GAAAhB,MAAA,CAAA;AACpB,KACF,CAAA,CAAA;AAEA,IAAA,OAAO,YAAM;AAAA,MAAA,IAAA8J,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,iBAAA,CAAA;MACX,IAAIlL,MAAMgB,QAAU,EAAA;QAClB,OAAO6D,QAAS,CAAA5C,KAAA,CAAA;AAClB,OAAA;AAEK,MAAA,IAAAjC,KAAA,CAAMa,aAAa,KAAa,CAAA,IAAA,CAACuB,OAAOH,KAAU,IAAAjC,KAAA,CAAMa,aAAa,KAAO,EAAA;AAAA,QAAA,IAAAsK,gBAAA,CAAA;AAC/E,QAAA,OAAAC,WAAA,CAAA,KAAA,EAAA;AAAA,UAAA,OAAA,EACcpL,KAAM,CAAAW,cAAA,CAAe0K;mBAAuBlB,EAAAA,WAAAA;SACrDtF,EAAAA,CAAAA,QAAS,CAAA5C,KAAA,EACT,CAAA,CAAAkJ,gBAAA,GAAA5K,IAAI0B,KAAM,CAAAC,IAAA,cAAAiJ,gBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAVA,gBAAA,CAAgBG,YAAiB,MAAA,KAAA,IAAAF,WAAA,CAAAxI,WAAA,EAAoB,IAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAG5D,OAAA;AACM,MAAA,IAAA2I,SAAA,GAAA,CAAAR,gBAAA,GAAYxK,GAAI,CAAA0B,KAAA,CAAMC,IAAM,MAAA6I,IAAAA,IAAAA,gBAAA,KAAhBA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,gBAAA,CAAgBrB,SAAA,CAAA;MAClC,IAAI,CAAC6B,SAAW,EAAA;AACVC,QAAAA,GAAA,CAAAC,KAAA,CAAM,SAAS,6BAA6B,CAAA,CAAA;AACzC,QAAA,OAAA,IAAA,CAAA;AACT,OAAA;MACM,IAAAC,YAAA,IAAAV,gBAAA,GAAezI,SAAU,CAAAN,KAAA,cAAA+I,gBAAA,KAAA,KAAA,CAAA,IAAA,CAAAA,gBAAA,GAAVA,gBAAA,CAAkB,CAAI,CAAA,cAAAA,gBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAtBA,gBAAA,CAAsBW,OAAA,CAAA;AAC3C,MAAA,IAAMC,kBAAqB,GAAAhI,aAAA,KAAKL,eAAA,CAAgBtB,KAAM,CAAA,CAAA;MACtD,OAAO2J,kBAAmB,CAAAzK,QAAA,CAAA;MAE1B,IAAA8J,CAAAA,gBAAA,GAAI1K,GAAI,CAAA0B,KAAA,CAAMC,IAAM,cAAA+I,gBAAA,KAAA,KAAA,CAAA,IAAA,CAAAA,gBAAA,GAAhBA,gBAAA,CAAgB1F,gBAAA,MAAA,IAAA,IAAA0F,gBAAA,KAAhBA,KAAAA,CAAAA,IAAAA,gBAAA,CAAkCxG,MAAQ,EAAA;QAC5ClE,GAAA,CAAI0B,KAAM,CAAAC,IAAA,CAAKqD,gBAAiB,CAAAC,OAAA,CAAQ,UAACqG,WAAgB,EAAA;AACvD,UAAA,IAAID,mBAAmBC,WAAc,CAAA,EAAA;YACnC,OAAOD,kBAAmB,CAAAC,WAAA,CAAA,CAAA;AAC5B,WAAA;AACF,SAAC,CAAA,CAAA;AACH,OAAA;AAEE,MAAA,OAAAT,WAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EACSpL,KAAA,CAAMW,eAAemL,YAC5B;QAAA,SAAS,EAAA,SAAAC,OAACvC,CAAAA,CAAkB,EAAA;UAC1BA,CAAA,CAAEY,eAAgB,EAAA,CAAA;SACpB;AAAA,QAAA,KAAA,EAAA,sBAAA;AAAA,OAAA,EAAA,CAAAgB,WAAA,CAAAG,SAAA,EAAAS,UAAA,CAAA;QAAA,QAIUN,EAAAA,YAAA,GAAe,CAAA,CAAAR,iBAAA,GAAA3I,SAAU,CAAAN,KAAA,MAAA,IAAA,IAAAiJ,iBAAA,KAAA,KAAA,CAAA,IAAA,CAAAA,iBAAA,GAAVA,iBAAA,CAAkB,CAAI,CAAA,MAAAA,IAAAA,IAAAA,iBAAA,KAAtBA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,iBAAA,CAAsBpK,IAAA,KAAQ,OAAU,GAAA,KAAA,CAAA;QAAA,MACzD4K,EAAAA,YAAAA;OACFtG,EAAAA,cAAA,CAAenD,KACf,EAAAgG,SAAA,CAAUhG,KACV,EAAA2J,kBAAA,EAAA;QAAA,OACGtJ,EAAAA,SAAA,CAAUL,KACjB;QAAA,UAAU0G,EAAAA,YAAAA;AACZ,OAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;KAGN,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}